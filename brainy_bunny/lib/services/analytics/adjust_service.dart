// lib/services/analytics/adjust_service.dart
import 'package:adjust_sdk/adjust.dart';
import 'package:adjust_sdk/adjust_attribution.dart';
import 'package:adjust_sdk/adjust_config.dart';
import 'package:adjust_sdk/adjust_event.dart';
import 'package:adjust_sdk/adjust_session_failure.dart';
import 'package:adjust_sdk/adjust_session_success.dart';
import 'package:flutter/foundation.dart';
import 'package:brainy_bunny/constants/app_constants.dart';

/// Service for Adjust SDK integration
/// Handles attribution tracking, event tracking, and ADID retrieval
class AdjustService {
  // Singleton pattern
  static final AdjustService _instance = AdjustService._internal();
  static AdjustService get instance => _instance;
  AdjustService._internal();

  bool _isInitialized = false;
  String? _adid;

  bool get isInitialized => _isInitialized;
  String? get adid => _adid;

  /// Initialize Adjust SDK
  Future<void> initialize() async {
    if (_isInitialized) {
      if (kDebugMode) {
        print('⚠️ Adjust SDK already initialized');
      }
      return;
    }

    try {
      if (kDebugMode) {
        print('📊 Initializing Adjust SDK...');
        print('   App Token: ${AppConstants.ADJUST_APP_TOKEN}');
      }

      // Create Adjust config
      AdjustConfig config = AdjustConfig(
        AppConstants.ADJUST_APP_TOKEN,
        kReleaseMode ? AdjustEnvironment.production : AdjustEnvironment.sandbox,
      );

      // Set log level for debugging
      config.logLevel = kDebugMode ? AdjustLogLevel.verbose : AdjustLogLevel.info;

      // Enable event buffering for better performance
      config.eventBufferingEnabled = true;

      // Set attribution callback
      config.attributionCallback = (AdjustAttribution attribution) {
        if (kDebugMode) {
          print('📊 Adjust Attribution Received:');
          print('   Tracker Token: ${attribution.trackerToken}');
          print('   Tracker Name: ${attribution.trackerName}');
          print('   Network: ${attribution.network}');
          print('   Campaign: ${attribution.campaign}');
          print('   Adgroup: ${attribution.adgroup}');
          print('   Creative: ${attribution.creative}');
        }
      };

      // Set session success callback
      config.sessionSuccessCallback = (AdjustSessionSuccess sessionSuccess) {
        if (kDebugMode) {
          print('✅ Adjust Session Success');
        }
      };

      // Set session failure callback
      config.sessionFailureCallback = (AdjustSessionFailure sessionFailure) {
        if (kDebugMode) {
          print('❌ Adjust Session Failure: ${sessionFailure.message}');
        }
      };

      // Initialize SDK
      Adjust.start(config);

      // Retrieve ADID
      _adid = await Adjust.getAdid();

      _isInitialized = true;

      if (kDebugMode) {
        print('✅ Adjust SDK initialized successfully');
        print('   ADID: $_adid');
      }
    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('❌ Failed to initialize Adjust SDK: $e');
        print('Stack trace: $stackTrace');
      }
      // Don't rethrow - app should work without analytics
    }
  }

  /// Track an event
  /// [eventToken] - The event token from Adjust dashboard
  /// [parameters] - Optional event parameters
  Future<void> trackEvent(String eventToken, {Map<String, String>? parameters}) async {
    if (!_isInitialized) {
      if (kDebugMode) {
        print('⚠️ Adjust SDK not initialized, skipping event: $eventToken');
      }
      return;
    }

    try {
      AdjustEvent event = AdjustEvent(eventToken);

      // Add callback parameters
      if (parameters != null) {
        parameters.forEach((key, value) {
          event.addCallbackParameter(key, value);
        });
      }

      // Track event
      Adjust.trackEvent(event);

      if (kDebugMode) {
        print('📊 Adjust Event Tracked: $eventToken');
        if (parameters != null && parameters.isNotEmpty) {
          print('   Parameters: $parameters');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to track Adjust event: $e');
      }
      // Log to local storage as fallback
      _logEventLocally(eventToken, parameters);
    }
  }

  /// Track revenue event (for purchases)
  /// [eventToken] - The event token from Adjust dashboard
  /// [revenue] - The revenue amount
  /// [currency] - Currency code (e.g., 'EUR', 'USD')
  /// [parameters] - Optional event parameters
  Future<void> trackRevenue(
    String eventToken, {
    required double revenue,
    required String currency,
    Map<String, String>? parameters,
  }) async {
    if (!_isInitialized) {
      if (kDebugMode) {
        print('⚠️ Adjust SDK not initialized, skipping revenue event: $eventToken');
      }
      return;
    }

    try {
      AdjustEvent event = AdjustEvent(eventToken);

      // Set revenue
      event.setRevenue(revenue, currency);

      // Add callback parameters
      if (parameters != null) {
        parameters.forEach((key, value) {
          event.addCallbackParameter(key, value);
        });
      }

      // Track event
      Adjust.trackEvent(event);

      if (kDebugMode) {
        print('💰 Adjust Revenue Event Tracked: $eventToken');
        print('   Revenue: $revenue $currency');
        if (parameters != null && parameters.isNotEmpty) {
          print('   Parameters: $parameters');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to track Adjust revenue event: $e');
      }
      // Log to local storage as fallback
      _logEventLocally(eventToken, parameters);
    }
  }

  /// Retrieve the current ADID
  Future<String?> getAdid() async {
    try {
      _adid = await Adjust.getAdid();
      return _adid;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to retrieve ADID: $e');
      }
      return null;
    }
  }

  /// Enable or disable the SDK
  void setEnabled(bool enabled) {
    if (!_isInitialized) return;

    try {
      Adjust.setEnabled(enabled);
      if (kDebugMode) {
        print('📊 Adjust SDK ${enabled ? 'enabled' : 'disabled'}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to set Adjust enabled state: $e');
      }
    }
  }

  /// Local event logging fallback
  void _logEventLocally(String eventToken, Map<String, String>? parameters) {
    if (kDebugMode) {
      print('📝 Logged event locally for later upload: $eventToken');
      // TODO: Implement local storage and batch upload mechanism
    }
  }
}

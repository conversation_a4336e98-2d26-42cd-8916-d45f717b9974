// lib/onboarding/widgets/demo_game_wrapper.dart
import 'package:flutter/material.dart';
import '../../flutter_gen/gen_l10n/app_localizations.dart';
import 'package:brainy_bunny/constants/onboarding_constants.dart';

/// Wrapper for demo games shown during onboarding
/// Displays educational context, progress, and celebration
class DemoGameWrapper extends StatefulWidget {
  final int gameIndex; // 0-4 for the 5 demo games
  final Widget gameWidget; // The actual game component
  final VoidCallback onComplete;

  const DemoGameWrapper({
    super.key,
    required this.gameIndex,
    required this.gameWidget,
    required this.onComplete,
  });

  @override
  State<DemoGameWrapper> createState() => DemoGameWrapperState();
}

// Export state class so it can be accessed via GlobalKey
class DemoGameWrapperState extends State<DemoGameWrapper>
    with SingleTickerProviderStateMixin {
  bool _isContextExpanded = true;
  bool _gameCompleted = false;
  int _matchesCompleted = 0;
  late AnimationController _celebrationController;
  late Animation<double> _celebrationScale;
  late Animation<double> _celebrationFade;

  @override
  void initState() {
    super.initState();

    _celebrationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _celebrationScale = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _celebrationController,
      curve: Curves.elasticOut,
    ));

    _celebrationFade = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _celebrationController,
      curve: const Interval(0.0, 0.3),
    ));
  }

  @override
  void dispose() {
    _celebrationController.dispose();
    super.dispose();
  }

  void _toggleContext() {
    setState(() {
      _isContextExpanded = !_isContextExpanded;
    });
  }

  // Called by child game when a match is completed
  void notifyMatchCompleted() {
    if (!mounted) return;

    setState(() {
      _matchesCompleted++;
    });

    if (_matchesCompleted >= OnboardingConstants.DEMO_GAME_MATCHES) {
      _completeGame();
    }
  }

  // Called by child game when all matches are done
  void notifyGameCompleted() {
    if (!mounted) return;
    _completeGame();
  }

  void _completeGame() {
    setState(() {
      _gameCompleted = true;
    });
    _celebrationController.forward();
  }

  String _getEducationalContext() {
    final l10n = AppLocalizations.of(context)!;
    switch (widget.gameIndex) {
      case 0:
        return l10n.demo_game_1_context;
      case 1:
        return l10n.demo_game_2_context;
      case 2:
        return l10n.demo_game_3_context;
      case 3:
        return l10n.demo_game_4_context;
      case 4:
        return l10n.demo_game_5_context;
      default:
        return '';
    }
  }

  String _getGameTitle() {
    final l10n = AppLocalizations.of(context)!;
    switch (widget.gameIndex) {
      case 0:
        return l10n.demo_game_1_title;
      case 1:
        return l10n.demo_game_2_title;
      case 2:
        return l10n.demo_game_3_title;
      case 3:
        return l10n.demo_game_4_title;
      case 4:
        return l10n.demo_game_5_title;
      default:
        return '';
    }
  }

  String _getScience() {
    final l10n = AppLocalizations.of(context)!;
    switch (widget.gameIndex) {
      case 0:
        return l10n.demo_game_1_science;
      case 1:
        return l10n.demo_game_2_science;
      case 2:
        return l10n.demo_game_3_science;
      case 3:
        return l10n.demo_game_4_science;
      case 4:
        return l10n.demo_game_5_science;
      default:
        return '';
    }
  }

  String _getBadge() {
    return OnboardingConstants.DEMO_GAME_BADGES[widget.gameIndex];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Stack(
          children: [
            // Game widget (always present)
            widget.gameWidget,

            // Fullscreen educational panel (initially shown, disappears when collapsed)
            if (_isContextExpanded)
              AnimatedPositioned(
                duration: const Duration(milliseconds: 400),
                curve: Curves.easeInOut,
                top: 0,
                bottom: 0,
                left: 0,
                right: 0,
                child: Material(
                  color: Colors.white,
                  elevation: 8,
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.blue.shade50,
                          Colors.white,
                        ],
                      ),
                    ),
                      child: Column(
                        children: [
                          // Close button
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                '${widget.gameIndex + 1}/3',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.blue.shade700,
                                ),
                              ),
                              IconButton(
                                icon: const Icon(
                                  Icons.close,
                                  size: 28,
                                ),
                                onPressed: _toggleContext,
                                color: Colors.grey.shade700,
                              ),
                            ],
                          ),

                          const SizedBox(height: 24),

                          // Progress indicator
                          LinearProgressIndicator(
                            value: (widget.gameIndex + 1) / 3,
                            backgroundColor: Colors.blue.shade100,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.blue.shade600,
                            ),
                            minHeight: 8,
                            borderRadius: BorderRadius.circular(4),
                          ),

                          const SizedBox(height: 32),

                          // Game title
                          Text(
                            _getGameTitle(),
                            style: TextStyle(
                              fontSize: 28,
                              fontWeight: FontWeight.bold,
                              color: Colors.blue.shade900,
                            ),
                            textAlign: TextAlign.center,
                          ),

                          const SizedBox(height: 24),

                          // Educational context
                          Expanded(
                            child: SingleChildScrollView(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    _getEducationalContext(),
                                    style: TextStyle(
                                      fontSize: 18,
                                      color: Colors.grey.shade800,
                                      height: 1.6,
                                    ),
                                  ),

                                  const SizedBox(height: 24),

                                  // Scientific backing
                                  Container(
                                    padding: const EdgeInsets.all(16),
                                    decoration: BoxDecoration(
                                      color: Colors.blue.shade100,
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Row(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Icon(
                                          Icons.science,
                                          size: 24,
                                          color: Colors.blue.shade600,
                                        ),
                                        const SizedBox(width: 12),
                                        Expanded(
                                          child: Text(
                                            _getScience(),
                                            style: TextStyle(
                                              fontSize: 14,
                                              color: Colors.grey.shade700,
                                              fontStyle: FontStyle.italic,
                                              height: 1.4,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),

                                  const SizedBox(height: 32),

                                  // Match progress
                                  Text(
                                    'Complete ${OnboardingConstants.DEMO_GAME_MATCHES} matches:',
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                      color: Colors.grey.shade800,
                                    ),
                                  ),
                                  const SizedBox(height: 12),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: List.generate(
                                      OnboardingConstants.DEMO_GAME_MATCHES,
                                      (index) {
                                        final isCompleted = index < _matchesCompleted;
                                        return Padding(
                                          padding: const EdgeInsets.symmetric(horizontal: 8),
                                          child: AnimatedContainer(
                                            duration: const Duration(milliseconds: 300),
                                            width: 48,
                                            height: 48,
                                            decoration: BoxDecoration(
                                              shape: BoxShape.circle,
                                              color: isCompleted
                                                  ? Colors.green.shade600
                                                  : Colors.grey.shade300,
                                              border: Border.all(
                                                color: isCompleted
                                                    ? Colors.green.shade700
                                                    : Colors.grey.shade400,
                                                width: 3,
                                              ),
                                            ),
                                            child: isCompleted
                                                ? const Icon(
                                                    Icons.check,
                                                    size: 28,
                                                    color: Colors.white,
                                                  )
                                                : Center(
                                                    child: Text(
                                                      '${index + 1}',
                                                      style: TextStyle(
                                                        fontSize: 18,
                                                        fontWeight: FontWeight.bold,
                                                        color: Colors.grey.shade600,
                                                      ),
                                                    ),
                                                  ),
                                          ),
                                        );
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),

                          const SizedBox(height: 24),

                          // Start game button
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton(
                              onPressed: _toggleContext,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.blue.shade600,
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(vertical: 20),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(16),
                                ),
                                elevation: 4,
                              ),
                              child: const Text(
                                'Start Game',
                                style: TextStyle(
                                  fontSize: 20,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

            // Celebration overlay
            if (_gameCompleted)
              AnimatedBuilder(
                animation: _celebrationController,
                builder: (context, child) {
                  return Positioned.fill(
                    child: Container(
                      color: Colors.black.withValues(
                        alpha: 0.7 * _celebrationFade.value,
                      ),
                      child: Center(
                        child: Transform.scale(
                          scale: _celebrationScale.value,
                          child: Container(
                            padding: const EdgeInsets.all(32),
                            margin: const EdgeInsets.symmetric(horizontal: 32),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(24),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.3),
                                  blurRadius: 20,
                                  offset: const Offset(0, 10),
                                ),
                              ],
                            ),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                // Trophy icon
                                Container(
                                  width: 80,
                                  height: 80,
                                  decoration: BoxDecoration(
                                    color: Colors.amber.shade50,
                                    shape: BoxShape.circle,
                                  ),
                                  child: Icon(
                                    Icons.emoji_events,
                                    size: 48,
                                    color: Colors.amber.shade600,
                                  ),
                                ),

                                const SizedBox(height: 24),

                                // Congratulations text
                                Text(
                                  AppLocalizations.of(context)!
                                      .demo_game_congratulations,
                                  style: const TextStyle(
                                    fontSize: 24,
                                    fontWeight: FontWeight.bold,
                                    color: Color(0xFF1E3A5F),
                                  ),
                                  textAlign: TextAlign.center,
                                ),

                                const SizedBox(height: 16),

                                // Badge earned
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 20,
                                    vertical: 12,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.purple.shade50,
                                    borderRadius: BorderRadius.circular(20),
                                    border: Border.all(
                                      color: Colors.purple.shade200,
                                      width: 2,
                                    ),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Icon(
                                        Icons.star,
                                        color: Colors.purple.shade600,
                                        size: 20,
                                      ),
                                      const SizedBox(width: 8),
                                      Text(
                                        _getBadge(),
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w600,
                                          color: Colors.purple.shade700,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),

                                const SizedBox(height: 32),

                                // Next Game button
                                SizedBox(
                                  width: double.infinity,
                                  child: ElevatedButton(
                                    onPressed: widget.onComplete,
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.blue.shade600,
                                      foregroundColor: Colors.white,
                                      padding: const EdgeInsets.symmetric(
                                        vertical: 16,
                                      ),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(16),
                                      ),
                                      elevation: 2,
                                    ),
                                    child: Text(
                                      widget.gameIndex < 2
                                          ? AppLocalizations.of(context)!
                                              .demo_game_next
                                          : AppLocalizations.of(context)!
                                              .continue_button,
                                      style: const TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }
}

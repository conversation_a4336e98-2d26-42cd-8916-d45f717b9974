// lib/onboarding/widgets/purchase_error_dialog.dart
import 'package:flutter/material.dart';

/// Error dialog shown when purchase cannot proceed
/// Usually due to internet connection or store issues
class PurchaseErrorDialog extends StatelessWidget {
  final VoidCallback onContinueWithFree;

  const PurchaseErrorDialog({
    super.key,
    required this.onContinueWithFree,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.all(32),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(24),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.3),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Stack(
          children: [
            Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Error icon
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.wifi_off,
                size: 48,
                color: Colors.orange.shade600,
              ),
            ),

            const SizedBox(height: 24),

            // Title
            const Text(
              'Connection Issue',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Color(0xFF1E3A5F),
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 16),

            // Message
            Text(
              'We\'re having trouble connecting to the store. This might be due to:\n\n'
              '• No internet connection\n'
              '• Store service temporarily unavailable\n'
              '• Network issues\n\n'
              'You can continue with 5 free games and try purchasing later.',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade700,
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 32),

            // Try again button (now primary action)
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue.shade600,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 2,
                ),
                child: const Text(
                  'Try Again Later',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ),
            // Skip button in top-right corner
            Positioned(
              top: 0,
              right: 0,
              child: TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  onContinueWithFree();
                },
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.all(8),
                ),
                child: Text(
                  'Skip',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>CADisableMinimumFrameDurationOnPhone</key>
		<true/>
		<key>CFBundleDevelopmentRegion</key>
		<string>$(DEVELOPMENT_LANGUAGE)</string>
		<key>CFBundleDisplayName</key>
		<string>Brainy Bunny</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>brainy_bunny</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(FLUTTER_BUILD_NAME)</string>
		<key>CFBundleSignature</key>
		<string>????</string>
		<key>CFBundleVersion</key>
		<string>$(FLUTTER_BUILD_NUMBER)</string>
		<key>LSRequiresIPhoneOS</key>
		<true/>
		<key>UIApplicationSupportsIndirectInputEvents</key>
		<true/>
		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UIRequiresFullScreen</key>
		<true/>
		<key>UIStatusBarHidden</key>
		<true/>
		<key>UIStatusBarStyle</key>
		<string></string>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UIViewControllerBasedStatusBarAppearance</key>
		<false/>
		<key>NSAdvertisingAttributionReportEndpoint</key>
		<string>https://skan-rocapine.com</string>
		<key>SKAdNetworkItems</key>
		<array>
			<!-- Adjust SKAdNetwork IDs -->
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>4fzdc2evr5.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>hs6bdukanm.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>v72qych5uu.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>ludvb6z3bs.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>c6k4g5qg8m.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>cstr6suwn9.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>4468km3ulz.skadnetwork</string>
			</dict>
			<dict>
				<key>SKAdNetworkIdentifier</key>
				<string>2u9pt9hc89.skadnetwork</string>
			</dict>
		</array>
	</dict>
</plist>

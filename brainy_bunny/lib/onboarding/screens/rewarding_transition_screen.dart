// lib/onboarding/screens/rewarding_transition_screen.dart
import 'package:flutter/material.dart';
import 'package:brainy_bunny/models/onboarding_state.dart';
import 'dart:async';
import 'dart:math' as math;

/// Rewarding Transition Screen - Screen 11
/// Thanks user, creates anticipation for personalized results
class RewardingTransitionScreen extends StatefulWidget {
  final OnboardingState state;
  final VoidCallback onComplete;

  const RewardingTransitionScreen({
    super.key,
    required this.state,
    required this.onComplete,
  });

  @override
  State<RewardingTransitionScreen> createState() =>
      _RewardingTransitionScreenState();
}

class _RewardingTransitionScreenState extends State<RewardingTransitionScreen>
    with TickerProviderStateMixin {
  late AnimationController _bounceController;
  late AnimationController _confettiController;
  late AnimationController _progressController;
  late AnimationController _textRotationController;

  late Animation<double> _bounceAnimation;
  late Animation<double> _progressAnimation;

  Timer? _autoAdvanceTimer;
  int _currentTextIndex = 0;
  final List<String> _rotatingTexts = [
    'Finding age-appropriate games...',
    'Personalizing learning path...',
    'Almost ready...',
  ];

  final List<ConfettiParticle> _confettiParticles = [];

  @override
  void initState() {
    super.initState();

    // Bounce animation
    _bounceController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _bounceAnimation = CurvedAnimation(
      parent: _bounceController,
      curve: Curves.elasticOut,
    );

    _bounceController.forward();

    // Confetti animation
    _confettiController = AnimationController(
      duration: const Duration(milliseconds: 3000),
      vsync: this,
    );

    _initializeConfetti();
    _confettiController.forward();

    // Progress bar animation
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 2500),
      vsync: this,
    );

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeOut,
    ));

    _progressController.forward();

    // Text rotation animation
    _textRotationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _startTextRotation();

    // Auto-advance after 3 seconds
    _autoAdvanceTimer = Timer(const Duration(milliseconds: 3000), () {
      if (mounted) {
        widget.onComplete();
      }
    });
  }

  void _initializeConfetti() {
    final random = math.Random();
    final colors = [
      const Color(0xFF4A90E2),
      const Color(0xFFFFE45C),
      const Color(0xFF90EE90),
      const Color(0xFFFFA500),
    ];

    for (int i = 0; i < 25; i++) {
      _confettiParticles.add(
        ConfettiParticle(
          x: random.nextDouble(),
          initialY: -0.1,
          color: colors[random.nextInt(colors.length)],
          size: 8.0 + random.nextDouble() * 4.0,
          rotation: random.nextDouble() * 2 * math.pi,
          fallSpeed: 0.3 + random.nextDouble() * 0.4,
          sway: (random.nextDouble() - 0.5) * 0.1,
        ),
      );
    }
  }

  void _startTextRotation() {
    Timer.periodic(const Duration(milliseconds: 800), (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }

      setState(() {
        _currentTextIndex = (_currentTextIndex + 1) % _rotatingTexts.length;
      });

      _textRotationController.forward(from: 0.0);
    });
  }

  @override
  void dispose() {
    _autoAdvanceTimer?.cancel();
    _bounceController.dispose();
    _confettiController.dispose();
    _progressController.dispose();
    _textRotationController.dispose();
    super.dispose();
  }

  String _getPersonalizedMessage() {
    final childName = widget.state.parentName;
    final childAge = widget.state.childAge ?? 3;

    if (childName != null && childName.isNotEmpty) {
      return 'Creating ${childName}\'s personalized\nlearning journey...';
    } else {
      return 'Creating the perfect\nexperience for your\n$childAge-year-old...';
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              const Color(0xFFF0F8FF),
              Colors.white,
            ],
          ),
        ),
        child: SafeArea(
          child: Stack(
            children: [
              // Confetti particles
              ...widget.state.parentName != null
                  ? _buildConfetti(size)
                  : <Widget>[],

              // Main content
              Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Bunny mascot with bounce
                    ScaleTransition(
                      scale: _bounceAnimation,
                      child: Image.asset(
                        'assets/images/bunny_celebrates.png',
                        width: 180,
                        height: 180,
                        fit: BoxFit.contain,
                      ),
                    ),

                    const SizedBox(height: 32),

                    // Thank you message
                    const Padding(
                      padding: EdgeInsets.symmetric(horizontal: 32.0),
                      child: Text(
                        'Thank you for sharing!',
                        style: TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF333333),
                          height: 1.3,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Personalized message
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 32.0),
                      child: Text(
                        _getPersonalizedMessage(),
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey.shade700,
                          height: 1.5,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),

                    const SizedBox(height: 40),

                    // Progress bar
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 60.0),
                      child: AnimatedBuilder(
                        animation: _progressAnimation,
                        builder: (context, child) {
                          return ClipRRect(
                            borderRadius: BorderRadius.circular(4),
                            child: LinearProgressIndicator(
                              value: _progressAnimation.value,
                              minHeight: 8,
                              backgroundColor: Colors.grey.shade200,
                              valueColor: const AlwaysStoppedAnimation<Color>(
                                Color(0xFF4A90E2),
                              ),
                            ),
                          );
                        },
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Rotating text
                    SizedBox(
                      height: 40,
                      child: FadeTransition(
                        opacity: _textRotationController,
                        child: Text(
                          _rotatingTexts[_currentTextIndex],
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade600,
                            height: 1.4,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<Widget> _buildConfetti(Size size) {
    return _confettiParticles.map((particle) {
      return AnimatedBuilder(
        animation: _confettiController,
        builder: (context, child) {
          final progress = _confettiController.value;
          final y = particle.initialY + (progress * particle.fallSpeed * 1.2);
          final x = particle.x + (math.sin(progress * math.pi * 2) * particle.sway);

          if (y > 1.1) return const SizedBox.shrink();

          return Positioned(
            left: x * size.width,
            top: y * size.height,
            child: Transform.rotate(
              angle: particle.rotation + (progress * math.pi * 2),
              child: Container(
                width: particle.size,
                height: particle.size,
                decoration: BoxDecoration(
                  color: particle.color,
                  shape: BoxShape.circle,
                ),
              ),
            ),
          );
        },
      );
    }).toList();
  }
}

class ConfettiParticle {
  final double x;
  final double initialY;
  final Color color;
  final double size;
  final double rotation;
  final double fallSpeed;
  final double sway;

  ConfettiParticle({
    required this.x,
    required this.initialY,
    required this.color,
    required this.size,
    required this.rotation,
    required this.fallSpeed,
    required this.sway,
  });
}

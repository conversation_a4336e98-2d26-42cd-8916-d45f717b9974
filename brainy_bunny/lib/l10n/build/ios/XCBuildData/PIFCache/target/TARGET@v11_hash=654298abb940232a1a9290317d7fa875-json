{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988d1e71bcf9c3351e78ac5a1199e186fb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "MODULEMAP_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "in_app_purchase_storekit", "PRODUCT_NAME": "in_app_purchase_storekit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9892005d2921633428b21c2808d64855e7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98eab6df99bc7780ce5bfd17f241184e82", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "MODULEMAP_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "in_app_purchase_storekit", "PRODUCT_NAME": "in_app_purchase_storekit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98997e66c0e98e1db88e18272b5fafa165", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98eab6df99bc7780ce5bfd17f241184e82", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "MODULEMAP_FILE": "Target Support Files/in_app_purchase_storekit/in_app_purchase_storekit.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "in_app_purchase_storekit", "PRODUCT_NAME": "in_app_purchase_storekit", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a8402fb7425dde778441c192bedf25e0", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980d7b0c98545d7edf4acbbcca25cf7886", "guid": "bfdfe7dc352907fc980b868725387e98e300db694d3b145abd9c294e1038b740", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98feb399c3120e3b7da02b1c0c2f1fdee1", "guid": "bfdfe7dc352907fc980b868725387e98a301e35bfdf42e1d0f48773e23f17d48", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bceef1e1e94a358cd0b368d6fdbcf402", "guid": "bfdfe7dc352907fc980b868725387e984ab2b9bbbe9a8130483f9456ed924724", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7e60b8c0b69516f06ee8a59e48db982", "guid": "bfdfe7dc352907fc980b868725387e98e28f5e5ae1a99f59d3bd23a41ebd3b11", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5cf61467288e4f8bfa86a30de43a354", "guid": "bfdfe7dc352907fc980b868725387e982945877bc29f76221792cd877b495477", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1666ccb46fbf7e327354cfe3ae5c96d", "guid": "bfdfe7dc352907fc980b868725387e9802a15522752ba158127e90850c5689ac", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2ef99e47682cdfdfea7112aaea4beac", "guid": "bfdfe7dc352907fc980b868725387e988ac7b92ed245bb7f57afadc1d15ab81d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986df4e54f1c8d8183a4ae05640276894e", "guid": "bfdfe7dc352907fc980b868725387e987376ad66d35bf74dbc10b6b234d6f846", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863d4f4fae5167c1359c7949b60f24817", "guid": "bfdfe7dc352907fc980b868725387e98d211f82cea3e51ae7e67996167739f3c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814205f61af45a772047e650cb473f635", "guid": "bfdfe7dc352907fc980b868725387e9886eb2d857b58bd7fa504a7cda86c874f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868de240edefe607a962a69fc50e7dec0", "guid": "bfdfe7dc352907fc980b868725387e98915cec4941ac715fea7a911676f65163", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826d4f9c6797647382281bd92d94899ff", "guid": "bfdfe7dc352907fc980b868725387e98262c0259af0f6c346cc9cb8babb4b979", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d51b6f3bc4545e0247af5a98e83a914", "guid": "bfdfe7dc352907fc980b868725387e98410534e5af45d620a747f5cfbd78ddcd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5d4ba338708898b1d90f0c8eaa2bcdb", "guid": "bfdfe7dc352907fc980b868725387e98651fbc75b4b514498295808f9c6f605f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98709a4ba4d11b5df355eded857df06e02", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c1293cfb90e505cefe4dd7dad2dc615d", "guid": "bfdfe7dc352907fc980b868725387e98e77d116665331a698a3e2488ec60092b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98160415078be570608b43f0c7de2e124c", "guid": "bfdfe7dc352907fc980b868725387e9817a3b302c22214f1083d8f4f4e6ab94c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de0d676386b30fefa01298b98c6e03be", "guid": "bfdfe7dc352907fc980b868725387e984d185a538fcb4399ee02393f75f183cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98629117dbce16ef947fa3fd3d8b36a532", "guid": "bfdfe7dc352907fc980b868725387e98d914f5595f1733db4d2146259be6348d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b963984572f6ccef332e7fab567d51cf", "guid": "bfdfe7dc352907fc980b868725387e9860556e7deb57b1d2a33a35dc743f0db3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ec0e0de9207baee62220751389c5172", "guid": "bfdfe7dc352907fc980b868725387e98147af6cfcb1e7dff07a6b3d52b86dd5a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d6671031c313a8785932dc19dc544b7", "guid": "bfdfe7dc352907fc980b868725387e9883f2667d9aa7bcfe57a381f508b13401"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d49ea0f0b8a8c40b3b2679fc1129704", "guid": "bfdfe7dc352907fc980b868725387e986b4b4b4ca89969c4e5bb6e4641c8f5ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa8299afd88cace7c25810824421a445", "guid": "bfdfe7dc352907fc980b868725387e982dfcda8280b4d8f9dafc4da4c1c903da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98493c849ef4d93505fd3a58c983b63f5f", "guid": "bfdfe7dc352907fc980b868725387e982d545dca737430ce62934ec475ec0bab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98135fe20e0b05b7b153314e02a25b759a", "guid": "bfdfe7dc352907fc980b868725387e986b7c2f8760ffaf440ea375915ddbb9ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1570b47f5dbb6b74922e3be2447c812", "guid": "bfdfe7dc352907fc980b868725387e98cd1b0435ecbbdeb695a196a93a31c6d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2a40ff83f8cad3c823b60b14880bd98", "guid": "bfdfe7dc352907fc980b868725387e98b600c49374b67d4052bf25a049e908e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f17b3921572677679bfc68098a730c4", "guid": "bfdfe7dc352907fc980b868725387e9828c90a6dedccd826c21fde68ed61cb59"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb97528efc34897fb077aeb9fee9bc64", "guid": "bfdfe7dc352907fc980b868725387e987800c6c5fdb08a437483882098ce650f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a55949e0fd3a3d1e91beb1768aaa899", "guid": "bfdfe7dc352907fc980b868725387e98b8b05ad2d76ec051ac24e35f85b93361"}], "guid": "bfdfe7dc352907fc980b868725387e986ab5620dc55715e8e8f6fcd49a2ef83a", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986d33a47f1e1031e7e6a671aab8d386e4", "guid": "bfdfe7dc352907fc980b868725387e984fe03a62b0f3efa2b48d3a3971811d17"}], "guid": "bfdfe7dc352907fc980b868725387e98319a14c9389993bee5d96d155795db97", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e987cd2512697fd8475ad5d01e85d7d220e", "targetReference": "bfdfe7dc352907fc980b868725387e98198bde90bb38fef3e81f0c0918a7f3f9"}], "guid": "bfdfe7dc352907fc980b868725387e9868992c43aa4117f939ab7493574523f6", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98198bde90bb38fef3e81f0c0918a7f3f9", "name": "in_app_purchase_storekit-in_app_purchase_storekit_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e982a930221dc4925ae3ad26ac05af9179d", "name": "in_app_purchase_storekit", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b48307e2bc58dc7155fc2e80bc197afb", "name": "in_app_purchase_storekit.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
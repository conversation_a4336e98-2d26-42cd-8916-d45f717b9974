// lib/constants/app_constants.dart
import 'dart:io';

/// Class to hold all application constants
class AppConstants {
  // Debug / Development Settings

  /// Set to true to reset onboarding on every app restart (for testing)
  /// WARNING: Set to false before release!
  static const bool RESET_ONBOARDING_ON_RESTART = true;

  // Firebase database keys
  static const String FULL_GAME_PURCHASED_KEY = 'full_game_purchased';
  static const String PURCHASE_ID_KEY = 'purchase_id';

  // Subscription configuration
  // CRITICAL: These product IDs must match exactly what's configured in:
  // - iOS: App Store Connect → Features → Subscriptions
  // - Android: Google Play Console → Monetization → Subscriptions
  // Bundle ID: com.goodkarmalab.brainyBunny

  // Weekly subscription
  static const String WEEKLY_SUBSCRIPTION_IOS = 'brainy_bunny_weekly';
  static const String WEEKLY_SUBSCRIPTION_ANDROID = 'brainy_bunny_weekly';

  // Monthly subscription
  static const String MONTHLY_SUBSCRIPTION_IOS = 'brainy_bunny_monthly';
  static const String MONTHLY_SUBSCRIPTION_ANDROID = 'brainy_bunny_monthly';

  // Yearly subscription (with 7-day free trial)
  static const String YEARLY_SUBSCRIPTION_IOS = 'brainy_bunny_yearly';
  static const String YEARLY_SUBSCRIPTION_ANDROID = 'brainy_bunny_yearly';

  // All subscription product IDs for querying
  static const Set<String> SUBSCRIPTION_PRODUCT_IDS = {
    'brainy_bunny_weekly',
    'brainy_bunny_monthly',
    'brainy_bunny_yearly',
  };

  // Legacy one-time purchase constants (kept for grandfathering existing users)
  // DO NOT REMOVE: Still needed by PurchaseManager for lifetime users
  static const String FALLBACK_PRICE = '€6.99'; // Old one-time purchase fallback
  static const String PRODUCT_ID = 'brainy_bunny'; // Old one-time purchase product ID

  static const String FALLBACK_PRICE_WEEKLY = '€4.99';
  static const String FALLBACK_PRICE_MONTHLY = '€8.99';
  static const String FALLBACK_PRICE_YEARLY = '€39.99';

  static const String APP_SALT = 'brainy_bunny_secure_salt_2024';

  // Trial configuration
  static const int TRIAL_DURATION_DAYS = 7;
  static const int TRIAL_REMINDER_DAY_5 = 5; // 2 days before end
  static const int TRIAL_REMINDER_DAY_6 = 6; // 1 day before end

  // Asset paths
  static const String SETTINGS_ICON_PATH = 'icon_gear.png';
  static const String LOCK_ICON_PATH = 'icon_padlock.png';
  static const String HOME_ICON_PATH = 'icon_home.png';
  static const String HOME_BACKGROUND_PATH = 'home_background.jpg';

  // Audio paths
  static const List<String> AUDIO_FILES = [
    'button_press.mp3',
    'match_sound.wav',
    'round_complete_sound.wav',
    'game_complete_sound.wav',
    'Menu.mp3', // menu music
    'Background_1.mp3', // Game music option 1
    'Background_2.mp3', // Game music option 2
    'Background_3.mp3', // Game music option 3
  ];

  // Game configuration
  static const int FREE_GAMES_COUNT = 5;
  static const int TOTAL_GAMES_COUNT = 15;
  static const List<int> ROUND_PAIRS = [1, 2, 3, 4, 5];

  // Game paths pattern
  static String getGameIconPath(int index) => 'game_${index + 1}/icon.png';

  static String getGamePath(int index) => 'game_${index + 1}';

  // Overlay identifiers
  static const String LOADING_OVERLAY = 'loading';
  static const String PENDING_PURCHASE_OVERLAY = 'pendingPurchase';
  static const String PURCHASE_ERROR_OVERLAY = 'purchaseError';
  static const String INVALID_PURCHASE_OVERLAY = 'invalidPurchase';

  // Purchase flow overlays
  static const String PURCHASE_OFFER_OVERLAY = 'purchaseOffer';
  static const String AGE_VERIFICATION_OVERLAY = 'ageVerification';

  // ADDED: Debug overlay
  static const String DEBUG_OVERLAY = 'debug';
  static const String IAP_DEBUG_OVERLAY = 'iap_debug';

  // Analytics Event Names
  // Install and onboarding events
  static const String EVENT_APP_INSTALLED = 'app_installed';
  static const String EVENT_ONBOARDING_STARTED = 'onboarding_started';
  static const String EVENT_ONBOARDING_COMPLETED = 'onboarding_completed';
  static const String EVENT_ONBOARDING_SCREEN_VIEW = 'onboarding_screen_view';

  // Game events
  static const String EVENT_GAME_PLAYED = 'game_played';
  static const String EVENT_GAME_COMPLETED = 'game_completed';
  static const String EVENT_DEMO_GAME_PLAYED = 'demo_game_played';

  // Paywall and purchase events
  static const String EVENT_PAYWALL_VIEWED = 'paywall_viewed';
  static const String EVENT_PAYWALL_DISMISSED = 'paywall_dismissed';
  static const String EVENT_SUBSCRIPTION_SELECTED = 'subscription_selected';
  static const String EVENT_TRIAL_STARTED = 'trial_started';
  static const String EVENT_SUBSCRIPTION_PURCHASED = 'subscription_purchased';
  static const String EVENT_SUBSCRIPTION_CANCELLED = 'subscription_cancelled';
  static const String EVENT_SUBSCRIPTION_RENEWED = 'subscription_renewed';
  static const String EVENT_RESTORE_PURCHASES = 'restore_purchases';

  // Error events
  static const String EVENT_PURCHASE_FAILED = 'purchase_failed';
  static const String EVENT_RESTORE_FAILED = 'restore_failed';
  static const String EVENT_VERIFICATION_FAILED = 'verification_failed';

  // Analytics event parameters
  static const String PARAM_SCREEN_NAME = 'screen_name';
  static const String PARAM_GAME_ID = 'game_id';
  static const String PARAM_GAME_NAME = 'game_name';
  static const String PARAM_SUBSCRIPTION_ID = 'subscription_id';
  static const String PARAM_SUBSCRIPTION_TIER = 'subscription_tier';
  static const String PARAM_PRICE = 'price';
  static const String PARAM_CURRENCY = 'currency';
  static const String PARAM_ERROR_CODE = 'error_code';
  static const String PARAM_ERROR_MESSAGE = 'error_message';
  static const String PARAM_CHILD_AGE = 'child_age';
  static const String PARAM_DEMO_GAME_INDEX = 'demo_game_index';

  // Adjust Configuration
  static const String ADJUST_APP_TOKEN = '2k0f6660xxxc'; // TODO: Replace with actual token
  static const String ADJUST_SKAN_ENDPOINT = 'https://skan-rocapine.com';

  // Meta SDK Configuration
  static const String META_APP_ID = '2012260112880464';
  static const String META_APP_NAME = 'Brainy Bunny';
  // TODO: Add Meta Client Token from dashboard

  // RevenueCat Configuration
  // TODO: Replace with actual RevenueCat API key from dashboard
  static const String REVENUECAT_API_KEY_IOS = 'appl_xxxxxxxxxxxxx';
  static const String REVENUECAT_API_KEY_ANDROID = 'goog_xxxxxxxxxxxxx';

  /// Get platform-specific RevenueCat API key
  static String get REVENUECAT_API_KEY {
    if (Platform.isIOS) {
      return REVENUECAT_API_KEY_IOS;
    } else {
      return REVENUECAT_API_KEY_ANDROID;
    }
  }
}
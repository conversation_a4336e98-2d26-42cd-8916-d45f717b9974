{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9818c86b2a5f9b4cc7c11c41e0e729bd81", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98336bc2c10120691fc98346fcaac53719", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989b60fbcbd7a6ec37059937d2542f9dd3", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9880943e3b503046039f8869294c9fc05d", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989b60fbcbd7a6ec37059937d2542f9dd3", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9812886e024234000fad9af6dda4b0b393", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9881be6ba7ddf8da6eab0e0996a7a7ad52", "guid": "bfdfe7dc352907fc980b868725387e987ff71f2015f5265dd18be09142579e0c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871ca65ff4187d3b5a24a4e58f289c7cc", "guid": "bfdfe7dc352907fc980b868725387e9860d917e8147c3a306f072ba0eb1bd586", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8e6b5427e3f2524448bf682b172684c", "guid": "bfdfe7dc352907fc980b868725387e989b33412f2b93c32214373fffcb4be57b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843bbfc5c7aeab1a2d10a82ff7b0117c3", "guid": "bfdfe7dc352907fc980b868725387e98f86e4ecc61bb5b0a2a950de919ce5d9c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a555bd8e9c7a1dc189ebe7376c9e75cc", "guid": "bfdfe7dc352907fc980b868725387e98c00afed4203da22e824f8c5465b6b4e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6e13b8e7d2b613b3a5a73cde0b6dc58", "guid": "bfdfe7dc352907fc980b868725387e98b8915a851d7186f3c8038e2625511993", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e987dd28bb62c8cf51f8b6ef51226e6e730", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988945cbe8592702322a7f1fdbec44da64", "guid": "bfdfe7dc352907fc980b868725387e98767c8837540372fdf8ff4e7760261aa4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1d95c1e33e908fd395687f3824cb804", "guid": "bfdfe7dc352907fc980b868725387e9810a03ad97e64e77c46d996196c35e351"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98151441efd328f7fa94dc0bba2bf3879c", "guid": "bfdfe7dc352907fc980b868725387e985fa7883b0e916d450573c8658d89a63e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883e0971fccd873ad3eb83594ed1a5e86", "guid": "bfdfe7dc352907fc980b868725387e98a5c5d26468156fe5b9b5b63358347aae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866cf28852dac5660b75a0c849fea90c5", "guid": "bfdfe7dc352907fc980b868725387e9843b09feb2e213edc9f519e47ec52dcda"}], "guid": "bfdfe7dc352907fc980b868725387e988a98f2a94ef8771f8bf8dcc52adfe42b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986d33a47f1e1031e7e6a671aab8d386e4", "guid": "bfdfe7dc352907fc980b868725387e98838d8da8f3c14d67dd05d7a983e1818f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825fcdd28269a098362a526741c54f856", "guid": "bfdfe7dc352907fc980b868725387e980e444402421a1cb931fe9147202592df"}], "guid": "bfdfe7dc352907fc980b868725387e98653fe0ef6d94af8bcfc9dd8204c25762", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98886903d1c398bb32bc9879c11935e75e", "targetReference": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1"}], "guid": "bfdfe7dc352907fc980b868725387e98355ae38fbf130d3dea05848c93f2def0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1", "name": "GTMSessionFetcher-GTMSessionFetcher_Core_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f65e88472d384b1ba0888326befb3a8e", "name": "GTMSessionFetcher.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
// lib/ui/screens/paywall_flow_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:brainy_bunny/onboarding/screens/orientation_transition_screen.dart';
import 'package:brainy_bunny/onboarding/screens/pre_paywall_screen.dart';
import 'package:brainy_bunny/onboarding/screens/trial_explanation_screen.dart';
import 'package:brainy_bunny/onboarding/screens/unified_paywall_screen.dart';
import 'package:brainy_bunny/onboarding/screens/notification_permission_screen.dart';
import 'package:brainy_bunny/services/analytics/analytics_manager.dart';
import '../../l10n/app_localizations.dart';

/// Reusable paywall flow screen that can be shown when:
/// 1. User clicks on a locked game in the home screen
/// 2. User needs to see subscription options
///
/// This matches the onboarding paywall flow (screens 8-11) in portrait mode.
class PaywallFlowScreen extends StatefulWidget {
  /// Callback when user completes subscription
  final VoidCallback? onSubscriptionComplete;

  /// Callback when user skips the paywall
  final VoidCallback? onSkip;

  const PaywallFlowScreen({
    super.key,
    this.onSubscriptionComplete,
    this.onSkip,
  });

  @override
  State<PaywallFlowScreen> createState() => _PaywallFlowScreenState();
}

class _PaywallFlowScreenState extends State<PaywallFlowScreen> {
  // Screen indices:
  // -1: Initial rotation screen (landscape → portrait)
  // 0: Pre-Paywall Screen (Personalized with child's info)
  // 1: Trial Explanation Screen (NEW)
  // 2: Unified Paywall Screen (NEW)
  // 3: Notification Permission (after subscription)
  // 4: Final rotation screen (portrait → landscape)
  int _currentScreenIndex = -1;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializePaywallFlow();
  }

  Future<void> _initializePaywallFlow() async {
    try {
      if (kDebugMode) {
        print('✅ PaywallFlowScreen initialized - showing rotation screen');
      }

      setState(() {
        _isLoading = false;
      });

      // Track paywall viewed event
      await AnalyticsManager.instance.trackEvent(
        eventName: 'paywall_flow_started',
        parameters: {
          'source': 'locked_game',
        },
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error initializing paywall flow: $e');
      }

      setState(() {
        _isLoading = false;
      });
    }
  }

  void _goToNextScreen() {
    setState(() {
      _currentScreenIndex++;
    });

    if (kDebugMode) {
      print('📱 Paywall flow: moved to screen $_currentScreenIndex');
    }
  }

  void _goBackScreen() {
    if (_currentScreenIndex > 0) {
      setState(() {
        _currentScreenIndex--;
      });

      if (kDebugMode) {
        print('📱 Paywall flow: moved back to screen $_currentScreenIndex');
      }
    } else {
      // At first screen, going back means skip/exit
      _handleSkip();
    }
  }

  Future<void> _handleSkip() async {
    if (kDebugMode) {
      print('🚪 User skipped paywall flow at screen $_currentScreenIndex');
    }

    // Track skip event
    await AnalyticsManager.instance.trackEvent(
      eventName: 'paywall_flow_skipped',
      parameters: {
        'screen_index': _currentScreenIndex.toString(),
        'source': 'locked_game',
      },
    );

    // Show rotation screen before returning to home
    setState(() {
      _currentScreenIndex = 4; // Final rotation screen
    });
  }

  Future<void> _handleSubscription() async {
    if (kDebugMode) {
      print('🎉 User completed subscription in paywall flow');
    }

    // Track subscription event
    await AnalyticsManager.instance.trackEvent(
      eventName: 'paywall_flow_subscription_completed',
      parameters: {
        'source': 'locked_game',
      },
    );

    // Show notification permission screen after subscription
    setState(() {
      _currentScreenIndex = 3; // Notification permission screen
    });
  }

  Future<void> _handleNotificationComplete() async {
    if (kDebugMode) {
      print('✅ Notification permission screen completed');
    }

    // Show rotation screen before returning to home
    setState(() {
      _currentScreenIndex = 4; // Final rotation screen
    });
  }

  Future<void> _handleFinalRotationComplete() async {
    if (kDebugMode) {
      print('↩️ Final rotation complete - returning to home');
    }

    // Reset to landscape orientation before returning to home
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);

    // Small delay to ensure orientation change completes
    await Future.delayed(const Duration(milliseconds: 300));

    if (mounted) {
      if (widget.onSubscriptionComplete != null) {
        widget.onSubscriptionComplete!();
      } else if (widget.onSkip != null) {
        widget.onSkip!();
      } else {
        // Default behavior: pop navigation
        Navigator.of(context).pop();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return WillPopScope(
      onWillPop: () async {
        // Handle back button - treat as skip
        _handleSkip();
        return false; // We handle the pop ourselves
      },
      child: _buildCurrentScreen(),
    );
  }

  Widget _buildCurrentScreen() {
    final l10n = AppLocalizations.of(context);

    switch (_currentScreenIndex) {
      case -1: // Initial rotation screen (landscape → portrait)
        return OrientationTransitionScreen(
          message: l10n?.onboarding_rotate_to_portrait ?? 'Please rotate to portrait',
          toLandscape: false, // Rotating TO portrait
          onComplete: () async {
            // Set portrait orientation
            await SystemChrome.setPreferredOrientations([
              DeviceOrientation.portraitUp,
            ]);

            // Small delay to ensure orientation completes
            await Future.delayed(const Duration(milliseconds: 300));

            // Move to pre-paywall screen
            _goToNextScreen();
          },
        );

      case 0: // Pre-Paywall Screen: Personalized with child's info
        return PrePaywallScreen(
          onStartTrial: _goToNextScreen, // Go to Trial Explanation Screen
          onTryFreeGames: _handleSkip, // Skip and return to home
          childName: null, // Will load from OnboardingState
          childAge: null, // Will load from OnboardingState
        );

      case 1: // Trial Explanation Screen (NEW)
        return TrialExplanationScreen(
          onContinue: _goToNextScreen, // Go to Unified Paywall
          onSkip: _handleSkip, // Skip and return to home
        );

      case 2: // Unified Paywall Screen (NEW)
        return UnifiedPaywallScreen(
          onSubscribed: _handleSubscription, // Complete purchase
          onSkip: _handleSkip, // Skip and return to home
          onBack: _goBackScreen, // Go back to trial explanation
        );

      case 3: // Notification Permission (after subscription)
        return NotificationPermissionScreen(
          onComplete: _handleNotificationComplete,
        );

      case 4: // Final rotation screen (portrait → landscape)
        return OrientationTransitionScreen(
          message: l10n?.onboarding_rotate_to_landscape ?? 'Please rotate to landscape',
          toLandscape: true, // Rotating TO landscape
          onComplete: _handleFinalRotationComplete,
        );

      default:
        // Shouldn't happen, but return initial rotation screen as fallback
        return OrientationTransitionScreen(
          message: l10n?.onboarding_rotate_to_portrait ?? 'Please rotate to portrait',
          toLandscape: false,
          onComplete: () {
            setState(() {
              _currentScreenIndex = 0;
            });
          },
        );
    }
  }

  @override
  void dispose() {
    try {
      // Ensure orientation is reset to landscape when leaving
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);

      if (kDebugMode) {
        print('🧹 PaywallFlowScreen disposed, orientation reset to landscape');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error disposing paywall flow screen: $e');
      }
    }
    super.dispose();
  }
}

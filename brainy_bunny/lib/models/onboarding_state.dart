// lib/models/onboarding_state.dart
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

/// Model to store onboarding state and user input
class OnboardingState {
  String? parentName;
  int? childAge;
  String? learningGoal;
  String? screenTimeGoal;
  DateTime? startedAt;
  DateTime? completedAt;

  OnboardingState({
    this.parentName,
    this.childAge,
    this.learningGoal,
    this.screenTimeGoal,
    this.startedAt,
    this.completedAt,
  });

  /// Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'parentName': parentName,
      'childAge': childAge,
      'learningGoal': learningGoal,
      'screenTimeGoal': screenTimeGoal,
      'startedAt': startedAt?.toIso8601String(),
      'completedAt': completedAt?.toIso8601String(),
    };
  }

  /// Create from JSON
  factory OnboardingState.fromJson(Map<String, dynamic> json) {
    return OnboardingState(
      parentName: json['parentName'] as String?,
      childAge: json['childAge'] as int?,
      learningGoal: json['learningGoal'] as String?,
      screenTimeGoal: json['screenTimeGoal'] as String?,
      startedAt: json['startedAt'] != null
          ? DateTime.parse(json['startedAt'] as String)
          : null,
      completedAt: json['completedAt'] != null
          ? DateTime.parse(json['completedAt'] as String)
          : null,
    );
  }

  /// Save to SharedPreferences
  Future<void> save() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = jsonEncode(toJson());
      await prefs.setString('onboarding_state', jsonString);
    } catch (e) {
      print('❌ Failed to save onboarding state: $e');
    }
  }

  /// Load from SharedPreferences
  static Future<OnboardingState> load() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString('onboarding_state');

      if (jsonString != null) {
        final json = jsonDecode(jsonString) as Map<String, dynamic>;
        return OnboardingState.fromJson(json);
      }
    } catch (e) {
      print('❌ Failed to load onboarding state: $e');
    }

    return OnboardingState();
  }

  /// Clear saved state
  static Future<void> clear() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('onboarding_state');
    } catch (e) {
      print('❌ Failed to clear onboarding state: $e');
    }
  }

  /// Validate required fields
  bool isValid() {
    return parentName != null &&
           parentName!.trim().length >= 2 &&
           childAge != null &&
           childAge! >= 2 &&
           childAge! <= 10;
  }

  /// Check if name is valid
  bool isNameValid(String? name) {
    if (name == null || name.trim().isEmpty) return false;
    final trimmed = name.trim();
    return trimmed.length >= 2 &&
           trimmed.length <= 20 &&
           RegExp(r'^[a-zA-Z\s\-àáâäãåąčćęèéêëėįìíîïłńòóôöõøùúûüųūÿýżźñçčšžÀÁÂÄÃÅĄĆČĖĘÈÉÊËÌÍÎÏĮŁŃÒÓÔÖÕØÙÚÛÜŲŪŸÝŻŹÑßÇŒÆČŠŽ∂ð]+$').hasMatch(trimmed);
  }

  /// Create a copy with updated fields
  OnboardingState copyWith({
    String? parentName,
    int? childAge,
    String? learningGoal,
    String? screenTimeGoal,
    DateTime? startedAt,
    DateTime? completedAt,
  }) {
    return OnboardingState(
      parentName: parentName ?? this.parentName,
      childAge: childAge ?? this.childAge,
      learningGoal: learningGoal ?? this.learningGoal,
      screenTimeGoal: screenTimeGoal ?? this.screenTimeGoal,
      startedAt: startedAt ?? this.startedAt,
      completedAt: completedAt ?? this.completedAt,
    );
  }

  @override
  String toString() {
    return 'OnboardingState(parentName: $parentName, childAge: $childAge, learningGoal: $learningGoal, screenTimeGoal: $screenTimeGoal)';
  }
}

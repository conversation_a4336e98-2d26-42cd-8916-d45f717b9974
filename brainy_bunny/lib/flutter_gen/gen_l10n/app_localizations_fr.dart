// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for French (`fr`).
class AppLocalizationsFr extends AppLocalizations {
  AppLocalizationsFr([String locale = 'fr']) : super(locale);

  @override
  String get onboarding_welcome_headline =>
      'Aidez votre enfant à apprendre et à grandir';

  @override
  String get onboarding_welcome_subheading =>
      'Jeux éducatifs conçus pour les tout-petits de 2 à 5 ans';

  @override
  String get onboarding_welcome_cta => 'Commencer';

  @override
  String get onboarding_name_headline => 'Personnalisons votre expérience';

  @override
  String get onboarding_name_hint => 'Votre nom';

  @override
  String get onboarding_name_mom => 'Maman';

  @override
  String get onboarding_name_dad => 'Papa';

  @override
  String get onboarding_name_parent => 'Parent';

  @override
  String onboarding_name_greeting(String name) {
    return '<PERSON><PERSON><PERSON>, $name !';
  }

  @override
  String get onboarding_child_age_headline => 'Quel âge a votre enfant ?';

  @override
  String get onboarding_child_age_subtext =>
      'All games work for ages 2-5. This helps us provide age-appropriate tips.';

  @override
  String get onboarding_age_2 => '2 ans';

  @override
  String get onboarding_age_3 => '3 ans';

  @override
  String get onboarding_age_4 => '4 ans';

  @override
  String get onboarding_age_5_plus => '5 ans et plus';

  @override
  String get onboarding_philosophy_headline =>
      'Transformez le temps d\'écran en temps d\'apprentissage';

  @override
  String get onboarding_philosophy_aap =>
      'Conforme aux recommandations de l\'AAP sur le temps d\'écran';

  @override
  String get onboarding_philosophy_learning =>
      'Transformez le temps d\'écran en expériences d\'apprentissage significatives';

  @override
  String get onboarding_philosophy_skills =>
      'Développez les compétences cognitives par l\'apprentissage par le jeu';

  @override
  String get onboarding_transition_to_games =>
      'Jouons ! Tournez votre appareil →';

  @override
  String get onboarding_transition_from_games =>
      'Excellent apprentissage ! Finissons →';

  @override
  String get onboarding_solution_headline => 'Learning through play';

  @override
  String get onboarding_solution_description =>
      'While they match shapes and colors, they\'re building real cognitive skills.';

  @override
  String get onboarding_solution_benefit_engagement => 'Active engagement';

  @override
  String get onboarding_solution_benefit_pattern_recognition =>
      'Pattern recognition';

  @override
  String get onboarding_solution_benefit_cause_effect =>
      'Cause-and-effect thinking';

  @override
  String get onboarding_solution_benefit_screen_time =>
      'Aligned with 1-hour screen time guidelines';

  @override
  String get onboarding_solution_research_text =>
      'Research shows: Matching activities improve spatial reasoning...';

  @override
  String get onboarding_solution_research_source =>
      'Based on developmental psychology studies';

  @override
  String get onboarding_solution_cta => 'See how it works';

  @override
  String get onboarding_problem_headline =>
      'We understand your\nscreen time concerns';

  @override
  String get onboarding_problem_point_1_title => 'Passive video consumption';

  @override
  String get onboarding_problem_point_1_description =>
      'Hours of mindless watching with zero interaction or learning';

  @override
  String get onboarding_problem_point_1_statistic =>
      '6x higher risk of language delays';

  @override
  String get onboarding_problem_point_2_title => 'Brain development damage';

  @override
  String get onboarding_problem_point_2_description =>
      'Excessive screen time alters white matter structure in developing brains';

  @override
  String get onboarding_problem_point_2_statistic =>
      'Reduced cognitive abilities';

  @override
  String get onboarding_problem_point_3_title =>
      'Inappropriate content exposure';

  @override
  String get onboarding_problem_point_3_description =>
      'Ads, violence, and age-inappropriate material in \'kids\' content';

  @override
  String get onboarding_problem_point_3_statistic =>
      '85% of kids\' apps contain ads';

  @override
  String get onboarding_problem_point_4_title => 'Attention & focus problems';

  @override
  String get onboarding_problem_point_4_description =>
      'Fast-paced content destroys ability to concentrate and learn';

  @override
  String get onboarding_problem_point_4_statistic =>
      '40% increase in ADHD symptoms';

  @override
  String get onboarding_problem_research_title => 'Scientific Evidence';

  @override
  String get onboarding_problem_research_text =>
      'Peer-reviewed studies confirm: excessive passive screen time causes brain changes, language delays, and attention problems in young children.';

  @override
  String get onboarding_problem_subtext =>
      'You\'re not alone. 89% of parents share these concerns.';

  @override
  String get onboarding_problem_cta => 'There\'s a better way';

  @override
  String get demo_game_1_skill =>
      'Appariement visuel et reconnaissance des formes';

  @override
  String get demo_game_1_science =>
      'Associer les animaux à leurs silhouettes développe la discrimination visuelle - la capacité de remarquer les différences entre des formes similaires. Cette compétence est essentielle pour la reconnaissance des lettres et la lecture.';

  @override
  String get demo_game_1_citation =>
      'Bornstein (1985) - Traitement visuel et pensée catégorielle';

  @override
  String get demo_game_1_badge => 'Détective des formes';

  @override
  String get demo_game_2_skill => 'Mémoire visuelle et attention';

  @override
  String get demo_game_2_science =>
      'Trouver des paires assorties renforce la mémoire de travail - la capacité de votre enfant à retenir et manipuler l\'information. Cela soutient directement la résolution de problèmes mathématiques et le suivi d\'instructions en plusieurs étapes.';

  @override
  String get demo_game_2_citation =>
      'Gopnik & Meltzoff (1987) - Catégorisation et flexibilité cognitive';

  @override
  String get demo_game_2_badge => 'Maître de la mémoire';

  @override
  String get demo_game_3_skill => 'Association logique et catégorisation';

  @override
  String get demo_game_3_science =>
      'Connecter les objets à leurs utilisateurs enseigne la catégorisation et la pensée logique. Votre enfant apprend que les choses vont ensemble pour des raisons - une étape clé pour comprendre la cause et l\'effet.';

  @override
  String get demo_game_3_citation =>
      'Piaget (1952) - Développement cognitif préopératoire';

  @override
  String get demo_game_3_badge => 'Étoile de la logique';

  @override
  String get demo_game_4_skill => 'Reconnaissance des motifs et appariement';

  @override
  String get demo_game_4_science =>
      'Associer des motifs développe la reconnaissance des motifs - la capacité de voir les relations entre les choses. Cette compétence prédit fortement le succès en mathématiques et aide les enfants à comprendre \'même\' vs \'différent\'.';

  @override
  String get demo_game_4_citation =>
      'Rittle-Johnson et al. (2019) - Compétences de motifs et mathématiques';

  @override
  String get demo_game_4_badge => 'Pro des motifs';

  @override
  String get demo_game_5_skill =>
      'Pensée symbolique et connexions avec le monde réel';

  @override
  String get demo_game_5_science =>
      'Connecter les outils aux carrières développe la pensée symbolique - comprendre qu\'une chose peut représenter une autre. Cette pensée abstraite est essentielle pour le langage, les mathématiques et l\'imagination.';

  @override
  String get demo_game_5_citation =>
      'Vygotsky (1978) - Représentation symbolique dans le développement cognitif';

  @override
  String get demo_game_5_badge => 'Explorateur du monde';

  @override
  String get demo_game_next => 'Jeu suivant';

  @override
  String onboarding_summary_headline(String name) {
    return 'Votre parcours jusqu\'à présent, $name';
  }

  @override
  String onboarding_summary_age(int age) {
    return 'Parfait pour les enfants de $age ans';
  }

  @override
  String get onboarding_summary_skills => '5 compétences pratiquées';

  @override
  String get onboarding_summary_screen_time =>
      'Approche du temps d\'écran sain alignée sur les directives de l\'AAP';

  @override
  String get onboarding_summary_cta => 'Voir votre plan personnalisé';

  @override
  String get trust_headline => 'Approuvé par les parents et les éducateurs';

  @override
  String get trust_approved => 'Approuvé par les enseignants';

  @override
  String get trust_cta => 'Débloquez l\'expérience d\'apprentissage complète';

  @override
  String get paywall_headline => 'Débloquez les 15 jeux d\'apprentissage';

  @override
  String get paywall_subheadline =>
      'Continuez le parcours d\'apprentissage de votre enfant';

  @override
  String get paywall_feature_games =>
      '15 jeux éducatifs ciblant les compétences clés';

  @override
  String get paywall_feature_progress => 'Suivi des progrès de votre enfant';

  @override
  String get paywall_feature_ad_free => 'Environnement sûr et sans publicité';

  @override
  String get paywall_feature_new_games => 'Nouveaux jeux ajoutés chaque mois';

  @override
  String get paywall_feature_ages => 'Conçu pour les 2-5 ans';

  @override
  String get paywall_trial_headline =>
      'Essayez toutes les fonctionnalités GRATUITEMENT pendant 7 jours';

  @override
  String get paywall_trial_price => 'Puis seulement 0,87 €/semaine';

  @override
  String get paywall_trial_today => 'Aujourd\'hui : Accès complet débloqué';

  @override
  String get paywall_trial_day5 => 'Jour 5 : Nous vous enverrons un rappel';

  @override
  String get paywall_trial_day7 =>
      'Jour 7 : La facturation commence (annulez à tout moment avant)';

  @override
  String get paywall_trial_no_payment => '✓ Aucun paiement dû maintenant';

  @override
  String get paywall_trial_cta => 'Commencer l\'essai gratuit →';

  @override
  String get paywall_weekly => 'Hebdomadaire';

  @override
  String get paywall_monthly => 'Mensuel';

  @override
  String get paywall_yearly => 'Annuel';

  @override
  String get paywall_save_60 => 'Économisez 60%';

  @override
  String get paywall_most_popular => 'Choix le plus populaire';

  @override
  String get paywall_cancel_anytime =>
      'Annulez à tout moment depuis les paramètres de votre appareil';

  @override
  String get paywall_restore => 'Restaurer les achats';

  @override
  String get subscription_premium => 'Premium';

  @override
  String get subscription_trial => 'Essai de 7 jours';

  @override
  String get subscription_lifetime => 'Accès à vie';

  @override
  String get subscription_expired => 'Abonnement terminé';

  @override
  String get subscription_manage => 'Gérer l\'abonnement';

  @override
  String get subscription_cancel => 'Annuler l\'abonnement';

  @override
  String get subscription_change_plan => 'Changer de forfait';

  @override
  String get continue_button => 'Continuer';

  @override
  String get skip_button => 'Passer';

  @override
  String get close_button => 'Fermer';

  @override
  String get loading => 'Chargement...';

  @override
  String get error_purchase_failed => 'Achat échoué';

  @override
  String get error_purchase_failed_message =>
      'Nous n\'avons pas pu finaliser votre achat. Veuillez réessayer.';

  @override
  String get error_restore_failed => 'Aucun achat trouvé';

  @override
  String get error_restore_failed_message =>
      'Nous n\'avons trouvé aucun achat précédent. Si vous pensez qu\'il s\'agit d\'une erreur, veuillez contacter le support.';

  @override
  String get error_network => 'Erreur réseau';

  @override
  String get error_network_message =>
      'Veuillez vérifier votre connexion Internet et réessayer.';

  @override
  String get summary_headline => 'Progrès incroyables !';

  @override
  String summary_message_age_2(Object name) {
    return 'Excellent travail, $name ! Votre petit construit des compétences importantes par le jeu. À 2 ans, chaque association qu\'ils font renforce leur reconnaissance visuelle et leurs capacités de résolution de problèmes.';
  }

  @override
  String summary_message_age_3(Object name) {
    return 'Merveilleux, $name ! Votre enfant développe des compétences cognitives cruciales. À 3 ans, ces activités améliorent leur mémoire, leur concentration et leur pensée logique.';
  }

  @override
  String summary_message_age_4(Object name) {
    return 'Excellent travail, $name ! Votre enfant de 4 ans maîtrise la résolution avancée de problèmes. Ces jeux le préparent à réussir à la maternelle.';
  }

  @override
  String summary_message_age_5_plus(Object name) {
    return 'Fantastique, $name ! Votre enfant excelle dans la pensée complexe. Ces compétences lui donneront une base solide pour l\'école.';
  }

  @override
  String get summary_badges_earned => 'Badges gagnés';

  @override
  String get summary_badges => 'Badges';

  @override
  String get summary_games => 'Jeux';

  @override
  String get summary_skills => 'Compétences';

  @override
  String get trust_aap_description =>
      'Notre approche éducative s\'aligne sur les directives de l\'AAP pour un temps d\'écran sain et le développement de la petite enfance.';

  @override
  String get trust_research_title => 'Programme soutenu par la recherche';

  @override
  String get trust_research_description =>
      'Chaque jeu est conçu sur la base d\'études évaluées par des pairs sur le développement cognitif, avec des méthodes éprouvées pour améliorer l\'apprentissage chez les enfants de 2 à 5 ans.';

  @override
  String get trust_testimonial_1_name => 'Sarah M., Mère d\'un enfant de 3 ans';

  @override
  String get trust_testimonial_1_quote =>
      '\"Ma fille est passée de la difficulté avec les formes à les identifier avec confiance partout. Les progrès en seulement 2 semaines m\'ont étonné !\"';

  @override
  String get trust_testimonial_2_name =>
      'Michael T., Père d\'un enfant de 4 ans';

  @override
  String get trust_testimonial_2_quote =>
      '\"Enfin, du temps d\'écran dont je me sens bien ! Mon fils apprend tout en s\'amusant, et je peux voir de réelles améliorations cognitives.\"';

  @override
  String get trust_downloads_title => 'Rejoignez plus de 100 000 familles';

  @override
  String get trust_downloads_description =>
      'Approuvé par des parents dans plus de 50 pays pour donner à leurs enfants une longueur d\'avance dans l\'apprentissage.';

  @override
  String get trust_cta_headline =>
      'Prêt à débloquer le plein potentiel de votre enfant ?';

  @override
  String get trust_cta_button => 'Commencer l\'essai gratuit';

  @override
  String get paywall_premium_badge => 'Accès Premium';

  @override
  String get paywall_step1_headline =>
      'Débloquez le plein potentiel de votre enfant';

  @override
  String get paywall_value_1_title => '15 jeux premium';

  @override
  String get paywall_value_1_description =>
      'Accédez à tous les jeux éducatifs conçus pour les 2-5 ans, couvrant les formes, les couleurs, les nombres, la logique et plus encore';

  @override
  String get paywall_value_2_title => 'Suivi des progrès';

  @override
  String get paywall_value_2_description =>
      'Analyses détaillées montrant le développement de votre enfant et l\'amélioration des compétences au fil du temps';

  @override
  String get paywall_value_3_title => 'Apprentissage personnalisé';

  @override
  String get paywall_value_3_description =>
      'Les jeux s\'adaptent à l\'âge et au niveau de compétence de votre enfant pour un apprentissage optimal';

  @override
  String get paywall_value_4_title => 'Nouveau contenu mensuel';

  @override
  String get paywall_value_4_description =>
      'Mises à jour régulières avec de nouveaux jeux et activités pour garder l\'apprentissage frais et engageant';

  @override
  String get paywall_step1_cta => 'Voir les forfaits';

  @override
  String get paywall_secure_payment => 'Traitement de paiement sécurisé';

  @override
  String get paywall_trial_badge => 'Essai gratuit de 7 jours';

  @override
  String get paywall_step2_headline => 'Choisissez votre forfait';

  @override
  String get paywall_step2_subheadline =>
      'Commencez votre essai gratuit de 7 jours. Annulez à tout moment.';

  @override
  String get paywall_plan_best_value => 'Meilleur rapport qualité-prix';

  @override
  String get paywall_plan_yearly_title => 'Annuel';

  @override
  String get paywall_plan_yearly_period => '/an';

  @override
  String get paywall_plan_yearly_per_month => 'Seulement 3,33 €/mois';

  @override
  String get paywall_plan_yearly_savings =>
      'Économisez 63% par rapport au mensuel';

  @override
  String get paywall_plan_monthly_title => 'Mensuel';

  @override
  String get paywall_plan_monthly_period => '/mois';

  @override
  String get paywall_plan_weekly_title => 'Hebdomadaire';

  @override
  String get paywall_plan_weekly_period => '/semaine';

  @override
  String get paywall_plan_weekly_note => 'Pour un accès à court terme';

  @override
  String get paywall_trial_reminder =>
      'Votre essai gratuit commence aujourd\'hui. Vous ne serez pas facturé avant le jour 8. Annulez à tout moment avant sans frais.';

  @override
  String get paywall_step2_cta => 'Continuer';

  @override
  String get paywall_terms =>
      'En continuant, vous acceptez nos Conditions d\'utilisation et notre Politique de confidentialité';

  @override
  String get paywall_urgency_text => 'Offre à durée limitée';

  @override
  String get paywall_step3_headline => 'Vous n\'êtes plus qu\'à un pas !';

  @override
  String get paywall_step3_included_title => 'Tout ce que vous obtenez :';

  @override
  String get paywall_included_1 => 'Les 15 jeux éducatifs premium';

  @override
  String get paywall_included_2 =>
      'Parcours d\'apprentissage personnalisés pour votre enfant';

  @override
  String get paywall_included_3 => 'Suivi détaillé des progrès et informations';

  @override
  String get paywall_included_4 => 'Contenu et activités nouveaux chaque mois';

  @override
  String get paywall_included_5 =>
      'Expérience sans publicité pour un apprentissage concentré';

  @override
  String get paywall_included_6 =>
      'Mode hors ligne - apprenez n\'importe où, n\'importe quand';

  @override
  String get paywall_guarantee_title => 'Garantie 100% sans risque';

  @override
  String get paywall_guarantee_text =>
      'Essayez-le gratuitement pendant 7 jours. Si vous n\'êtes pas complètement satisfait, annulez avant la fin de l\'essai et ne payez rien. Sans questions posées.';

  @override
  String get paywall_step3_cta => 'Commencer mon essai gratuit';

  @override
  String get pre_paywall_headline => 'Your Learning Journey is Ready!';

  @override
  String pre_paywall_subheadline_personalized(String name) {
    return 'Here\'s what we\'ve prepared for $name:';
  }

  @override
  String pre_paywall_subheadline_age(String age) {
    return 'Here\'s what we\'ve prepared for your $age-year-old:';
  }

  @override
  String get pre_paywall_subheadline_generic =>
      'Here\'s what we\'ve prepared for your child:';

  @override
  String get pre_paywall_card_1_title => 'Age-Appropriate Content';

  @override
  String pre_paywall_card_1_subtitle_age(String age) {
    return 'Perfect for $age years old';
  }

  @override
  String get pre_paywall_card_1_subtitle_generic =>
      'All 15 games work great for ages 2-5';

  @override
  String get pre_paywall_card_2_title => 'Learning Focus';

  @override
  String get pre_paywall_card_2_subtitle => 'Building skills through play';

  @override
  String get pre_paywall_card_3_title => '15 Educational Games';

  @override
  String get pre_paywall_card_3_subtitle =>
      'Shapes, colors, patterns, animals & more';

  @override
  String get pre_paywall_key_benefit =>
      'Transform screen time from guilt into growth—educational content you can feel good about.';

  @override
  String get pre_paywall_trust_1 => 'Teacher Approved';

  @override
  String get pre_paywall_trust_2 => 'Ad-Free';

  @override
  String get pre_paywall_trust_3 => 'Expert-Backed';

  @override
  String get pre_paywall_cta_primary => 'Start 7-Day FREE Trial';

  @override
  String get pre_paywall_cta_primary_subtext =>
      'Unlock all 15 games • No charge today';

  @override
  String get pre_paywall_cta_secondary => 'Continue with 5 free games';

  @override
  String get pre_paywall_important_note =>
      'Trial includes all 15 games. After trial: Keep 5 free games or subscribe for full access.';

  @override
  String get onboarding_rotate_to_landscape =>
      'Tournez votre appareil en mode paysage';

  @override
  String get onboarding_rotate_to_portrait =>
      'Tournez votre appareil en mode portrait';

  @override
  String get demo_game_1_title => 'Formes d\'animaux';

  @override
  String get demo_game_1_context =>
      'Aidez votre enfant à associer les animaux à leurs formes ! Cela développe les compétences de reconnaissance visuelle essentielles pour la lecture.';

  @override
  String get demo_game_2_title => 'Mémoire associative';

  @override
  String get demo_game_2_context =>
      'Trouvez les paires assorties pour renforcer la mémoire de travail, essentielle pour suivre les instructions et résoudre les problèmes.';

  @override
  String get demo_game_3_title => 'Énigmes logiques';

  @override
  String get demo_game_3_context =>
      'Résolvez des énigmes pour développer la pensée logique et la reconnaissance des motifs.';

  @override
  String get demo_game_4_title => 'Plaisir des motifs';

  @override
  String get demo_game_4_context =>
      'Reconnaissez les motifs pour développer la préparation mathématique et les compétences de séquençage.';

  @override
  String get demo_game_5_title => 'Explorateur du monde';

  @override
  String get demo_game_5_context =>
      'Explorez le monde pour élargir le vocabulaire et la conscience culturelle.';

  @override
  String get demo_game_congratulations =>
      'Incroyable ! Vous avez gagné un badge !';

  @override
  String paywall_benefit_all_games(int gameCount) {
    return 'Accès à tous les $gameCount jeux';
  }

  @override
  String get paywall_benefit_age_appropriate => 'Contenu adapté à l\'âge';

  @override
  String get paywall_benefit_progress_tracking => 'Suivi des progrès';

  @override
  String get paywall_benefit_offline_play => 'Jeu hors ligne pris en charge';

  @override
  String get paywall_benefit_no_ads => 'Sans publicité, sûr pour les enfants';

  @override
  String get paywall_benefit_regular_updates => 'Mises à jour régulières';

  @override
  String get paywall_start_trial => 'Commencer l\'essai gratuit';

  @override
  String get paywall_step3_benefit_1 =>
      'Accès complet à tous les jeux d\'apprentissage';

  @override
  String get paywall_step3_benefit_2 =>
      'Environnement d\'apprentissage sûr et sans publicité';

  @override
  String get paywall_step3_benefit_3 => 'Parfait pour toute la famille';

  @override
  String get paywall_subscribe_button => 'S\'abonner maintenant';

  @override
  String get trial_explanation_headline => 'Try All 15 Games Free for 7 Days';

  @override
  String get trial_explanation_feature_1 =>
      'Full access to all 15 educational games';

  @override
  String get trial_explanation_feature_2 => 'No charge for 7 days';

  @override
  String get trial_explanation_feature_3 =>
      'We\'ll remind you 2 days before trial ends';

  @override
  String get trial_explanation_feature_4 =>
      'Cancel anytime during trial - no cost';

  @override
  String get trial_explanation_subtext =>
      'You won\'t be charged until day 8 of your trial';

  @override
  String get trial_explanation_cta => 'See Plans';

  @override
  String get unified_paywall_headline => 'Choose Your Plan';

  @override
  String get unified_paywall_subheadline =>
      'All plans unlock 15 educational games';

  @override
  String get unified_paywall_yearly_badge_save => 'Save 60%';

  @override
  String get unified_paywall_yearly_badge_trial => '7-Day Free Trial';

  @override
  String get unified_paywall_yearly_title => 'Yearly';

  @override
  String unified_paywall_yearly_price_breakdown(String monthlyEquivalent) {
    return 'Just $monthlyEquivalent/month';
  }

  @override
  String get unified_paywall_yearly_savings => 'Save 63% • Try free for 7 days';

  @override
  String get unified_paywall_yearly_feature_1 => 'No charge for 7 days';

  @override
  String get unified_paywall_yearly_feature_2 => 'Cancel anytime during trial';

  @override
  String get unified_paywall_yearly_feature_3 => 'Full access to all 15 games';

  @override
  String get unified_paywall_yearly_button => 'Start Free Trial';

  @override
  String get unified_paywall_monthly_title => 'Monthly';

  @override
  String unified_paywall_monthly_per_week(String weeklyEquivalent) {
    return '$weeklyEquivalent/week';
  }

  @override
  String get unified_paywall_monthly_savings => 'Flexible monthly plan';

  @override
  String get unified_paywall_monthly_feature_1 => 'Cancel anytime';

  @override
  String get unified_paywall_monthly_feature_2 => 'Full access to all 15 games';

  @override
  String get unified_paywall_monthly_button => 'Subscribe Monthly';

  @override
  String get unified_paywall_weekly_title => 'Weekly';

  @override
  String get unified_paywall_weekly_savings => 'Try for just one week';

  @override
  String get unified_paywall_weekly_feature_1 => 'Cancel anytime';

  @override
  String get unified_paywall_weekly_feature_2 => 'Full access to all 15 games';

  @override
  String get unified_paywall_weekly_button => 'Subscribe Weekly';

  @override
  String get unified_paywall_trust_1 => 'Secure payment processing';

  @override
  String get unified_paywall_trust_2 => 'Manage in App/Play Store';

  @override
  String get unified_paywall_trust_3 => 'All plans include full access';

  @override
  String get unified_paywall_restore => 'Restore Purchases';

  @override
  String get unified_paywall_terms =>
      'By continuing, you agree to our Terms of Service and Privacy Policy';

  @override
  String get educational_value_headline =>
      'Du temps d\'écran de qualité qui aide vraiment votre enfant';

  @override
  String get educational_value_point_1_title =>
      'Develops pattern recognition & logical thinking';

  @override
  String get educational_value_point_1_description =>
      'Essential skills for math readiness and problem-solving';

  @override
  String get educational_value_point_2_title =>
      'Strengthens visual discrimination skills';

  @override
  String get educational_value_point_2_description =>
      'Helps children identify differences and similarities';

  @override
  String get educational_value_point_3_title =>
      'Builds problem-solving abilities';

  @override
  String get educational_value_point_3_description =>
      'Active engagement develops critical thinking';

  @override
  String get educational_value_point_4_title =>
      'Designed for healthy screen time';

  @override
  String get educational_value_point_4_description =>
      'Aligned with pediatric recommendation of 1 hour daily';

  @override
  String get educational_value_research =>
      'Soutenu par la recherche en développement de la petite enfance';

  @override
  String get educational_value_research_source =>
      'Based on developmental psychology studies';

  @override
  String get value_carousel_1_headline =>
      'Développer les compétences cognitives par le jeu';

  @override
  String get value_carousel_1_description =>
      'Nos jeux sont conçus pour renforcer la mémoire, la résolution de problèmes et les compétences de pensée critique au rythme parfait pour l\'âge de votre enfant.';

  @override
  String get value_carousel_1_subtext =>
      'Not just entertainment—actual learning in every match.';

  @override
  String get value_carousel_1_benefit_1_title => 'Développement de la mémoire';

  @override
  String get value_carousel_1_benefit_1_description =>
      'Jeux d\'association qui renforcent le rappel et la reconnaissance';

  @override
  String get value_carousel_1_benefit_2_title => 'Résolution de problèmes';

  @override
  String get value_carousel_1_benefit_2_description =>
      'Énigmes qui encouragent la pensée logique et la stratégie';

  @override
  String get value_carousel_1_benefit_3_title => 'Apprentissage progressif';

  @override
  String get value_carousel_1_benefit_3_description =>
      'La difficulté s\'adapte au fur et à mesure que votre enfant maîtrise de nouvelles compétences';

  @override
  String get value_carousel_2_headline =>
      'Du temps d\'écran dont vous pouvez vous sentir bien';

  @override
  String get value_carousel_2_description =>
      'Contrairement au divertissement passif, Brainy Bunny engage activement l\'esprit de votre enfant dans un environnement sûr et sans publicité.';

  @override
  String get value_carousel_2_category_1 => 'Shapes & Geometry';

  @override
  String get value_carousel_2_category_2 => 'Colors & Patterns';

  @override
  String get value_carousel_2_category_3 => 'Animals & Nature';

  @override
  String get value_carousel_2_category_4 => 'Professions & Roles';

  @override
  String get value_carousel_2_category_5 => 'Cause & Effect';

  @override
  String get value_carousel_2_category_6 => 'And more...';

  @override
  String get value_carousel_2_feature_1_title => '100% sans publicité';

  @override
  String get value_carousel_2_feature_1_description =>
      'Pas de publicités, pas de distractions, pas de contenu inapproprié';

  @override
  String get value_carousel_2_feature_2_title => 'Adapté à l\'âge';

  @override
  String get value_carousel_2_feature_2_description =>
      'Contenu conçu spécifiquement pour les 2-5 ans';

  @override
  String get value_carousel_2_feature_3_title => 'Apprentissage actif';

  @override
  String get value_carousel_2_feature_3_description =>
      'Activités engageantes, pas de visionnage passif';

  @override
  String get value_carousel_3_headline =>
      'Voyez leurs progrès et gardez-les engagés';

  @override
  String get value_carousel_3_trust_element_1 => 'Ad-free learning environment';

  @override
  String get value_carousel_3_trust_element_2 => 'No data collection';

  @override
  String get value_carousel_3_trust_element_3 =>
      'Based on child development expert recommendations';

  @override
  String get value_carousel_3_trust_element_4 => 'Safe for young children';

  @override
  String get value_carousel_3_feature_1_title => 'Suivi des progrès';

  @override
  String get value_carousel_3_feature_1_description =>
      'Voyez quelles compétences votre enfant développe';

  @override
  String get value_carousel_3_feature_2_title => 'Récompenses de réalisation';

  @override
  String get value_carousel_3_feature_2_description =>
      'Gagnez des étoiles et des badges qui motivent l\'apprentissage continu';

  @override
  String get value_carousel_3_feature_3_title => 'Expérience personnalisée';

  @override
  String get value_carousel_3_feature_3_description =>
      'Jeux qui s\'adaptent au niveau de compétence de votre enfant';

  @override
  String get summary_headline_new =>
      'Parfait ! Voici votre parcours d\'apprentissage personnalisé';

  @override
  String get summary_learning_path_title => 'Ce que votre enfant apprendra :';

  @override
  String get summary_skill_cognitive => 'Développement cognitif';

  @override
  String summary_skill_cognitive_desc(int age) {
    return 'Jeux de mémoire et activités de résolution de problèmes parfaits pour l\'âge de $age ans';
  }

  @override
  String get summary_skill_visual => 'Perception visuelle';

  @override
  String summary_skill_visual_desc(int age) {
    return 'Jeux de reconnaissance des formes et conscience spatiale pour les enfants de $age ans';
  }

  @override
  String get summary_skill_exploration => 'Exploration et découverte';

  @override
  String summary_skill_exploration_desc(int age) {
    return 'Jeux interactifs qui encouragent la curiosité à $age ans';
  }

  @override
  String get summary_next_step =>
      'Suivant : Essayez Premium gratuitement pendant 7 jours pour débloquer tous les jeux !';

  @override
  String get trial_badge => 'Essai gratuit de 7 jours';

  @override
  String get trial_headline => 'Essayez Premium gratuitement pendant 7 jours';

  @override
  String get trial_description =>
      'Obtenez un accès complet à tous les jeux et fonctionnalités premium. Annulez à tout moment pendant votre essai - aucun frais si vous annulez avant la fin.';

  @override
  String get trial_feature_1_title => 'Tous les jeux premium';

  @override
  String get trial_feature_1_description =>
      'Accédez à tous les jeux d\'apprentissage de notre bibliothèque';

  @override
  String get trial_feature_2_title => 'Expérience sans publicité';

  @override
  String get trial_feature_2_description =>
      'Environnement d\'apprentissage sûr sans publicités';

  @override
  String get trial_feature_3_title => 'Suivi des progrès';

  @override
  String get trial_feature_3_description =>
      'Voyez le développement et les réalisations de votre enfant';

  @override
  String get trial_feature_4_title => 'Mises à jour régulières';

  @override
  String get trial_feature_4_description =>
      'Nouveaux jeux et fonctionnalités ajoutés régulièrement';

  @override
  String get trial_how_it_works_title => 'Comment ça marche :';

  @override
  String get trial_step_1 =>
      'Commencez votre essai gratuit de 7 jours aujourd\'hui';

  @override
  String get trial_step_2 =>
      'Profitez d\'un accès complet à toutes les fonctionnalités premium';

  @override
  String get trial_step_3 =>
      'Annulez à tout moment - aucun frais avant la fin de l\'essai';

  @override
  String get trial_cta => 'Commencer mon essai gratuit';

  @override
  String get trial_disclaimer =>
      'Gratuit pendant 7 jours, puis tarif du forfait sélectionné. Annulez à tout moment.';

  @override
  String get notification_permission_headline =>
      'Restez connecté à l\'apprentissage de votre enfant';

  @override
  String get notification_permission_description =>
      'Recevez des rappels utiles et célébrez les étapes importantes avec votre enfant. Nous vous enverrons des notifications opportunes sur les réalisations et les opportunités d\'apprentissage.';

  @override
  String get notification_benefit_1_title => 'Rappels d\'essai';

  @override
  String get notification_benefit_1_description =>
      'Soyez averti avant la fin de votre essai pour ne jamais perdre l\'accès';

  @override
  String get notification_benefit_2_title => 'Étapes d\'apprentissage';

  @override
  String get notification_benefit_2_description =>
      'Célébrez quand votre enfant atteint de nouvelles réalisations';

  @override
  String get notification_benefit_3_title => 'Conseils d\'engagement';

  @override
  String get notification_benefit_3_description =>
      'Obtenez des suggestions pour garder l\'apprentissage amusant et engageant';

  @override
  String get notification_privacy_note =>
      'Nous respectons votre vie privée. Vous pouvez désactiver les notifications à tout moment dans les paramètres.';

  @override
  String get notification_enable_button => 'Activer les notifications';

  @override
  String get notification_maybe_later => 'Peut-être plus tard';

  @override
  String get subscription_management_title => 'Gérer l\'abonnement';

  @override
  String get subscription_status_active => 'Premium actif';

  @override
  String get subscription_status_active_description =>
      'Vous avez un accès complet à toutes les fonctionnalités premium';

  @override
  String get subscription_status_inactive => 'Version gratuite';

  @override
  String get subscription_status_inactive_description =>
      'Passez à premium pour un accès complet à tous les jeux';

  @override
  String get subscription_actions_title => 'Actions';

  @override
  String get subscription_restore_title => 'Restaurer les achats';

  @override
  String get subscription_restore_description =>
      'Déjà abonné sur un autre appareil ? Restaurez vos achats ici.';

  @override
  String get subscription_restore_button => 'Restaurer';

  @override
  String get subscription_manage_title => 'Gérez votre abonnement';

  @override
  String get subscription_manage_description =>
      'Consultez, modifiez ou annulez votre abonnement via votre compte de la boutique d\'applications.';

  @override
  String get subscription_manage_button =>
      'Ouvrir les paramètres d\'abonnement';

  @override
  String get subscription_help_title => 'Aide et informations';

  @override
  String get subscription_cancel_title => 'Comment annuler';

  @override
  String get subscription_cancel_description =>
      'Vous pouvez annuler votre abonnement à tout moment via les paramètres de votre compte App Store ou Google Play. Vous conserverez l\'accès jusqu\'à la fin de votre période de facturation.';

  @override
  String get subscription_payment_failure_title => 'Problèmes de paiement';

  @override
  String get subscription_payment_failure_description =>
      'Si votre paiement échoue, mettez à jour votre méthode de paiement dans votre compte de la boutique d\'applications. Nous réessayerons automatiquement le paiement.';

  @override
  String get next_button => 'Suivant';

  @override
  String get back_button => 'Retour';

  @override
  String get onboarding_priority_question => 'What\'s most important to you?';

  @override
  String get onboarding_priority_1 => 'Educational screen time';

  @override
  String get onboarding_priority_1_sub => 'Learning while playing';

  @override
  String get onboarding_priority_2 => 'School readiness';

  @override
  String get onboarding_priority_2_sub =>
      'Preparing for preschool/kindergarten';

  @override
  String get onboarding_priority_3 => 'Keeping them engaged';

  @override
  String get onboarding_priority_3_sub => 'Productive, happy screen time';

  @override
  String get onboarding_priority_4 => 'Learning through play';

  @override
  String get onboarding_priority_4_sub => 'Fun that builds skills';

  @override
  String get onboarding_transition_message => 'Thank you for sharing!';

  @override
  String onboarding_transition_submessage(int age) {
    return 'We\'re creating the perfect experience for your $age-year-old...';
  }

  @override
  String summary_result_headline(int age) {
    return 'Perfect for your $age-year old!';
  }

  @override
  String get summary_card_1_title => 'What They\'ll Learn:';

  @override
  String get summary_card_1_point_1 =>
      'Pattern recognition through 15 different games';

  @override
  String get summary_card_1_point_2 => 'Visual discrimination skills';

  @override
  String get summary_card_1_point_3 => 'Logical thinking and problem-solving';

  @override
  String get summary_card_1_point_4 =>
      'Real-world connections (animals, professions, nature)';

  @override
  String get summary_card_2_title => 'Why It Works:';

  @override
  String get summary_card_2_point_1 => 'Active learning beats passive watching';

  @override
  String get summary_card_2_point_2 => 'Immediate feedback keeps them engaged';

  @override
  String get summary_card_2_point_3 => 'Variety prevents boredom';

  @override
  String get summary_card_2_point_4 => 'Aligned with screen time guidelines';

  @override
  String get summary_cta => 'See what\'s included';

  @override
  String get free_trial_headline => 'Try 5 games free, unlock 10 more';

  @override
  String get free_trial_free_section => 'Start with 5 free matching games';

  @override
  String get free_trial_free_point_1 => 'Shapes, colors, and more';

  @override
  String get free_trial_free_point_2 => 'No time limit';

  @override
  String get free_trial_free_point_3 => 'No credit card needed';

  @override
  String get free_trial_premium_section =>
      'Unlock all 15 games with a subscription';

  @override
  String get free_trial_premium_point_1 => 'Full game library';

  @override
  String get free_trial_premium_point_2 => 'New learning categories';

  @override
  String get free_trial_premium_point_3 => 'Cancel anytime';

  @override
  String get free_trial_bottom_message =>
      'Try the free games first—unlock more anytime!';

  @override
  String get free_trial_cta_primary => 'Start with free games';

  @override
  String get free_trial_cta_secondary => 'See subscription options';

  @override
  String get paywall_section1_headline =>
      'Ready to unlock all 15 learning games?';

  @override
  String get paywall_section1_feature_1 => '15 educational matching games';

  @override
  String get paywall_section1_feature_2 =>
      'Shape, color, pattern & logic games';

  @override
  String get paywall_section1_feature_3 =>
      'Animals, professions, nature themes';

  @override
  String get paywall_section1_feature_4 => 'Cause-and-effect learning';

  @override
  String get paywall_section1_feature_5 => 'No ads, no distractions';

  @override
  String get paywall_section1_feature_6 => 'Teacher Approved';

  @override
  String get paywall_section2_badge => 'Teacher Approved';

  @override
  String get paywall_section2_text =>
      'Educators recognize Brainy Bunny for its developmental approach to early learning.';

  @override
  String get paywall_section3_weekly_title => 'Weekly';

  @override
  String get paywall_section3_weekly_subtext => 'Try it out';

  @override
  String get paywall_section3_weekly_feature => 'Cancel anytime';

  @override
  String get paywall_section3_weekly_button => 'Subscribe';

  @override
  String get paywall_section3_yearly_title => 'Yearly';

  @override
  String get paywall_section3_yearly_badge => 'BEST VALUE - Save 60%';

  @override
  String get paywall_section3_yearly_highlight => '7-day FREE trial';

  @override
  String paywall_section3_yearly_breakdown(String monthlyEquivalent) {
    return 'Just $monthlyEquivalent/month';
  }

  @override
  String paywall_section3_yearly_fine_print(String yearlyPrice) {
    return 'Then $yearlyPrice annually';
  }

  @override
  String get paywall_section3_yearly_feature =>
      'Cancel during trial - no charge';

  @override
  String get paywall_section3_yearly_button => 'Start FREE trial';

  @override
  String get paywall_section3_monthly_title => 'Monthly';

  @override
  String get paywall_section3_monthly_subtext => 'Flexible option';

  @override
  String get paywall_section3_monthly_feature => 'Cancel anytime';

  @override
  String get paywall_section3_monthly_button => 'Subscribe';

  @override
  String get paywall_trust_element_1 => 'Secure payment';

  @override
  String get paywall_trust_element_2 => 'Cancel anytime during trial';

  @override
  String get paywall_trust_element_3 =>
      'Manage subscription in App Store/Play Store';

  @override
  String get paywall_trust_element_4 =>
      'Charged only after trial ends (for yearly)';

  @override
  String get paywall_disclaimer =>
      'You won\'t be charged during your 7-day trial. Cancel anytime in your device settings.';

  @override
  String get paywall_continue_free_link => 'Continue with free games';

  @override
  String get parent_gate_title => 'Parent Verification Required';

  @override
  String get parent_gate_instruction =>
      'This purchase requires an adult. Please solve this problem:';

  @override
  String get parent_gate_input_placeholder => 'Enter answer';

  @override
  String get parent_gate_cancel => 'Cancel';

  @override
  String get parent_gate_verify => 'Verify';

  @override
  String get parent_gate_error => 'Incorrect answer. Please try again.';

  @override
  String get notification_type_1 => 'New game unlocks';

  @override
  String get notification_type_2 => 'Learning streak milestones';

  @override
  String get notification_type_3 => 'Trial ending reminder (if applicable)';

  @override
  String get notification_type_4 => 'Daily learning encouragement';

  @override
  String get notification_trial_callout =>
      'We\'ll remind you 2 days before your trial ends, so you\'re never surprised.';

  @override
  String get notification_benefit_1 => 'Stay consistent with learning';

  @override
  String get notification_benefit_2 => 'Never miss trial deadline';

  @override
  String get notification_benefit_3 => 'Celebrate progress together';

  @override
  String get notification_cta_enable => 'Enable notifications';

  @override
  String get notification_cta_skip => 'Not now';

  @override
  String get subscription_error_loading_title => 'Loading Subscriptions';

  @override
  String get subscription_error_loading_description =>
      'Please wait while we load subscription options...';

  @override
  String get subscription_error_offline_title => 'No Internet Connection';

  @override
  String get subscription_error_offline_description =>
      'Please check your internet connection and try again. You need to be online to subscribe.';

  @override
  String get subscription_error_not_available_title =>
      'Subscriptions Not Available';

  @override
  String get subscription_error_not_available_description =>
      'In-app purchases are not available on this device. Please try again later or contact support.';

  @override
  String get subscription_error_products_not_found_title =>
      'Products Not Available';

  @override
  String get subscription_error_products_not_found_description =>
      'We couldn\'t load subscription products from the store. Please try again later.';

  @override
  String get subscription_error_unknown_title => 'Something Went Wrong';

  @override
  String get subscription_error_unknown_description =>
      'An unexpected error occurred. Please try again.';

  @override
  String get subscription_error_retry => 'Try Again';

  @override
  String get subscription_error_continue_free => 'Continue with Free Games';

  @override
  String get subscription_loading => 'Loading...';

  @override
  String get goal_preschool_title => 'Prepare for preschool/kindergarten';

  @override
  String get goal_preschool_description => 'Building readiness skills';

  @override
  String get goal_cognitive_title => 'Develop cognitive abilities';

  @override
  String get goal_cognitive_description =>
      'Pattern recognition & problem-solving';

  @override
  String get goal_replace_screen_time_title => 'Replace passive screen time';

  @override
  String get goal_replace_screen_time_description =>
      'Active learning instead of videos';

  @override
  String get goal_keep_engaged_title => 'Keep them engaged & learning';

  @override
  String get goal_keep_engaged_description => 'Fun that actually builds skills';

  @override
  String get summary_age2_headline => 'Perfect for your 2-year-old explorer';

  @override
  String get summary_age2_card1_title => 'What they\'ll learn';

  @override
  String get summary_age2_card1_point1 =>
      'Basic shape recognition (circles, squares, triangles)';

  @override
  String get summary_age2_card1_point2 => 'Simple color matching';

  @override
  String get summary_age2_card1_point3 =>
      'Hand-eye coordination through drag-and-drop';

  @override
  String get summary_age2_card1_point4 => 'Cause and effect understanding';

  @override
  String get summary_age2_card2_title => 'Why it works for age 2';

  @override
  String get summary_age2_card2_point1 =>
      'Extra-large pieces perfect for tiny fingers';

  @override
  String get summary_age2_card2_point2 => 'Simple 1-2 pair matching to start';

  @override
  String get summary_age2_card2_point3 =>
      'Instant positive feedback builds confidence';

  @override
  String get summary_age2_card2_point4 =>
      'Sessions designed for 5-10 minute attention spans';

  @override
  String get summary_age3_headline => 'Designed for your curious 3-year-old';

  @override
  String get summary_age3_card1_title => 'What they\'ll learn';

  @override
  String get summary_age3_card1_point1 =>
      'Advanced shape recognition and sorting';

  @override
  String get summary_age3_card1_point2 => 'Pattern identification';

  @override
  String get summary_age3_card1_point3 => 'Color mixing and matching concepts';

  @override
  String get summary_age3_card1_point4 => 'Early problem-solving skills';

  @override
  String get summary_age3_card2_title => 'Why it works for age 3';

  @override
  String get summary_age3_card2_point1 =>
      'Progressive difficulty grows with their skills';

  @override
  String get summary_age3_card2_point2 =>
      'Builds on preschool learning concepts';

  @override
  String get summary_age3_card2_point3 =>
      'Celebrates small wins to boost motivation';

  @override
  String get summary_age3_card2_point4 => 'Perfect for emerging independence';

  @override
  String get summary_age4_headline => 'Tailored for your smart 4-year-old';

  @override
  String get summary_age4_card1_title => 'What they\'ll learn';

  @override
  String get summary_age4_card1_point1 => 'Complex pattern recognition';

  @override
  String get summary_age4_card1_point2 =>
      'Categorical thinking (animals, professions, objects)';

  @override
  String get summary_age4_card1_point3 => 'Spatial reasoning and relationships';

  @override
  String get summary_age4_card1_point4 =>
      'Pre-reading visual discrimination skills';

  @override
  String get summary_age4_card2_title => 'Why it works for age 4';

  @override
  String get summary_age4_card2_point1 =>
      'Challenges that match pre-K curriculum';

  @override
  String get summary_age4_card2_point2 =>
      'Multiple rounds build sustained focus';

  @override
  String get summary_age4_card2_point3 =>
      'Vocabulary expansion through themed games';

  @override
  String get summary_age4_card2_point4 => 'Prepares for kindergarten readiness';

  @override
  String get summary_age5_headline => 'Engaging games for your 5+ year-old';

  @override
  String get summary_age5_card1_title => 'What they\'ll learn';

  @override
  String get summary_age5_card1_point1 => 'Advanced categorization and sorting';

  @override
  String get summary_age5_card1_point2 => 'Abstract pattern completion';

  @override
  String get summary_age5_card1_point3 => 'Critical thinking and strategy';

  @override
  String get summary_age5_card1_point4 => 'Visual memory enhancement';

  @override
  String get summary_age5_card2_title => 'Why it works for age 5+';

  @override
  String get summary_age5_card2_point1 =>
      'Kindergarten-level cognitive challenges';

  @override
  String get summary_age5_card2_point2 =>
      'Builds confidence for school success';

  @override
  String get summary_age5_card2_point3 =>
      'Reinforces classroom learning at home';

  @override
  String get summary_age5_card2_point4 => 'Keeps advanced learners engaged';

  @override
  String get parental_gate_overlay_title => 'Parental Verification';

  @override
  String get parental_gate_overlay_instruction =>
      'Please solve this problem to continue:';

  @override
  String get error_purchase_verification_failed =>
      'Purchase verification failed. Please restart the app.';

  @override
  String get paywall_step2_badge_save => 'Save 60%';

  @override
  String get paywall_step2_badge_trial => '7-Day Trial';

  @override
  String get paywall_step2_yearly_title => 'Yearly';

  @override
  String paywall_step2_yearly_per_month(String price) {
    return 'Just $price/month';
  }

  @override
  String get paywall_step2_yearly_savings => 'Save 63% • Try free for 7 days';

  @override
  String get paywall_step2_yearly_feature1 => 'No charge for 7 days';

  @override
  String get paywall_step2_yearly_feature2 => 'Cancel anytime during trial';

  @override
  String get paywall_step2_yearly_feature3 =>
      'We\'ll remind you 2 days before trial ends';

  @override
  String get paywall_step2_yearly_feature4 => 'Full access to all 15 games';

  @override
  String get paywall_step2_yearly_button => 'Start FREE Trial';

  @override
  String get paywall_step2_monthly_title => 'Monthly';

  @override
  String paywall_step2_monthly_per_week(String price) {
    return '$price/week';
  }

  @override
  String get paywall_step2_monthly_savings => 'Flexible monthly plan';

  @override
  String get paywall_step2_monthly_feature1 => 'Cancel anytime';

  @override
  String get paywall_step2_monthly_feature2 => 'Full access to all 15 games';

  @override
  String get paywall_step2_monthly_button => 'Subscribe Monthly';

  @override
  String get paywall_step2_weekly_title => 'Weekly';

  @override
  String get paywall_step2_weekly_savings => 'Try for just one week';

  @override
  String get paywall_step2_weekly_feature1 => 'Cancel anytime';

  @override
  String get paywall_step2_weekly_feature2 => 'Full access to all 15 games';

  @override
  String get paywall_step2_weekly_button => 'Subscribe Weekly';

  @override
  String locked_game_headline_personalized(String childName) {
    return 'Unlock All Games for $childName!';
  }

  @override
  String get locked_game_headline_generic => 'Unlock All 15 Educational Games!';

  @override
  String locked_game_card1_title_age(int age) {
    return 'Perfect for Your $age-Year-Old';
  }

  @override
  String get locked_game_card1_title_generic => 'Age-Appropriate Learning';

  @override
  String get locked_game_card2_title => 'Building Cognitive Abilities';

  @override
  String locked_game_card3_title(int count) {
    return '$count More Games to Explore';
  }

  @override
  String get locked_game_card3_subtitle =>
      'Unlock the full collection of educational games';

  @override
  String get locked_game_age_skill_1_generic => 'Shape and color recognition';

  @override
  String get locked_game_age_skill_2_generic => 'Problem-solving abilities';

  @override
  String get locked_game_age_skill_3_generic => 'Hand-eye coordination';

  @override
  String get locked_game_age_skill_4_generic => 'Memory and focus';

  @override
  String get locked_game_age_skill_1_age2 => 'Basic shape recognition';

  @override
  String get locked_game_age_skill_2_age2 => 'Simple color matching';

  @override
  String get locked_game_age_skill_3_age2 => 'Hand-eye coordination';

  @override
  String get locked_game_age_skill_4_age2 => 'Cause and effect understanding';

  @override
  String get locked_game_age_skill_1_age3 => 'Advanced shape sorting';

  @override
  String get locked_game_age_skill_2_age3 => 'Pattern identification';

  @override
  String get locked_game_age_skill_3_age3 => 'Color mixing concepts';

  @override
  String get locked_game_age_skill_4_age3 => 'Early problem-solving';

  @override
  String get locked_game_age_skill_1_age4 => 'Complex pattern recognition';

  @override
  String get locked_game_age_skill_2_age4 => 'Categorical thinking';

  @override
  String get locked_game_age_skill_3_age4 => 'Spatial reasoning';

  @override
  String get locked_game_age_skill_4_age4 => 'Pre-reading visual skills';

  @override
  String get locked_game_age_skill_1_age5 => 'Advanced categorization';

  @override
  String get locked_game_age_skill_2_age5 => 'Abstract pattern completion';

  @override
  String get locked_game_age_skill_3_age5 => 'Critical thinking';

  @override
  String get locked_game_age_skill_4_age5 => 'Visual memory enhancement';

  @override
  String get locked_game_card2_content_default =>
      'Develop essential cognitive skills through play';

  @override
  String get locked_game_card2_content_school =>
      'Build skills for preschool and kindergarten success';

  @override
  String get locked_game_card2_content_cognitive =>
      'Enhance memory, focus, and problem-solving abilities';

  @override
  String get locked_game_card2_content_screentime =>
      'Quality educational content that parents can feel good about';

  @override
  String get locked_game_card2_content_engagement =>
      'Keep your child engaged with fun, educational activities';
}

/// The translations for French, as used in Canada (`fr_CA`).
class AppLocalizationsFrCa extends AppLocalizationsFr {
  AppLocalizationsFrCa() : super('fr_CA');

  @override
  String get onboarding_welcome_headline =>
      'Aidez votre enfant à apprendre et à grandir';

  @override
  String get onboarding_welcome_subheading =>
      'Jeux éducatifs conçus pour les tout-petits de 2 à 5 ans';

  @override
  String get onboarding_welcome_cta => 'Commencer';

  @override
  String get onboarding_name_headline => 'Personnalisons votre expérience';

  @override
  String get onboarding_name_hint => 'Votre nom';

  @override
  String get onboarding_name_mom => 'Maman';

  @override
  String get onboarding_name_dad => 'Papa';

  @override
  String get onboarding_name_parent => 'Parent';

  @override
  String onboarding_name_greeting(String name) {
    return 'Génial, $name !';
  }

  @override
  String get onboarding_child_age_headline => 'Quel âge a votre enfant ?';

  @override
  String get onboarding_age_2 => '2 ans';

  @override
  String get onboarding_age_3 => '3 ans';

  @override
  String get onboarding_age_4 => '4 ans';

  @override
  String get onboarding_age_5_plus => '5 ans et plus';

  @override
  String get onboarding_philosophy_headline =>
      'Transformez le temps d\'écran en temps d\'apprentissage';

  @override
  String get onboarding_philosophy_aap =>
      'Conforme aux recommandations de l\'AAP sur le temps d\'écran';

  @override
  String get onboarding_philosophy_learning =>
      'Transformez le temps d\'écran en expériences d\'apprentissage significatives';

  @override
  String get onboarding_philosophy_skills =>
      'Développez les compétences cognitives par l\'apprentissage par le jeu';

  @override
  String get onboarding_transition_to_games =>
      'Jouons ! Tournez votre appareil →';

  @override
  String get onboarding_transition_from_games =>
      'Excellent apprentissage ! Finissons →';

  @override
  String get demo_game_1_skill =>
      'Appariement visuel et reconnaissance des formes';

  @override
  String get demo_game_1_science =>
      'Associer les animaux à leurs silhouettes développe la discrimination visuelle - la capacité de remarquer les différences entre des formes similaires. Cette compétence est essentielle pour la reconnaissance des lettres et la lecture.';

  @override
  String get demo_game_1_citation =>
      'Bornstein (1985) - Traitement visuel et pensée catégorielle';

  @override
  String get demo_game_1_badge => 'Détective des formes';

  @override
  String get demo_game_2_skill => 'Mémoire visuelle et attention';

  @override
  String get demo_game_2_science =>
      'Trouver des paires assorties renforce la mémoire de travail - la capacité de votre enfant à retenir et manipuler l\'information. Cela soutient directement la résolution de problèmes mathématiques et le suivi d\'instructions en plusieurs étapes.';

  @override
  String get demo_game_2_citation =>
      'Gopnik & Meltzoff (1987) - Catégorisation et flexibilité cognitive';

  @override
  String get demo_game_2_badge => 'Maître de la mémoire';

  @override
  String get demo_game_3_skill => 'Association logique et catégorisation';

  @override
  String get demo_game_3_science =>
      'Connecter les objets à leurs utilisateurs enseigne la catégorisation et la pensée logique. Votre enfant apprend que les choses vont ensemble pour des raisons - une étape clé pour comprendre la cause et l\'effet.';

  @override
  String get demo_game_3_citation =>
      'Piaget (1952) - Développement cognitif préopératoire';

  @override
  String get demo_game_3_badge => 'Étoile de la logique';

  @override
  String get demo_game_4_skill => 'Reconnaissance des motifs et appariement';

  @override
  String get demo_game_4_science =>
      'Associer des motifs développe la reconnaissance des motifs - la capacité de voir les relations entre les choses. Cette compétence prédit fortement le succès en mathématiques et aide les enfants à comprendre \'même\' vs \'différent\'.';

  @override
  String get demo_game_4_citation =>
      'Rittle-Johnson et al. (2019) - Compétences de motifs et mathématiques';

  @override
  String get demo_game_4_badge => 'Pro des motifs';

  @override
  String get demo_game_5_skill =>
      'Pensée symbolique et connexions avec le monde réel';

  @override
  String get demo_game_5_science =>
      'Connecter les outils aux carrières développe la pensée symbolique - comprendre qu\'une chose peut représenter une autre. Cette pensée abstraite est essentielle pour le langage, les mathématiques et l\'imagination.';

  @override
  String get demo_game_5_citation =>
      'Vygotsky (1978) - Représentation symbolique dans le développement cognitif';

  @override
  String get demo_game_5_badge => 'Explorateur du monde';

  @override
  String get demo_game_next => 'Jeu suivant';

  @override
  String onboarding_summary_headline(String name) {
    return 'Votre parcours jusqu\'à présent, $name';
  }

  @override
  String onboarding_summary_age(int age) {
    return 'Parfait pour les enfants de $age ans';
  }

  @override
  String get onboarding_summary_skills => '5 compétences pratiquées';

  @override
  String get onboarding_summary_screen_time =>
      'Approche du temps d\'écran sain alignée sur les directives de l\'AAP';

  @override
  String get onboarding_summary_cta => 'Voir votre plan personnalisé';

  @override
  String get trust_headline => 'Approuvé par les parents et les éducateurs';

  @override
  String get trust_approved => 'Approuvé par les enseignants';

  @override
  String get trust_cta => 'Débloquez l\'expérience d\'apprentissage complète';

  @override
  String get paywall_headline => 'Débloquez les 15 jeux d\'apprentissage';

  @override
  String get paywall_subheadline =>
      'Continuez le parcours d\'apprentissage de votre enfant';

  @override
  String get paywall_feature_games =>
      '15 jeux éducatifs ciblant les compétences clés';

  @override
  String get paywall_feature_progress => 'Suivi des progrès de votre enfant';

  @override
  String get paywall_feature_ad_free => 'Environnement sûr et sans publicité';

  @override
  String get paywall_feature_new_games => 'Nouveaux jeux ajoutés chaque mois';

  @override
  String get paywall_feature_ages => 'Conçu pour les 2-5 ans';

  @override
  String get paywall_trial_headline =>
      'Essayez toutes les fonctionnalités GRATUITEMENT pendant 7 jours';

  @override
  String get paywall_trial_price => 'Puis seulement 0,87 €/semaine';

  @override
  String get paywall_trial_today => 'Aujourd\'hui : Accès complet débloqué';

  @override
  String get paywall_trial_day5 => 'Jour 5 : Nous vous enverrons un rappel';

  @override
  String get paywall_trial_day7 =>
      'Jour 7 : La facturation commence (annulez à tout moment avant)';

  @override
  String get paywall_trial_no_payment => '✓ Aucun paiement dû maintenant';

  @override
  String get paywall_trial_cta => 'Commencer l\'essai gratuit →';

  @override
  String get paywall_weekly => 'Hebdomadaire';

  @override
  String get paywall_monthly => 'Mensuel';

  @override
  String get paywall_yearly => 'Annuel';

  @override
  String get paywall_save_60 => 'Économisez 60%';

  @override
  String get paywall_most_popular => 'Choix le plus populaire';

  @override
  String get paywall_cancel_anytime =>
      'Annulez à tout moment depuis les paramètres de votre appareil';

  @override
  String get paywall_restore => 'Restaurer les achats';

  @override
  String get subscription_premium => 'Premium';

  @override
  String get subscription_trial => 'Essai de 7 jours';

  @override
  String get subscription_lifetime => 'Accès à vie';

  @override
  String get subscription_expired => 'Abonnement terminé';

  @override
  String get subscription_manage => 'Gérer l\'abonnement';

  @override
  String get subscription_cancel => 'Annuler l\'abonnement';

  @override
  String get subscription_change_plan => 'Changer de forfait';

  @override
  String get continue_button => 'Continuer';

  @override
  String get skip_button => 'Passer';

  @override
  String get close_button => 'Fermer';

  @override
  String get loading => 'Chargement...';

  @override
  String get error_purchase_failed => 'Achat échoué';

  @override
  String get error_purchase_failed_message =>
      'Nous n\'avons pas pu finaliser votre achat. Veuillez réessayer.';

  @override
  String get error_restore_failed => 'Aucun achat trouvé';

  @override
  String get error_restore_failed_message =>
      'Nous n\'avons trouvé aucun achat précédent. Si vous pensez qu\'il s\'agit d\'une erreur, veuillez contacter le support.';

  @override
  String get error_network => 'Erreur réseau';

  @override
  String get error_network_message =>
      'Veuillez vérifier votre connexion Internet et réessayer.';

  @override
  String get summary_headline => 'Progrès incroyables !';

  @override
  String summary_message_age_2(Object name) {
    return 'Excellent travail, $name ! Votre petit construit des compétences importantes par le jeu. À 2 ans, chaque association qu\'ils font renforce leur reconnaissance visuelle et leurs capacités de résolution de problèmes.';
  }

  @override
  String summary_message_age_3(Object name) {
    return 'Merveilleux, $name ! Votre enfant développe des compétences cognitives cruciales. À 3 ans, ces activités améliorent leur mémoire, leur concentration et leur pensée logique.';
  }

  @override
  String summary_message_age_4(Object name) {
    return 'Excellent travail, $name ! Votre enfant de 4 ans maîtrise la résolution avancée de problèmes. Ces jeux le préparent à réussir à la maternelle.';
  }

  @override
  String summary_message_age_5_plus(Object name) {
    return 'Fantastique, $name ! Votre enfant excelle dans la pensée complexe. Ces compétences lui donneront une base solide pour l\'école.';
  }

  @override
  String get summary_badges_earned => 'Badges gagnés';

  @override
  String get summary_badges => 'Badges';

  @override
  String get summary_games => 'Jeux';

  @override
  String get summary_skills => 'Compétences';

  @override
  String get trust_aap_description =>
      'Notre approche éducative s\'aligne sur les directives de l\'AAP pour un temps d\'écran sain et le développement de la petite enfance.';

  @override
  String get trust_research_title => 'Programme soutenu par la recherche';

  @override
  String get trust_research_description =>
      'Chaque jeu est conçu sur la base d\'études évaluées par des pairs sur le développement cognitif, avec des méthodes éprouvées pour améliorer l\'apprentissage chez les enfants de 2 à 5 ans.';

  @override
  String get trust_testimonial_1_name => 'Sarah M., Mère d\'un enfant de 3 ans';

  @override
  String get trust_testimonial_1_quote =>
      '\"Ma fille est passée de la difficulté avec les formes à les identifier avec confiance partout. Les progrès en seulement 2 semaines m\'ont étonné !\"';

  @override
  String get trust_testimonial_2_name =>
      'Michael T., Père d\'un enfant de 4 ans';

  @override
  String get trust_testimonial_2_quote =>
      '\"Enfin, du temps d\'écran dont je me sens bien ! Mon fils apprend tout en s\'amusant, et je peux voir de réelles améliorations cognitives.\"';

  @override
  String get trust_downloads_title => 'Rejoignez plus de 100 000 familles';

  @override
  String get trust_downloads_description =>
      'Approuvé par des parents dans plus de 50 pays pour donner à leurs enfants une longueur d\'avance dans l\'apprentissage.';

  @override
  String get trust_cta_headline =>
      'Prêt à débloquer le plein potentiel de votre enfant ?';

  @override
  String get trust_cta_button => 'Commencer l\'essai gratuit';

  @override
  String get paywall_premium_badge => 'Accès Premium';

  @override
  String get paywall_step1_headline =>
      'Débloquez le plein potentiel de votre enfant';

  @override
  String get paywall_value_1_title => '15 jeux premium';

  @override
  String get paywall_value_1_description =>
      'Accédez à tous les jeux éducatifs conçus pour les 2-5 ans, couvrant les formes, les couleurs, les nombres, la logique et plus encore';

  @override
  String get paywall_value_2_title => 'Suivi des progrès';

  @override
  String get paywall_value_2_description =>
      'Analyses détaillées montrant le développement de votre enfant et l\'amélioration des compétences au fil du temps';

  @override
  String get paywall_value_3_title => 'Apprentissage personnalisé';

  @override
  String get paywall_value_3_description =>
      'Les jeux s\'adaptent à l\'âge et au niveau de compétence de votre enfant pour un apprentissage optimal';

  @override
  String get paywall_value_4_title => 'Nouveau contenu mensuel';

  @override
  String get paywall_value_4_description =>
      'Mises à jour régulières avec de nouveaux jeux et activités pour garder l\'apprentissage frais et engageant';

  @override
  String get paywall_step1_cta => 'Voir les forfaits';

  @override
  String get paywall_secure_payment => 'Traitement de paiement sécurisé';

  @override
  String get paywall_trial_badge => 'Essai gratuit de 7 jours';

  @override
  String get paywall_step2_headline => 'Choisissez votre forfait';

  @override
  String get paywall_step2_subheadline =>
      'Commencez votre essai gratuit de 7 jours. Annulez à tout moment.';

  @override
  String get paywall_plan_best_value => 'Meilleur rapport qualité-prix';

  @override
  String get paywall_plan_yearly_title => 'Annuel';

  @override
  String get paywall_plan_yearly_period => '/an';

  @override
  String get paywall_plan_yearly_per_month => 'Seulement 3,33 €/mois';

  @override
  String get paywall_plan_yearly_savings =>
      'Économisez 63% par rapport au mensuel';

  @override
  String get paywall_plan_monthly_title => 'Mensuel';

  @override
  String get paywall_plan_monthly_period => '/mois';

  @override
  String get paywall_plan_weekly_title => 'Hebdomadaire';

  @override
  String get paywall_plan_weekly_period => '/semaine';

  @override
  String get paywall_plan_weekly_note => 'Pour un accès à court terme';

  @override
  String get paywall_trial_reminder =>
      'Votre essai gratuit commence aujourd\'hui. Vous ne serez pas facturé avant le jour 8. Annulez à tout moment avant sans frais.';

  @override
  String get paywall_step2_cta => 'Continuer';

  @override
  String get paywall_terms =>
      'En continuant, vous acceptez nos Conditions d\'utilisation et notre Politique de confidentialité';

  @override
  String get paywall_urgency_text => 'Offre à durée limitée';

  @override
  String get paywall_step3_headline => 'Vous n\'êtes plus qu\'à un pas !';

  @override
  String get paywall_step3_included_title => 'Tout ce que vous obtenez :';

  @override
  String get paywall_included_1 => 'Les 15 jeux éducatifs premium';

  @override
  String get paywall_included_2 =>
      'Parcours d\'apprentissage personnalisés pour votre enfant';

  @override
  String get paywall_included_3 => 'Suivi détaillé des progrès et informations';

  @override
  String get paywall_included_4 => 'Contenu et activités nouveaux chaque mois';

  @override
  String get paywall_included_5 =>
      'Expérience sans publicité pour un apprentissage concentré';

  @override
  String get paywall_included_6 =>
      'Mode hors ligne - apprenez n\'importe où, n\'importe quand';

  @override
  String get paywall_guarantee_title => 'Garantie 100% sans risque';

  @override
  String get paywall_guarantee_text =>
      'Essayez-le gratuitement pendant 7 jours. Si vous n\'êtes pas complètement satisfait, annulez avant la fin de l\'essai et ne payez rien. Sans questions posées.';

  @override
  String get paywall_step3_cta => 'Commencer mon essai gratuit';

  @override
  String get onboarding_rotate_to_landscape =>
      'Tournez votre appareil en mode paysage';

  @override
  String get onboarding_rotate_to_portrait =>
      'Tournez votre appareil en mode portrait';

  @override
  String get demo_game_1_title => 'Formes d\'animaux';

  @override
  String get demo_game_1_context =>
      'Aidez votre enfant à associer les animaux à leurs formes ! Cela développe les compétences de reconnaissance visuelle essentielles pour la lecture.';

  @override
  String get demo_game_2_title => 'Mémoire associative';

  @override
  String get demo_game_2_context =>
      'Trouvez les paires assorties pour renforcer la mémoire de travail, essentielle pour suivre les instructions et résoudre les problèmes.';

  @override
  String get demo_game_3_title => 'Énigmes logiques';

  @override
  String get demo_game_3_context =>
      'Résolvez des énigmes pour développer la pensée logique et la reconnaissance des motifs.';

  @override
  String get demo_game_4_title => 'Plaisir des motifs';

  @override
  String get demo_game_4_context =>
      'Reconnaissez les motifs pour développer la préparation mathématique et les compétences de séquençage.';

  @override
  String get demo_game_5_title => 'Explorateur du monde';

  @override
  String get demo_game_5_context =>
      'Explorez le monde pour élargir le vocabulaire et la conscience culturelle.';

  @override
  String get demo_game_congratulations =>
      'Incroyable ! Vous avez gagné un badge !';

  @override
  String paywall_benefit_all_games(int gameCount) {
    return 'Accès à tous les $gameCount jeux';
  }

  @override
  String get paywall_benefit_age_appropriate => 'Contenu adapté à l\'âge';

  @override
  String get paywall_benefit_progress_tracking => 'Suivi des progrès';

  @override
  String get paywall_benefit_offline_play => 'Jeu hors ligne pris en charge';

  @override
  String get paywall_benefit_no_ads => 'Sans publicité, sûr pour les enfants';

  @override
  String get paywall_benefit_regular_updates => 'Mises à jour régulières';

  @override
  String get paywall_start_trial => 'Commencer l\'essai gratuit';

  @override
  String get paywall_step3_benefit_1 =>
      'Accès complet à tous les jeux d\'apprentissage';

  @override
  String get paywall_step3_benefit_2 =>
      'Environnement d\'apprentissage sûr et sans publicité';

  @override
  String get paywall_step3_benefit_3 => 'Parfait pour toute la famille';

  @override
  String get paywall_subscribe_button => 'S\'abonner maintenant';

  @override
  String get educational_value_headline =>
      'Du temps d\'écran de qualité qui aide vraiment votre enfant';

  @override
  String get educational_value_research =>
      'Soutenu par la recherche en développement de la petite enfance';

  @override
  String get value_carousel_1_headline =>
      'Développer les compétences cognitives par le jeu';

  @override
  String get value_carousel_1_description =>
      'Nos jeux sont conçus pour renforcer la mémoire, la résolution de problèmes et les compétences de pensée critique au rythme parfait pour l\'âge de votre enfant.';

  @override
  String get value_carousel_1_benefit_1_title => 'Développement de la mémoire';

  @override
  String get value_carousel_1_benefit_1_description =>
      'Jeux d\'association qui renforcent le rappel et la reconnaissance';

  @override
  String get value_carousel_1_benefit_2_title => 'Résolution de problèmes';

  @override
  String get value_carousel_1_benefit_2_description =>
      'Énigmes qui encouragent la pensée logique et la stratégie';

  @override
  String get value_carousel_1_benefit_3_title => 'Apprentissage progressif';

  @override
  String get value_carousel_1_benefit_3_description =>
      'La difficulté s\'adapte au fur et à mesure que votre enfant maîtrise de nouvelles compétences';

  @override
  String get value_carousel_2_headline =>
      'Du temps d\'écran dont vous pouvez vous sentir bien';

  @override
  String get value_carousel_2_description =>
      'Contrairement au divertissement passif, Brainy Bunny engage activement l\'esprit de votre enfant dans un environnement sûr et sans publicité.';

  @override
  String get value_carousel_2_feature_1_title => '100% sans publicité';

  @override
  String get value_carousel_2_feature_1_description =>
      'Pas de publicités, pas de distractions, pas de contenu inapproprié';

  @override
  String get value_carousel_2_feature_2_title => 'Adapté à l\'âge';

  @override
  String get value_carousel_2_feature_2_description =>
      'Contenu conçu spécifiquement pour les 2-5 ans';

  @override
  String get value_carousel_2_feature_3_title => 'Apprentissage actif';

  @override
  String get value_carousel_2_feature_3_description =>
      'Activités engageantes, pas de visionnage passif';

  @override
  String get value_carousel_3_headline =>
      'Voyez leurs progrès et gardez-les engagés';

  @override
  String get value_carousel_3_feature_1_title => 'Suivi des progrès';

  @override
  String get value_carousel_3_feature_1_description =>
      'Voyez quelles compétences votre enfant développe';

  @override
  String get value_carousel_3_feature_2_title => 'Récompenses de réalisation';

  @override
  String get value_carousel_3_feature_2_description =>
      'Gagnez des étoiles et des badges qui motivent l\'apprentissage continu';

  @override
  String get value_carousel_3_feature_3_title => 'Expérience personnalisée';

  @override
  String get value_carousel_3_feature_3_description =>
      'Jeux qui s\'adaptent au niveau de compétence de votre enfant';

  @override
  String get summary_headline_new =>
      'Parfait ! Voici votre parcours d\'apprentissage personnalisé';

  @override
  String get summary_learning_path_title => 'Ce que votre enfant apprendra :';

  @override
  String get summary_skill_cognitive => 'Développement cognitif';

  @override
  String summary_skill_cognitive_desc(int age) {
    return 'Jeux de mémoire et activités de résolution de problèmes parfaits pour l\'âge de $age ans';
  }

  @override
  String get summary_skill_visual => 'Perception visuelle';

  @override
  String summary_skill_visual_desc(int age) {
    return 'Jeux de reconnaissance des formes et conscience spatiale pour les enfants de $age ans';
  }

  @override
  String get summary_skill_exploration => 'Exploration et découverte';

  @override
  String summary_skill_exploration_desc(int age) {
    return 'Jeux interactifs qui encouragent la curiosité à $age ans';
  }

  @override
  String get summary_next_step =>
      'Suivant : Essayez Premium gratuitement pendant 7 jours pour débloquer tous les jeux !';

  @override
  String get trial_badge => 'Essai gratuit de 7 jours';

  @override
  String get trial_headline => 'Essayez Premium gratuitement pendant 7 jours';

  @override
  String get trial_description =>
      'Obtenez un accès complet à tous les jeux et fonctionnalités premium. Annulez à tout moment pendant votre essai - aucun frais si vous annulez avant la fin.';

  @override
  String get trial_feature_1_title => 'Tous les jeux premium';

  @override
  String get trial_feature_1_description =>
      'Accédez à tous les jeux d\'apprentissage de notre bibliothèque';

  @override
  String get trial_feature_2_title => 'Expérience sans publicité';

  @override
  String get trial_feature_2_description =>
      'Environnement d\'apprentissage sûr sans publicités';

  @override
  String get trial_feature_3_title => 'Suivi des progrès';

  @override
  String get trial_feature_3_description =>
      'Voyez le développement et les réalisations de votre enfant';

  @override
  String get trial_feature_4_title => 'Mises à jour régulières';

  @override
  String get trial_feature_4_description =>
      'Nouveaux jeux et fonctionnalités ajoutés régulièrement';

  @override
  String get trial_how_it_works_title => 'Comment ça marche :';

  @override
  String get trial_step_1 =>
      'Commencez votre essai gratuit de 7 jours aujourd\'hui';

  @override
  String get trial_step_2 =>
      'Profitez d\'un accès complet à toutes les fonctionnalités premium';

  @override
  String get trial_step_3 =>
      'Annulez à tout moment - aucun frais avant la fin de l\'essai';

  @override
  String get trial_cta => 'Commencer mon essai gratuit';

  @override
  String get trial_disclaimer =>
      'Gratuit pendant 7 jours, puis tarif du forfait sélectionné. Annulez à tout moment.';

  @override
  String get notification_permission_headline =>
      'Restez connecté à l\'apprentissage de votre enfant';

  @override
  String get notification_permission_description =>
      'Recevez des rappels utiles et célébrez les étapes importantes avec votre enfant. Nous vous enverrons des notifications opportunes sur les réalisations et les opportunités d\'apprentissage.';

  @override
  String get notification_benefit_1_title => 'Rappels d\'essai';

  @override
  String get notification_benefit_1_description =>
      'Soyez averti avant la fin de votre essai pour ne jamais perdre l\'accès';

  @override
  String get notification_benefit_2_title => 'Étapes d\'apprentissage';

  @override
  String get notification_benefit_2_description =>
      'Célébrez quand votre enfant atteint de nouvelles réalisations';

  @override
  String get notification_benefit_3_title => 'Conseils d\'engagement';

  @override
  String get notification_benefit_3_description =>
      'Obtenez des suggestions pour garder l\'apprentissage amusant et engageant';

  @override
  String get notification_privacy_note =>
      'Nous respectons votre vie privée. Vous pouvez désactiver les notifications à tout moment dans les paramètres.';

  @override
  String get notification_enable_button => 'Activer les notifications';

  @override
  String get notification_maybe_later => 'Peut-être plus tard';

  @override
  String get subscription_management_title => 'Gérer l\'abonnement';

  @override
  String get subscription_status_active => 'Premium actif';

  @override
  String get subscription_status_active_description =>
      'Vous avez un accès complet à toutes les fonctionnalités premium';

  @override
  String get subscription_status_inactive => 'Version gratuite';

  @override
  String get subscription_status_inactive_description =>
      'Passez à premium pour un accès complet à tous les jeux';

  @override
  String get subscription_actions_title => 'Actions';

  @override
  String get subscription_restore_title => 'Restaurer les achats';

  @override
  String get subscription_restore_description =>
      'Déjà abonné sur un autre appareil ? Restaurez vos achats ici.';

  @override
  String get subscription_restore_button => 'Restaurer';

  @override
  String get subscription_manage_title => 'Gérez votre abonnement';

  @override
  String get subscription_manage_description =>
      'Consultez, modifiez ou annulez votre abonnement via votre compte de la boutique d\'applications.';

  @override
  String get subscription_manage_button =>
      'Ouvrir les paramètres d\'abonnement';

  @override
  String get subscription_help_title => 'Aide et informations';

  @override
  String get subscription_cancel_title => 'Comment annuler';

  @override
  String get subscription_cancel_description =>
      'Vous pouvez annuler votre abonnement à tout moment via les paramètres de votre compte App Store ou Google Play. Vous conserverez l\'accès jusqu\'à la fin de votre période de facturation.';

  @override
  String get subscription_payment_failure_title => 'Problèmes de paiement';

  @override
  String get subscription_payment_failure_description =>
      'Si votre paiement échoue, mettez à jour votre méthode de paiement dans votre compte de la boutique d\'applications. Nous réessayerons automatiquement le paiement.';

  @override
  String get next_button => 'Suivant';

  @override
  String get back_button => 'Retour';
}

// lib/onboarding/screens/paywall_step_1_screen.dart
import 'package:flutter/material.dart';
import '../../flutter_gen/gen_l10n/app_localizations.dart';

/// Paywall Step 1: Value Proposition
/// Presents the core benefits and value of the premium subscription
class PaywallStep1Screen extends StatefulWidget {
  final VoidCallback onContinue;
  final VoidCallback onSkip;

  const PaywallStep1Screen({
    super.key,
    required this.onContinue,
    required this.onSkip,
  });

  @override
  State<PaywallStep1Screen> createState() => _PaywallStep1ScreenState();
}

class _PaywallStep1ScreenState extends State<PaywallStep1Screen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final size = MediaQuery.of(context).size;

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Stack(
          children: [
            // Main content
            Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: size.height * 0.02),

                  // Skip button
                  Align(
                    alignment: Alignment.topRight,
                    child: TextButton(
                      onPressed: widget.onSkip,
                      child: Text(
                        l10n.skip_button,
                        style: TextStyle(
                          fontSize: 16,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ),
                  ),

                  SizedBox(height: size.height * 0.02),

                  // Premium badge
                  Center(
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 10,
                      ),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Colors.amber.shade400,
                            Colors.amber.shade600,
                          ],
                        ),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.stars,
                            color: Colors.white,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            l10n.paywall_premium_badge,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  SizedBox(height: size.height * 0.03),

                  // Headline
                  Center(
                    child: Text(
                      l10n.paywall_step1_headline,
                      style: const TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1E3A5F),
                        height: 1.3,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),

                  SizedBox(height: size.height * 0.04),

                  // Value propositions with animation
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          _AnimatedValueProp(
                            animation: _animationController,
                            delay: 0.0,
                            icon: Icons.games,
                            iconColor: Colors.blue.shade600,
                            title: l10n.paywall_value_1_title,
                            description: l10n.paywall_value_1_description,
                          ),
                          const SizedBox(height: 20),
                          _AnimatedValueProp(
                            animation: _animationController,
                            delay: 0.2,
                            icon: Icons.trending_up,
                            iconColor: Colors.green.shade600,
                            title: l10n.paywall_value_2_title,
                            description: l10n.paywall_value_2_description,
                          ),
                          const SizedBox(height: 20),
                          _AnimatedValueProp(
                            animation: _animationController,
                            delay: 0.4,
                            icon: Icons.auto_awesome,
                            iconColor: Colors.purple.shade600,
                            title: l10n.paywall_value_3_title,
                            description: l10n.paywall_value_3_description,
                          ),
                          const SizedBox(height: 20),
                          _AnimatedValueProp(
                            animation: _animationController,
                            delay: 0.6,
                            icon: Icons.update,
                            iconColor: Colors.orange.shade600,
                            title: l10n.paywall_value_4_title,
                            description: l10n.paywall_value_4_description,
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // CTA Button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: widget.onContinue,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue.shade600,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 18),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        elevation: 2,
                      ),
                      child: Text(
                        l10n.paywall_step1_cta,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Trust badge
                  Center(
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.lock,
                          size: 16,
                          color: Colors.grey.shade600,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          l10n.paywall_secure_payment,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _AnimatedValueProp extends StatelessWidget {
  final Animation<double> animation;
  final double delay;
  final IconData icon;
  final Color iconColor;
  final String title;
  final String description;

  const _AnimatedValueProp({
    required this.animation,
    required this.delay,
    required this.icon,
    required this.iconColor,
    required this.title,
    required this.description,
  });

  @override
  Widget build(BuildContext context) {
    final delayedAnimation = CurvedAnimation(
      parent: animation,
      curve: Interval(
        delay,
        1.0,
        curve: Curves.easeOut,
      ),
    );

    return FadeTransition(
      opacity: delayedAnimation,
      child: SlideTransition(
        position: Tween<Offset>(
          begin: const Offset(-0.3, 0),
          end: Offset.zero,
        ).animate(delayedAnimation),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: 56,
              height: 56,
              decoration: BoxDecoration(
                color: iconColor.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                color: iconColor,
                size: 28,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 8),
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF1E3A5F),
                    ),
                  ),
                  const SizedBox(height: 6),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade700,
                      height: 1.4,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

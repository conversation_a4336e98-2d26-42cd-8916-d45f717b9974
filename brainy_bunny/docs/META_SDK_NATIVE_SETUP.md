# Meta SDK Native Implementation Guide

This document describes the native iOS and Android implementation required for Meta SDK integration.

## iOS Implementation

### 1. Add Facebook SDK to Podfile

Add to `ios/Podfile`:

```ruby
pod 'FBSDKCoreKit', '~> 16.0.0'
```

Run:
```bash
cd ios
pod install
```

### 2. Configure Info.plist

Add to `ios/Runner/Info.plist`:

```xml
<key>FacebookAppID</key>
<string>2012260112880464</string>

<key>FacebookClientToken</key>
<string>YOUR_CLIENT_TOKEN_HERE</string>

<key>FacebookDisplayName</key>
<string>Brainy Bunny</string>

<key>CFBundleURLTypes</key>
<array>
  <dict>
    <key>CFBundleURLSchemes</key>
    <array>
      <string>fb2012260112880464</string>
    </array>
  </dict>
</array>
```

### 3. Update AppDelegate.swift

Replace `ios/Runner/AppDelegate.swift` with:

```swift
import UIKit
import Flutter
import FBSDK<PERSON>oreKit

@UIApplicationMain
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    GeneratedPluginRegistrant.register(with: self)

    // Initialize Facebook SDK
    ApplicationDelegate.shared.application(
      application,
      didFinishLaunchingWithOptions: launchOptions
    )

    // Set up Meta platform channel
    let controller = window?.rootViewController as! FlutterViewController
    let metaChannel = FlutterMethodChannel(
      name: "com.goodkarmalab.brainy_bunny/meta",
      binaryMessenger: controller.binaryMessenger
    )

    metaChannel.setMethodCallHandler { (call: FlutterMethodCall, result: @escaping FlutterResult) in
      switch call.method {
      case "isInitialized":
        result(true)

      case "logEvent":
        if let args = call.arguments as? [String: Any],
           let eventName = args["eventName"] as? String {
          let parameters = args["parameters"] as? [String: Any]

          // Convert parameters to [AppEvents.ParameterName: Any]
          var fbParameters: [AppEvents.ParameterName: Any] = [:]
          if let params = parameters {
            for (key, value) in params {
              fbParameters[AppEvents.ParameterName(rawValue: key)] = value
            }
          }

          // Log event to Meta
          AppEvents.shared.logEvent(
            AppEvents.Name(rawValue: eventName),
            parameters: fbParameters
          )

          result(true)
        } else {
          result(FlutterError(code: "INVALID_ARGS", message: "Invalid arguments", details: nil))
        }

      default:
        result(FlutterMethodNotImplemented)
      }
    }

    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }

  override func application(
    _ app: UIApplication,
    open url: URL,
    options: [UIApplication.OpenURLOptionsKey : Any] = [:]
  ) -> Bool {
    ApplicationDelegate.shared.application(
      app,
      open: url,
      sourceApplication: options[UIApplication.OpenURLOptionsKey.sourceApplication] as? String,
      annotation: options[UIApplication.OpenURLOptionsKey.annotation]
    )
    return super.application(app, open: url, options: options)
  }
}
```

## Android Implementation

### 1. Add Facebook SDK to build.gradle

Add to `android/app/build.gradle`:

```gradle
dependencies {
    implementation 'com.facebook.android:facebook-android-sdk:16.0.0'
    // ... other dependencies
}
```

### 2. Configure AndroidManifest.xml

Add to `android/app/src/main/AndroidManifest.xml` inside `<application>`:

```xml
<meta-data
    android:name="com.facebook.sdk.ApplicationId"
    android:value="@string/facebook_app_id"/>

<meta-data
    android:name="com.facebook.sdk.ClientToken"
    android:value="@string/facebook_client_token"/>
```

### 3. Add strings.xml

Create/edit `android/app/src/main/res/values/strings.xml`:

```xml
<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">Brainy Bunny</string>
    <string name="facebook_app_id">2012260112880464</string>
    <string name="facebook_client_token">YOUR_CLIENT_TOKEN_HERE</string>
</resources>
```

### 4. Update MainActivity.kt

Replace `android/app/src/main/kotlin/com/goodkarmalab/brainy_bunny/MainActivity.kt` with:

```kotlin
package com.goodkarmalab.brainy_bunny

import android.os.Bundle
import com.facebook.FacebookSdk
import com.facebook.appevents.AppEventsLogger
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity: FlutterActivity() {
    private val CHANNEL = "com.goodkarmalab.brainy_bunny/meta"
    private lateinit var logger: AppEventsLogger

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Initialize Facebook SDK
        FacebookSdk.sdkInitialize(applicationContext)
        logger = AppEventsLogger.newLogger(this)
    }

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "isInitialized" -> {
                    result.success(true)
                }
                "logEvent" -> {
                    val eventName = call.argument<String>("eventName")
                    val parameters = call.argument<Map<String, Any>>("parameters")

                    if (eventName != null) {
                        val bundle = Bundle()
                        parameters?.forEach { (key, value) ->
                            when (value) {
                                is String -> bundle.putString(key, value)
                                is Int -> bundle.putInt(key, value)
                                is Double -> bundle.putDouble(key, value)
                                is Boolean -> bundle.putBoolean(key, value)
                            }
                        }

                        logger.logEvent(eventName, bundle)
                        result.success(true)
                    } else {
                        result.error("INVALID_ARGS", "Invalid arguments", null)
                    }
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }
}
```

## Obtaining Facebook Client Token

1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Navigate to your app (App ID: 2012260112880464)
3. Go to Settings → Advanced
4. Find "Client Token" section
5. Copy the client token
6. Replace `YOUR_CLIENT_TOKEN_HERE` in both iOS and Android configurations

## Testing

After implementing native code:

1. Run the app
2. Check debug logs for Meta SDK initialization
3. Trigger test events from Flutter code
4. Verify events appear in Meta Event Manager dashboard

## Troubleshooting

**iOS Issues:**
- Run `pod install` after adding FBSDKCoreKit
- Clean build folder in Xcode
- Check Info.plist is properly formatted (no XML errors)

**Android Issues:**
- Sync Gradle after adding dependency
- Check strings.xml exists in res/values/
- Verify MainActivity.kt package name matches your app
- Clean and rebuild project

**Event Tracking Issues:**
- Events may take 15-30 minutes to appear in dashboard
- Use Meta Event Manager's Test Events feature
- Enable debug logging in both platforms

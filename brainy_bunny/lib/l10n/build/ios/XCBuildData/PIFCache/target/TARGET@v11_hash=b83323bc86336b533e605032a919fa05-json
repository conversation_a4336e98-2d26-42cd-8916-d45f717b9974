{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983599ff2ef17cd5004356d7f56c0bfac4", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/leveldb-library", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "leveldb", "INFOPLIST_FILE": "Target Support Files/leveldb-library/ResourceBundle-leveldb_Privacy-leveldb-library-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "leveldb_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98e6b4500e117e7ac31ea3fd53353786e9", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9803d25163f2fe1eb430ff928247549859", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/leveldb-library", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "leveldb", "INFOPLIST_FILE": "Target Support Files/leveldb-library/ResourceBundle-leveldb_Privacy-leveldb-library-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "PRODUCT_NAME": "leveldb_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e989194457796bf172ff5a913c30c2da503", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9803d25163f2fe1eb430ff928247549859", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/leveldb-library", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "leveldb", "INFOPLIST_FILE": "Target Support Files/leveldb-library/ResourceBundle-leveldb_Privacy-leveldb-library-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "PRODUCT_NAME": "leveldb_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e987df2a485395eebb7dc60247c10607c93", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e982801fcd524c0c91a55980dc7c8b78013", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98670afbed5c4c31b41f8310081cdfcf2b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987a098b2781640a8e64b5416951eb6545", "guid": "bfdfe7dc352907fc980b868725387e9824dabca12e5e6aadbb05615c1e20ed11"}], "guid": "bfdfe7dc352907fc980b868725387e9885dbcbbfe85105665f5605f088161bdc", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e984fe1f454389a944b317683cfdba2e41e", "name": "leveldb-library-leveldb_Privacy", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980fe361aa6bc047147e11f30537be10ee", "name": "leveldb_Privacy.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}
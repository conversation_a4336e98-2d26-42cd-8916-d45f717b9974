# Server-Side Purchase Verification Setup

This guide explains how to implement server-side purchase verification for Brainy Bunny using RevenueCat webhooks and Cloud Functions.

## Overview

Server-side verification ensures:
- Purchase receipts are validated by Apple/Google before granting access
- Protection against fraud and jailbreak/root exploits
- Consistent subscription status across devices
- Audit trail for financial reconciliation
- Automatic handling of subscription lifecycle events

## Architecture

```
User Device → RevenueCat SDK → App Store/Play Store
                    ↓
              RevenueCat Server
                    ↓
              Webhook → Cloud Function → Firestore
                                              ↓
                                        Update User Status
```

## Part 1: RevenueCat Webhook Configuration

### 1.1 Set Up Webhook URL

In RevenueCat Dashboard:
1. Go to **Project Settings → Integrations**
2. Click **+ Webhooks**
3. Enter your Cloud Function URL:
   ```
   https://us-central1-<your-project-id>.cloudfunctions.net/revenuecatWebhook
   ```
4. Select events to listen for:
   - ✅ Initial Purchase
   - ✅ Renewal
   - ✅ Cancellation
   - ✅ Uncancellation
   - ✅ Non Renewing Purchase
   - ✅ Expiration
   - ✅ Billing Issue
   - ✅ Product Change
   - ✅ Transfer

### 1.2 Get Webhook Authorization

1. In RevenueCat webhook settings, copy the **Authorization** header value
2. This will be used to verify webhook authenticity in Cloud Function
3. Store this securely - it's like a password

## Part 2: Cloud Function Implementation

### 2.1 Set Up Firebase Cloud Functions

In your Firebase project directory:

```bash
cd functions
npm install --save express
npm install --save firebase-admin
npm install --save firebase-functions
```

### 2.2 Create Webhook Handler

Create `functions/index.js`:

```javascript
const functions = require('firebase-functions');
const admin = require('firebase-admin');
const express = require('express');

admin.initializeApp();
const db = admin.firestore();

const app = express();
app.use(express.json());

// RevenueCat Authorization header (store in Firebase config)
const REVENUECAT_WEBHOOK_AUTH = functions.config().revenuecat.webhook_auth;

/**
 * RevenueCat Webhook Handler
 * Processes subscription lifecycle events
 */
app.post('/revenuecat-webhook', async (req, res) => {
  try {
    // 1. Verify webhook authenticity
    const authHeader = req.headers.authorization;

    if (authHeader !== `Bearer ${REVENUECAT_WEBHOOK_AUTH}`) {
      console.error('Unauthorized webhook attempt');
      return res.status(401).send('Unauthorized');
    }

    // 2. Extract event data
    const event = req.body;
    const eventType = event.type;
    const appUserId = event.event?.app_user_id;
    const productId = event.event?.product_id;
    const purchasedAt = event.event?.purchased_at_ms;
    const expirationAt = event.event?.expiration_at_ms;
    const isTrialConversion = event.event?.is_trial_conversion || false;
    const periodType = event.event?.period_type;
    const store = event.event?.store;
    const price = event.event?.price;
    const currency = event.event?.currency;

    console.log(`Processing webhook: ${eventType} for user ${appUserId}`);

    // 3. Handle different event types
    switch (eventType) {
      case 'INITIAL_PURCHASE':
        await handleInitialPurchase(appUserId, {
          productId,
          purchasedAt,
          expirationAt,
          isTrialConversion,
          periodType,
          store,
          price,
          currency,
        });
        break;

      case 'RENEWAL':
        await handleRenewal(appUserId, {
          productId,
          purchasedAt,
          expirationAt,
          store,
          price,
          currency,
        });
        break;

      case 'CANCELLATION':
        await handleCancellation(appUserId, {
          productId,
          expirationAt,
        });
        break;

      case 'UNCANCELLATION':
        await handleUncancellation(appUserId, {
          productId,
          expirationAt,
        });
        break;

      case 'EXPIRATION':
        await handleExpiration(appUserId, {
          productId,
        });
        break;

      case 'BILLING_ISSUE':
        await handleBillingIssue(appUserId, {
          productId,
          expirationAt,
        });
        break;

      case 'PRODUCT_CHANGE':
        await handleProductChange(appUserId, {
          oldProductId: event.event?.old_product_id,
          newProductId: productId,
          expirationAt,
        });
        break;

      default:
        console.log(`Unhandled event type: ${eventType}`);
    }

    // 4. Send success response
    res.status(200).send('OK');

  } catch (error) {
    console.error('Webhook processing error:', error);
    res.status(500).send('Internal Server Error');
  }
});

/**
 * Handle initial purchase (including trial starts)
 */
async function handleInitialPurchase(userId, data) {
  const subscriptionData = {
    status: 'active',
    productId: data.productId,
    purchasedAt: new Date(data.purchasedAt),
    expirationAt: data.expirationAt ? new Date(data.expirationAt) : null,
    isTrialPeriod: data.periodType === 'TRIAL',
    isTrialConversion: data.isTrialConversion,
    store: data.store,
    price: data.price,
    currency: data.currency,
    lastUpdated: admin.firestore.FieldValue.serverTimestamp(),
  };

  // Update user subscription status
  await db.collection('users').doc(userId).set({
    subscription: subscriptionData,
    hasActiveSubscription: true,
  }, { merge: true });

  // Log purchase event
  await db.collection('subscription_events').add({
    userId,
    eventType: 'INITIAL_PURCHASE',
    ...subscriptionData,
    timestamp: admin.firestore.FieldValue.serverTimestamp(),
  });

  console.log(`Initial purchase recorded for user ${userId}: ${data.productId}`);
}

/**
 * Handle subscription renewal
 */
async function handleRenewal(userId, data) {
  const subscriptionData = {
    status: 'active',
    productId: data.productId,
    purchasedAt: new Date(data.purchasedAt),
    expirationAt: data.expirationAt ? new Date(data.expirationAt) : null,
    isTrialPeriod: false,
    store: data.store,
    price: data.price,
    currency: data.currency,
    lastUpdated: admin.firestore.FieldValue.serverTimestamp(),
  };

  await db.collection('users').doc(userId).set({
    subscription: subscriptionData,
    hasActiveSubscription: true,
  }, { merge: true });

  await db.collection('subscription_events').add({
    userId,
    eventType: 'RENEWAL',
    ...subscriptionData,
    timestamp: admin.firestore.FieldValue.serverTimestamp(),
  });

  console.log(`Renewal recorded for user ${userId}: ${data.productId}`);
}

/**
 * Handle subscription cancellation
 */
async function handleCancellation(userId, data) {
  // Note: User still has access until expiration date
  await db.collection('users').doc(userId).set({
    subscription: {
      status: 'cancelled',
      productId: data.productId,
      expirationAt: data.expirationAt ? new Date(data.expirationAt) : null,
      cancelledAt: admin.firestore.FieldValue.serverTimestamp(),
      lastUpdated: admin.firestore.FieldValue.serverTimestamp(),
    },
    hasActiveSubscription: true, // Still active until expiration
  }, { merge: true });

  await db.collection('subscription_events').add({
    userId,
    eventType: 'CANCELLATION',
    productId: data.productId,
    expirationAt: data.expirationAt ? new Date(data.expirationAt) : null,
    timestamp: admin.firestore.FieldValue.serverTimestamp(),
  });

  console.log(`Cancellation recorded for user ${userId}`);
}

/**
 * Handle subscription uncancellation (user re-enables auto-renew)
 */
async function handleUncancellation(userId, data) {
  await db.collection('users').doc(userId).set({
    subscription: {
      status: 'active',
      productId: data.productId,
      expirationAt: data.expirationAt ? new Date(data.expirationAt) : null,
      uncancelledAt: admin.firestore.FieldValue.serverTimestamp(),
      lastUpdated: admin.firestore.FieldValue.serverTimestamp(),
    },
    hasActiveSubscription: true,
  }, { merge: true });

  await db.collection('subscription_events').add({
    userId,
    eventType: 'UNCANCELLATION',
    productId: data.productId,
    timestamp: admin.firestore.FieldValue.serverTimestamp(),
  });

  console.log(`Uncancellation recorded for user ${userId}`);
}

/**
 * Handle subscription expiration
 */
async function handleExpiration(userId, data) {
  await db.collection('users').doc(userId).set({
    subscription: {
      status: 'expired',
      productId: data.productId,
      expiredAt: admin.firestore.FieldValue.serverTimestamp(),
      lastUpdated: admin.firestore.FieldValue.serverTimestamp(),
    },
    hasActiveSubscription: false,
  }, { merge: true });

  await db.collection('subscription_events').add({
    userId,
    eventType: 'EXPIRATION',
    productId: data.productId,
    timestamp: admin.firestore.FieldValue.serverTimestamp(),
  });

  console.log(`Expiration recorded for user ${userId}`);
}

/**
 * Handle billing issues (payment failed)
 */
async function handleBillingIssue(userId, data) {
  await db.collection('users').doc(userId).set({
    subscription: {
      status: 'billing_issue',
      productId: data.productId,
      expirationAt: data.expirationAt ? new Date(data.expirationAt) : null,
      billingIssueDetectedAt: admin.firestore.FieldValue.serverTimestamp(),
      lastUpdated: admin.firestore.FieldValue.serverTimestamp(),
    },
    hasActiveSubscription: true, // Grace period - still has access
  }, { merge: true });

  await db.collection('subscription_events').add({
    userId,
    eventType: 'BILLING_ISSUE',
    productId: data.productId,
    timestamp: admin.firestore.FieldValue.serverTimestamp(),
  });

  // TODO: Send notification to user about billing issue

  console.log(`Billing issue recorded for user ${userId}`);
}

/**
 * Handle product change (upgrade/downgrade)
 */
async function handleProductChange(userId, data) {
  await db.collection('users').doc(userId).set({
    subscription: {
      status: 'active',
      productId: data.newProductId,
      previousProductId: data.oldProductId,
      expirationAt: data.expirationAt ? new Date(data.expirationAt) : null,
      productChangedAt: admin.firestore.FieldValue.serverTimestamp(),
      lastUpdated: admin.firestore.FieldValue.serverTimestamp(),
    },
    hasActiveSubscription: true,
  }, { merge: true });

  await db.collection('subscription_events').add({
    userId,
    eventType: 'PRODUCT_CHANGE',
    oldProductId: data.oldProductId,
    newProductId: data.newProductId,
    timestamp: admin.firestore.FieldValue.serverTimestamp(),
  });

  console.log(`Product change recorded for user ${userId}: ${data.oldProductId} → ${data.newProductId}`);
}

// Export Cloud Function
exports.revenuecatWebhook = functions.https.onRequest(app);
```

### 2.3 Set Webhook Authorization Secret

```bash
firebase functions:config:set revenuecat.webhook_auth="YOUR_REVENUECAT_WEBHOOK_AUTH_TOKEN"
```

### 2.4 Deploy Cloud Function

```bash
firebase deploy --only functions:revenuecatWebhook
```

### 2.5 Get Function URL

After deployment, you'll see:
```
✔  functions[revenuecatWebhook]: Successful create operation.
Function URL: https://us-central1-<project-id>.cloudfunctions.net/revenuecatWebhook
```

Copy this URL and add it to RevenueCat webhook settings.

## Part 3: Firestore Security Rules

Update `firestore.rules`:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

    // Users collection
    match /users/{userId} {
      // Users can read their own subscription status
      allow read: if request.auth != null && request.auth.uid == userId;

      // Only Cloud Functions can write subscription data
      allow write: if false;
    }

    // Subscription events (audit log)
    match /subscription_events/{eventId} {
      // Only admins can read events
      allow read: if request.auth != null &&
                     get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';

      // Only Cloud Functions can write
      allow write: if false;
    }
  }
}
```

## Part 4: Client-Side Integration

### 4.1 Update SubscriptionManager

Add method to sync with Firestore:

```dart
/// Sync subscription status from server (Firestore)
Future<void> syncWithServer() async {
  try {
    final user = FirebaseAuth.instance.currentUser;
    if (user == null) return;

    final userDoc = await FirebaseFirestore.instance
        .collection('users')
        .doc(user.uid)
        .get();

    if (userDoc.exists) {
      final data = userDoc.data();
      if (data != null) {
        final serverHasSubscription = data['hasActiveSubscription'] ?? false;
        final subscriptionData = data['subscription'] as Map<String, dynamic>?;

        if (serverHasSubscription && subscriptionData != null) {
          // Server confirms active subscription
          final productId = subscriptionData['productId'] as String?;
          final status = subscriptionData['status'] as String?;

          if (status == 'active' || status == 'cancelled' || status == 'billing_issue') {
            _hasActiveSubscription = true;
            _activeProductId = productId;

            if (kDebugMode) {
              print('✅ Server-verified active subscription: $productId');
            }
          }
        } else {
          // Server says no active subscription
          if (_hasActiveSubscription && !_isLifetimeUser) {
            if (kDebugMode) {
              print('⚠️ Server override: Subscription not active on server');
            }
            _hasActiveSubscription = false;
            _activeProductId = null;
          }
        }

        _subscriptionStateController.add(_hasActiveSubscription || _isLifetimeUser);
      }
    }
  } catch (e) {
    if (kDebugMode) {
      print('❌ Error syncing with server: $e');
    }
  }
}
```

### 4.2 Call Sync Periodically

In app initialization:

```dart
// After initializing SubscriptionManager
await subscriptionManager.initialize(apiKey: REVENUECAT_API_KEY);

// Sync with server
await subscriptionManager.syncWithServer();

// Set up periodic sync (every 5 minutes while app is active)
Timer.periodic(Duration(minutes: 5), (timer) {
  subscriptionManager.syncWithServer();
});
```

## Part 5: Testing

### 5.1 Test Webhook Locally

Use ngrok to test webhooks locally:

```bash
# Start Firebase emulator
firebase emulators:start --only functions

# In another terminal, expose local function
ngrok http 5001

# Use ngrok URL in RevenueCat webhook settings
# Format: https://xxxx.ngrok.io/<project-id>/us-central1/revenuecatWebhook
```

### 5.2 Test Events

RevenueCat Dashboard → Settings → Webhooks → Test Webhook

Send test events:
- Initial Purchase
- Renewal
- Cancellation
- Expiration

Verify in Firestore that records are created correctly.

### 5.3 Production Testing

1. Make a real sandbox purchase on device
2. Check RevenueCat webhook logs for delivery
3. Verify Firestore data is updated
4. Check that app reflects subscription status

## Part 6: Monitoring

### 6.1 Cloud Function Logs

```bash
firebase functions:log --only revenuecatWebhook
```

Or view in Firebase Console → Functions → Logs

### 6.2 RevenueCat Webhook Logs

RevenueCat Dashboard → Settings → Webhooks → History

Shows all webhook deliveries, retries, and responses.

### 6.3 Firestore Audit Trail

Query `subscription_events` collection to track all subscription changes:

```javascript
db.collection('subscription_events')
  .where('userId', '==', userId)
  .orderBy('timestamp', 'desc')
  .limit(50)
  .get();
```

## Part 7: Error Handling

### 7.1 Webhook Retry Logic

RevenueCat automatically retries failed webhooks:
- Initial retry: After 1 minute
- Subsequent retries: Exponential backoff
- Max retries: 5 attempts
- Timeout: After 24 hours

### 7.2 Handle Missed Webhooks

Implement periodic reconciliation:

```dart
Future<void> reconcileWithRevenueCat() async {
  try {
    // Get latest customer info from RevenueCat
    final customerInfo = await Purchases.getCustomerInfo();

    // Get server status from Firestore
    await syncWithServer();

    // Compare and log discrepancies
    if (_hasActiveSubscription != customerInfo.entitlements.active.containsKey('premium')) {
      if (kDebugMode) {
        print('⚠️ DISCREPANCY: RevenueCat vs Server mismatch');
        print('   RevenueCat: ${customerInfo.entitlements.active.containsKey('premium')}');
        print('   Server: $_hasActiveSubscription');
      }

      // Trust RevenueCat as source of truth
      _updateSubscriptionState(customerInfo);

      // Log for investigation
      await AnalyticsManager.instance.trackEvent(
        eventName: 'subscription_reconciliation_mismatch',
        parameters: {
          'revenuecat_status': customerInfo.entitlements.active.containsKey('premium').toString(),
          'server_status': _hasActiveSubscription.toString(),
        },
      );
    }
  } catch (e) {
    if (kDebugMode) {
      print('❌ Reconciliation error: $e');
    }
  }
}
```

## Part 8: Security Best Practices

1. **Never trust client-side data** - Always verify with server
2. **Use webhook authentication** - Verify RevenueCat authorization header
3. **Implement rate limiting** - Prevent abuse of webhook endpoint
4. **Log all events** - Maintain audit trail for compliance
5. **Handle replay attacks** - Check event timestamps
6. **Encrypt sensitive data** - Use Firestore field-level encryption if needed
7. **Monitor for anomalies** - Alert on unusual subscription patterns

## Part 9: Compliance

### GDPR
- Store minimal user data
- Allow users to export subscription history
- Handle deletion requests (cancel + delete records)
- Document data retention policy

### COPPA
- No personal data collection from children
- All subscription decisions by parents
- Parental consent mechanism (already in app via parent gate)

### Financial Regulations
- Maintain audit trail of all transactions
- Keep records for 7 years minimum
- Implement refund tracking
- Tax compliance documentation

---

**Last Updated:** January 2025
**Version:** 1.0
**Maintained by:** Development Team

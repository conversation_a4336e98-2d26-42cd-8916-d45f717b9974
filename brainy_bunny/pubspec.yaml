name: brainy_bunny
description: "<PERSON><PERSON> Bunny"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+26

environment:
  sdk: '>=3.4.1 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.6
  flame: ^1.18.0
  flame_audio: ^2.10.4
  firebase_core: ^3.6.0
  firebase_database: ^11.1.4
  firebase_auth: ^5.3.1
  cloud_functions: ^5.1.3
  cloud_firestore: ^5.4.4
  in_app_purchase: ^3.2.0
  flutter_launcher_icons: ^0.14.1
  flutter_native_splash: ^2.4.1

  # Enhanced dependencies for robust purchase system
  flutter_secure_storage: ^9.2.2
  shared_preferences: ^2.3.2
  device_info_plus: ^10.1.2
  connectivity_plus: ^6.0.5
  crypto: ^3.0.3
  uuid: ^4.5.1

  # Analytics and subscription management
  adjust_sdk: ^4.38.0
  purchases_flutter: ^9.7.0

  # Localization
  flutter_localizations:
    sdk: flutter
  intl: ^0.19.0

  # Monitoring
  firebase_crashlytics: ^4.1.3

  # URL launcher for subscription management
  url_launcher: ^6.3.1

  # Local notifications for trial reminders and engagement
  flutter_local_notifications: ^17.2.3
  timezone: ^0.9.4

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^3.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  generate: true

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/images/game_1/
    - assets/images/game_2/
    - assets/images/game_3/
    - assets/images/game_4/
    - assets/images/game_5/
    - assets/images/game_6/
    - assets/images/game_7/
    - assets/images/game_8/
    - assets/images/game_9/
    - assets/images/game_10/
    - assets/images/game_11/
    - assets/images/game_12/
    - assets/images/game_13/
    - assets/images/game_14/
    - assets/images/game_15/
    - assets/audio/
    - assets/videos/
    - assets/icon/

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

# Flutter Native Splash configuration
flutter_native_splash:
  # This package generates native code to customize Flutter's default white native splash screen
  # with background color and splash image.
  # Customize the parameters below, and run the following command in the terminal:
  # dart run flutter_native_splash:create

  # IMPORTANT: The splash screen will show for a very short time as we've optimized the app startup.
  # The main purpose is to eliminate the white screen flash before our Flutter splash screen appears.

  # color or background_image is the only required parameter.  Use color to set the background
  # of your splash screen to a solid color.  For background_image, use the path to a png image.
  color: "#FFFFFF"

  # The image parameter allows you to specify an image used in the splash screen.  It must be a
  # png file and should be sized for 4x pixel density.
  image: assets/images/good-karma-lab-logo.jpeg

  # Android 12 handles the splash screen differently than previous versions.
  android_12:
    # The image parameter sets the splash screen icon image.
    image: assets/icon/icon.png
    # Splash screen background color.
    color: "#FFFFFF"

  # The android, ios and web parameters can be used to disable generating a splash screen on a given platform.
  android: true
  ios: true
  web: false

  # The screen orientation can be set in Android with the android_screen_orientation parameter.
  android_screen_orientation: sensorLandscape

  # To hide the notification bar, use the fullscreen parameter.
  fullscreen: true
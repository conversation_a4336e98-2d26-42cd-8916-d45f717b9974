#!/usr/bin/env dart

/// <PERSON>ript to validate translation ARB files
/// Checks for:
/// - Missing translations
/// - Placeholder mismatches
/// - Syntax errors
/// - Length violations
///
/// Usage: dart scripts/validate_translations.dart

import 'dart:io';
import 'dart:convert';

const String l10nDir = 'lib/l10n';
const String sourceFile = 'app_en.arb';

// Maximum lengths for different string types
const Map<String, int> maxLengths = {
  'button_': 20,
  'badge_': 20,
  '_headline': 50,
  '_title': 40,
};

void main() async {
  print('🔍 Validating translation files...\n');

  // Load source (English) file
  final sourceFilePath = '$l10nDir/$sourceFile';
  final sourceContent = await File(sourceFilePath).readAsString();
  final sourceJson = jsonDecode(sourceContent) as Map<String, dynamic>;

  // Extract keys (excluding metadata keys starting with @)
  final sourceKeys = sourceJson.keys
      .where((key) => !key.startsWith('@'))
      .toList();

  print('📝 Source file: $sourceFile');
  print('   Total keys: ${sourceKeys.length}\n');

  // Get all translation files
  final l10nDirectory = Directory(l10nDir);
  final translationFiles = l10nDirectory
      .listSync()
      .where((entity) =>
          entity is File &&
          entity.path.endsWith('.arb') &&
          !entity.path.endsWith(sourceFile))
      .cast<File>()
      .toList();

  print('🌍 Found ${translationFiles.length} translation files\n');

  var totalErrors = 0;
  var totalWarnings = 0;

  for (final file in translationFiles) {
    final result = await validateFile(file, sourceKeys, sourceJson);
    totalErrors += result['errors'] as int;
    totalWarnings += result['warnings'] as int;
  }

  print('\n${'='*60}');
  print('📊 Summary:');
  print('   Total errors: $totalErrors');
  print('   Total warnings: $totalWarnings');

  if (totalErrors > 0) {
    print('\n❌ Validation failed!');
    exit(1);
  } else if (totalWarnings > 0) {
    print('\n⚠️  Validation passed with warnings');
    exit(0);
  } else {
    print('\n✅ All translations valid!');
    exit(0);
  }
}

Future<Map<String, int>> validateFile(
  File file,
  List<String> sourceKeys,
  Map<String, dynamic> sourceJson,
) async {
  final fileName = file.path.split('/').last;
  final locale = fileName.replaceAll('app_', '').replaceAll('.arb', '');

  print('🔎 Validating: $fileName ($locale)');

  var errors = 0;
  var warnings = 0;

  try {
    // Parse JSON
    final content = await file.readAsString();
    final Map<String, dynamic> json;

    try {
      json = jsonDecode(content) as Map<String, dynamic>;
    } catch (e) {
      print('   ❌ JSON syntax error: $e');
      errors++;
      return {'errors': errors, 'warnings': warnings};
    }

    // Check if empty (placeholder file)
    if (json.isEmpty || (json.length == 1 && json.containsKey('@@locale'))) {
      print('   ⚠️  Empty/placeholder file (not yet translated)');
      warnings++;
      return {'errors': errors, 'warnings': warnings};
    }

    // Check locale declaration
    if (!json.containsKey('@@locale')) {
      print('   ⚠️  Missing @@locale declaration');
      warnings++;
    } else if (json['@@locale'] != locale) {
      print('   ❌ Locale mismatch: expected "$locale", got "${json['@@locale']}"');
      errors++;
    }

    // Extract translation keys
    final translationKeys = json.keys
        .where((key) => !key.startsWith('@'))
        .toList();

    // Check for missing translations
    final missingKeys = sourceKeys
        .where((key) => !translationKeys.contains(key))
        .toList();

    if (missingKeys.isNotEmpty) {
      print('   ❌ Missing ${missingKeys.length} translations:');
      for (final key in missingKeys.take(5)) {
        print('      - $key');
      }
      if (missingKeys.length > 5) {
        print('      ... and ${missingKeys.length - 5} more');
      }
      errors += missingKeys.length;
    }

    // Check for extra keys (not in source)
    final extraKeys = translationKeys
        .where((key) => !sourceKeys.contains(key))
        .toList();

    if (extraKeys.isNotEmpty) {
      print('   ⚠️  Found ${extraKeys.length} extra keys not in source:');
      for (final key in extraKeys.take(3)) {
        print('      - $key');
      }
      if (extraKeys.length > 3) {
        print('      ... and ${extraKeys.length - 3} more');
      }
      warnings += extraKeys.length;
    }

    // Check each translation
    var placeholderErrors = 0;
    var lengthWarnings = 0;

    for (final key in translationKeys) {
      if (!sourceKeys.contains(key)) continue;

      final sourceValue = sourceJson[key] as String?;
      final translatedValue = json[key] as String?;

      if (sourceValue == null || translatedValue == null) continue;

      // Check for placeholders
      final sourcePlaceholders = _extractPlaceholders(sourceValue);
      final translatedPlaceholders = _extractPlaceholders(translatedValue);

      if (!_placeholdersMatch(sourcePlaceholders, translatedPlaceholders)) {
        print('   ❌ Placeholder mismatch in "$key"');
        print('      Source: $sourcePlaceholders');
        print('      Translation: $translatedPlaceholders');
        placeholderErrors++;
      }

      // Check length constraints
      for (final pattern in maxLengths.keys) {
        if (key.contains(pattern)) {
          final maxLength = maxLengths[pattern]!;
          if (translatedValue.length > maxLength) {
            print('   ⚠️  Length warning for "$key": ${translatedValue.length} > $maxLength chars');
            lengthWarnings++;
          }
          break;
        }
      }

      // Check for markdown preservation
      if (sourceValue.contains('**') || sourceValue.contains('_')) {
        if (!translatedValue.contains('**') && !translatedValue.contains('_')) {
          print('   ⚠️  Missing markdown formatting in "$key"');
          warnings++;
        }
      }

      // Check if still in English (simple heuristic)
      if (translatedValue == sourceValue && translatedValue.split(' ').length > 3) {
        print('   ⚠️  Possibly untranslated: "$key" = "$translatedValue"');
        warnings++;
      }
    }

    errors += placeholderErrors;
    warnings += lengthWarnings;

    if (errors == 0 && warnings == 0) {
      print('   ✅ No issues found');
    }

  } catch (e) {
    print('   ❌ Error processing file: $e');
    errors++;
  }

  print('');
  return {'errors': errors, 'warnings': warnings};
}

List<String> _extractPlaceholders(String text) {
  final regex = RegExp(r'\{(\w+)\}');
  final matches = regex.allMatches(text);
  return matches.map((m) => m.group(1)!).toList()..sort();
}

bool _placeholdersMatch(List<String> source, List<String> translated) {
  if (source.length != translated.length) return false;
  for (var i = 0; i < source.length; i++) {
    if (source[i] != translated[i]) return false;
  }
  return true;
}

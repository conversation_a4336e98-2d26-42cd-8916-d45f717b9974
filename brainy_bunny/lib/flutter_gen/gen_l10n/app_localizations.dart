import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_ar.dart';
import 'app_localizations_ca.dart';
import 'app_localizations_de.dart';
import 'app_localizations_en.dart';
import 'app_localizations_es.dart';
import 'app_localizations_fr.dart';
import 'app_localizations_hi.dart';
import 'app_localizations_id.dart';
import 'app_localizations_it.dart';
import 'app_localizations_ja.dart';
import 'app_localizations_ko.dart';
import 'app_localizations_pt.dart';
import 'app_localizations_ru.dart';
import 'app_localizations_zh.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'gen_l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
      : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('ar'),
    Locale('ca'),
    Locale('de'),
    Locale('en'),
    Locale('es'),
    Locale('es', 'MX'),
    Locale('fr'),
    Locale('fr', 'CA'),
    Locale('hi'),
    Locale('id'),
    Locale('it'),
    Locale('ja'),
    Locale('ko'),
    Locale('pt'),
    Locale('pt', 'BR'),
    Locale('ru'),
    Locale('zh'),
    Locale.fromSubtags(languageCode: 'zh', scriptCode: 'Hans'),
    Locale.fromSubtags(languageCode: 'zh', scriptCode: 'Hant')
  ];

  /// Main headline on welcome screen - per publisher guidelines
  ///
  /// In en, this message translates to:
  /// **'Welcome to Brainy Bunny'**
  String get onboarding_welcome_headline;

  /// Subheading on welcome screen - per publisher guidelines
  ///
  /// In en, this message translates to:
  /// **'Educational matching games for little learners'**
  String get onboarding_welcome_subheading;

  /// Call to action button on welcome screen - per publisher guidelines
  ///
  /// In en, this message translates to:
  /// **'Let\'s begin'**
  String get onboarding_welcome_cta;

  /// Headline for name input screen
  ///
  /// In en, this message translates to:
  /// **'Let\'s personalize your experience'**
  String get onboarding_name_headline;

  /// Hint text for name input field
  ///
  /// In en, this message translates to:
  /// **'Your name'**
  String get onboarding_name_hint;

  /// Quick select button for Mom
  ///
  /// In en, this message translates to:
  /// **'Mom'**
  String get onboarding_name_mom;

  /// Quick select button for Dad
  ///
  /// In en, this message translates to:
  /// **'Dad'**
  String get onboarding_name_dad;

  /// Quick select button for Parent
  ///
  /// In en, this message translates to:
  /// **'Parent'**
  String get onboarding_name_parent;

  /// Greeting after parent enters name
  ///
  /// In en, this message translates to:
  /// **'Great, {name}!'**
  String onboarding_name_greeting(String name);

  /// Headline for child age selection - per publisher guidelines
  ///
  /// In en, this message translates to:
  /// **'How old is your little one?'**
  String get onboarding_child_age_headline;

  /// Subtext for age selection screen
  ///
  /// In en, this message translates to:
  /// **'All games work for ages 2-5. This helps us provide age-appropriate tips.'**
  String get onboarding_child_age_subtext;

  /// Option for 2-year-old child
  ///
  /// In en, this message translates to:
  /// **'2 years old'**
  String get onboarding_age_2;

  /// Option for 3-year-old child
  ///
  /// In en, this message translates to:
  /// **'3 years old'**
  String get onboarding_age_3;

  /// Option for 4-year-old child
  ///
  /// In en, this message translates to:
  /// **'4 years old'**
  String get onboarding_age_4;

  /// Option for 5 or older child
  ///
  /// In en, this message translates to:
  /// **'5+ years old'**
  String get onboarding_age_5_plus;

  /// Educational philosophy headline
  ///
  /// In en, this message translates to:
  /// **'Transform screen time into learning time'**
  String get onboarding_philosophy_headline;

  /// American Academy of Pediatrics compliance statement
  ///
  /// In en, this message translates to:
  /// **'Aligned with AAP screen time recommendations'**
  String get onboarding_philosophy_aap;

  /// Learning benefit statement
  ///
  /// In en, this message translates to:
  /// **'Turn screen time into meaningful learning experiences'**
  String get onboarding_philosophy_learning;

  /// Skills development statement
  ///
  /// In en, this message translates to:
  /// **'Develop cognitive skills through play-based learning'**
  String get onboarding_philosophy_skills;

  /// Instruction to rotate device before demo games
  ///
  /// In en, this message translates to:
  /// **'Let\'s play! Rotate your device →'**
  String get onboarding_transition_to_games;

  /// Instruction to rotate device after demo games
  ///
  /// In en, this message translates to:
  /// **'Great learning! Let\'s finish up →'**
  String get onboarding_transition_from_games;

  /// Main headline on solution introduction screen
  ///
  /// In en, this message translates to:
  /// **'Learning through play'**
  String get onboarding_solution_headline;

  /// Description text on solution introduction screen
  ///
  /// In en, this message translates to:
  /// **'While they match shapes and colors, they\'re building real cognitive skills.'**
  String get onboarding_solution_description;

  /// Benefit 1: Active engagement
  ///
  /// In en, this message translates to:
  /// **'Active engagement'**
  String get onboarding_solution_benefit_engagement;

  /// Benefit 2: Pattern recognition
  ///
  /// In en, this message translates to:
  /// **'Pattern recognition'**
  String get onboarding_solution_benefit_pattern_recognition;

  /// Benefit 3: Cause-and-effect thinking
  ///
  /// In en, this message translates to:
  /// **'Cause-and-effect thinking'**
  String get onboarding_solution_benefit_cause_effect;

  /// Benefit 4: Screen time alignment
  ///
  /// In en, this message translates to:
  /// **'Aligned with 1-hour screen time guidelines'**
  String get onboarding_solution_benefit_screen_time;

  /// Research backing statement on solution introduction screen
  ///
  /// In en, this message translates to:
  /// **'Research shows: Matching activities improve spatial reasoning...'**
  String get onboarding_solution_research_text;

  /// Research source note on solution introduction screen
  ///
  /// In en, this message translates to:
  /// **'Based on developmental psychology studies'**
  String get onboarding_solution_research_source;

  /// Continue button text on solution introduction screen
  ///
  /// In en, this message translates to:
  /// **'See how it works'**
  String get onboarding_solution_cta;

  /// Main headline on problem screen - empathetic toward parents
  ///
  /// In en, this message translates to:
  /// **'We understand your\nscreen time concerns'**
  String get onboarding_problem_headline;

  /// Problem point 1 title
  ///
  /// In en, this message translates to:
  /// **'Passive video consumption'**
  String get onboarding_problem_point_1_title;

  /// Problem point 1 description
  ///
  /// In en, this message translates to:
  /// **'Hours of mindless watching with zero interaction or learning'**
  String get onboarding_problem_point_1_description;

  /// Problem point 1 statistic
  ///
  /// In en, this message translates to:
  /// **'6x higher risk of language delays'**
  String get onboarding_problem_point_1_statistic;

  /// Problem point 2 title
  ///
  /// In en, this message translates to:
  /// **'Brain development damage'**
  String get onboarding_problem_point_2_title;

  /// Problem point 2 description
  ///
  /// In en, this message translates to:
  /// **'Excessive screen time alters white matter structure in developing brains'**
  String get onboarding_problem_point_2_description;

  /// Problem point 2 statistic
  ///
  /// In en, this message translates to:
  /// **'Reduced cognitive abilities'**
  String get onboarding_problem_point_2_statistic;

  /// Problem point 3 title
  ///
  /// In en, this message translates to:
  /// **'Inappropriate content exposure'**
  String get onboarding_problem_point_3_title;

  /// Problem point 3 description
  ///
  /// In en, this message translates to:
  /// **'Ads, violence, and age-inappropriate material in \'kids\' content'**
  String get onboarding_problem_point_3_description;

  /// Problem point 3 statistic
  ///
  /// In en, this message translates to:
  /// **'85% of kids\' apps contain ads'**
  String get onboarding_problem_point_3_statistic;

  /// Problem point 4 title
  ///
  /// In en, this message translates to:
  /// **'Attention & focus problems'**
  String get onboarding_problem_point_4_title;

  /// Problem point 4 description
  ///
  /// In en, this message translates to:
  /// **'Fast-paced content destroys ability to concentrate and learn'**
  String get onboarding_problem_point_4_description;

  /// Problem point 4 statistic
  ///
  /// In en, this message translates to:
  /// **'40% increase in ADHD symptoms'**
  String get onboarding_problem_point_4_statistic;

  /// Research section title on problem screen
  ///
  /// In en, this message translates to:
  /// **'Scientific Evidence'**
  String get onboarding_problem_research_title;

  /// Research backing statement on problem screen - compact version
  ///
  /// In en, this message translates to:
  /// **'Peer-reviewed studies confirm: excessive passive screen time causes brain changes, language delays, and attention problems in young children.'**
  String get onboarding_problem_research_text;

  /// Subtext on problem screen - more empathetic
  ///
  /// In en, this message translates to:
  /// **'You\'re not alone. 89% of parents share these concerns.'**
  String get onboarding_problem_subtext;

  /// Continue button text on problem screen
  ///
  /// In en, this message translates to:
  /// **'There\'s a better way'**
  String get onboarding_problem_cta;

  /// Skill name for demo game 1
  ///
  /// In en, this message translates to:
  /// **'Visual Matching & Shape Recognition'**
  String get demo_game_1_skill;

  /// Educational explanation for demo game 1
  ///
  /// In en, this message translates to:
  /// **'Matching animals to silhouettes develops visual discrimination - the ability to notice differences between similar shapes. This skill is foundational for letter recognition and reading.'**
  String get demo_game_1_science;

  /// Research citation for demo game 1
  ///
  /// In en, this message translates to:
  /// **'Bornstein (1985) - Visual processing and categorical thinking'**
  String get demo_game_1_citation;

  /// Badge name awarded after completing demo game 1
  ///
  /// In en, this message translates to:
  /// **'Shape Detective'**
  String get demo_game_1_badge;

  /// Skill name for demo game 2
  ///
  /// In en, this message translates to:
  /// **'Visual Memory & Attention'**
  String get demo_game_2_skill;

  /// Educational explanation for demo game 2
  ///
  /// In en, this message translates to:
  /// **'Finding matching pairs strengthens working memory - your child\'s ability to hold and manipulate information. This directly supports math problem-solving and following multi-step instructions.'**
  String get demo_game_2_science;

  /// Research citation for demo game 2
  ///
  /// In en, this message translates to:
  /// **'Gopnik & Meltzoff (1987) - Categorization and cognitive flexibility'**
  String get demo_game_2_citation;

  /// Badge name awarded after completing demo game 2
  ///
  /// In en, this message translates to:
  /// **'Memory Master'**
  String get demo_game_2_badge;

  /// Skill name for demo game 3
  ///
  /// In en, this message translates to:
  /// **'Logical Association & Categorization'**
  String get demo_game_3_skill;

  /// Educational explanation for demo game 3
  ///
  /// In en, this message translates to:
  /// **'Connecting objects to their users teaches categorization and logical thinking. Your child learns that things belong together for reasons - a key step in understanding cause and effect.'**
  String get demo_game_3_science;

  /// Research citation for demo game 3
  ///
  /// In en, this message translates to:
  /// **'Piaget (1952) - Pre-operational cognitive development'**
  String get demo_game_3_citation;

  /// Badge name awarded after completing demo game 3
  ///
  /// In en, this message translates to:
  /// **'Logic Star'**
  String get demo_game_3_badge;

  /// Skill name for demo game 4
  ///
  /// In en, this message translates to:
  /// **'Pattern Recognition & Matching'**
  String get demo_game_4_skill;

  /// Educational explanation for demo game 4
  ///
  /// In en, this message translates to:
  /// **'Matching patterns builds pattern recognition - the ability to see relationships between things. This skill strongly predicts math success and helps children understand \'same\' vs \'different.\''**
  String get demo_game_4_science;

  /// Research citation for demo game 4
  ///
  /// In en, this message translates to:
  /// **'Rittle-Johnson et al. (2019) - Pattern skills and mathematics'**
  String get demo_game_4_citation;

  /// Badge name awarded after completing demo game 4
  ///
  /// In en, this message translates to:
  /// **'Pattern Pro'**
  String get demo_game_4_badge;

  /// Skill name for demo game 5
  ///
  /// In en, this message translates to:
  /// **'Symbolic Thinking & Real-World Connections'**
  String get demo_game_5_skill;

  /// Educational explanation for demo game 5
  ///
  /// In en, this message translates to:
  /// **'Connecting tools to careers builds symbolic thinking - understanding that one thing can represent another. This abstract thinking is essential for language, math, and imagination.'**
  String get demo_game_5_science;

  /// Research citation for demo game 5
  ///
  /// In en, this message translates to:
  /// **'Vygotsky (1978) - Symbolic representation in cognitive development'**
  String get demo_game_5_citation;

  /// Badge name awarded after completing demo game 5
  ///
  /// In en, this message translates to:
  /// **'World Explorer'**
  String get demo_game_5_badge;

  /// Button to proceed to next demo game
  ///
  /// In en, this message translates to:
  /// **'Next Game'**
  String get demo_game_next;

  /// Personalized summary headline
  ///
  /// In en, this message translates to:
  /// **'Your Journey So Far, {name}'**
  String onboarding_summary_headline(String name);

  /// Age-appropriate confirmation
  ///
  /// In en, this message translates to:
  /// **'Perfect for {age}-year-olds'**
  String onboarding_summary_age(int age);

  /// Summary of skills practiced
  ///
  /// In en, this message translates to:
  /// **'5 skills practiced'**
  String get onboarding_summary_skills;

  /// Screen time approach statement
  ///
  /// In en, this message translates to:
  /// **'Healthy screen time approach aligned with AAP guidelines'**
  String get onboarding_summary_screen_time;

  /// Call to action from summary screen
  ///
  /// In en, this message translates to:
  /// **'See Your Personalized Plan'**
  String get onboarding_summary_cta;

  /// Headline for trust proof screen
  ///
  /// In en, this message translates to:
  /// **'Trusted by Parents Worldwide'**
  String get trust_headline;

  /// American Academy of Pediatrics approval badge
  ///
  /// In en, this message translates to:
  /// **'AAP Approved'**
  String get trust_approved;

  /// Call to action from trust screen
  ///
  /// In en, this message translates to:
  /// **'Unlock Full Learning Experience'**
  String get trust_cta;

  /// Main paywall headline
  ///
  /// In en, this message translates to:
  /// **'Unlock All 15 Learning Games'**
  String get paywall_headline;

  /// Paywall subheadline
  ///
  /// In en, this message translates to:
  /// **'Continue your child\'s learning journey'**
  String get paywall_subheadline;

  /// Feature: number of games
  ///
  /// In en, this message translates to:
  /// **'15 educational games targeting key skills'**
  String get paywall_feature_games;

  /// Feature: progress tracking
  ///
  /// In en, this message translates to:
  /// **'Progress tracking for your child'**
  String get paywall_feature_progress;

  /// Feature: no ads
  ///
  /// In en, this message translates to:
  /// **'Ad-free, safe environment'**
  String get paywall_feature_ad_free;

  /// Feature: regular updates
  ///
  /// In en, this message translates to:
  /// **'New games added monthly'**
  String get paywall_feature_new_games;

  /// Feature: age range
  ///
  /// In en, this message translates to:
  /// **'Designed for ages 2-5'**
  String get paywall_feature_ages;

  /// Trial offer headline
  ///
  /// In en, this message translates to:
  /// **'Try All Features FREE for 7 Days'**
  String get paywall_trial_headline;

  /// Price after trial
  ///
  /// In en, this message translates to:
  /// **'Then just €0.87/week'**
  String get paywall_trial_price;

  /// Trial timeline: day 0
  ///
  /// In en, this message translates to:
  /// **'Today: Full Access Unlocked'**
  String get paywall_trial_today;

  /// Trial timeline: day 5
  ///
  /// In en, this message translates to:
  /// **'Day 5: We\'ll send a reminder'**
  String get paywall_trial_day5;

  /// Trial timeline: day 7
  ///
  /// In en, this message translates to:
  /// **'Day 7: Billing starts (cancel anytime before)'**
  String get paywall_trial_day7;

  /// No immediate payment notice
  ///
  /// In en, this message translates to:
  /// **'✓ No Payment Due Now'**
  String get paywall_trial_no_payment;

  /// Start trial button
  ///
  /// In en, this message translates to:
  /// **'Start Free Trial →'**
  String get paywall_trial_cta;

  /// Weekly subscription plan name
  ///
  /// In en, this message translates to:
  /// **'Weekly'**
  String get paywall_weekly;

  /// Monthly subscription plan name
  ///
  /// In en, this message translates to:
  /// **'Monthly'**
  String get paywall_monthly;

  /// Yearly subscription plan name
  ///
  /// In en, this message translates to:
  /// **'Yearly'**
  String get paywall_yearly;

  /// Savings badge for yearly plan
  ///
  /// In en, this message translates to:
  /// **'Save 60%'**
  String get paywall_save_60;

  /// Most popular plan indicator
  ///
  /// In en, this message translates to:
  /// **'Most popular choice'**
  String get paywall_most_popular;

  /// Cancel anytime text
  ///
  /// In en, this message translates to:
  /// **'Cancel anytime from your device settings'**
  String get paywall_cancel_anytime;

  /// Restore purchases link
  ///
  /// In en, this message translates to:
  /// **'Restore Purchases'**
  String get paywall_restore;

  /// Premium subscription badge
  ///
  /// In en, this message translates to:
  /// **'Premium'**
  String get subscription_premium;

  /// Trial subscription badge
  ///
  /// In en, this message translates to:
  /// **'7-Day Trial'**
  String get subscription_trial;

  /// Lifetime access badge for grandfathered users
  ///
  /// In en, this message translates to:
  /// **'Lifetime Access'**
  String get subscription_lifetime;

  /// Expired subscription indicator
  ///
  /// In en, this message translates to:
  /// **'Subscription Ended'**
  String get subscription_expired;

  /// Button to manage subscription
  ///
  /// In en, this message translates to:
  /// **'Manage Subscription'**
  String get subscription_manage;

  /// Button to cancel subscription
  ///
  /// In en, this message translates to:
  /// **'Cancel Subscription'**
  String get subscription_cancel;

  /// Button to change subscription plan
  ///
  /// In en, this message translates to:
  /// **'Change Plan'**
  String get subscription_change_plan;

  /// Generic continue button
  ///
  /// In en, this message translates to:
  /// **'Continue'**
  String get continue_button;

  /// Generic skip button
  ///
  /// In en, this message translates to:
  /// **'Skip'**
  String get skip_button;

  /// Generic close button
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get close_button;

  /// Loading indicator text
  ///
  /// In en, this message translates to:
  /// **'Loading...'**
  String get loading;

  /// Error message when purchase fails
  ///
  /// In en, this message translates to:
  /// **'Purchase failed. If you already own this, try restarting the app.'**
  String get error_purchase_failed;

  /// Purchase failure detailed message
  ///
  /// In en, this message translates to:
  /// **'We couldn\'t complete your purchase. Please try again.'**
  String get error_purchase_failed_message;

  /// Restore purchases failure
  ///
  /// In en, this message translates to:
  /// **'No purchases found'**
  String get error_restore_failed;

  /// Restore failure detailed message
  ///
  /// In en, this message translates to:
  /// **'We couldn\'t find any previous purchases. If you believe this is an error, please contact support.'**
  String get error_restore_failed_message;

  /// Network connection error
  ///
  /// In en, this message translates to:
  /// **'Network error'**
  String get error_network;

  /// Network error detailed message
  ///
  /// In en, this message translates to:
  /// **'Please check your internet connection and try again.'**
  String get error_network_message;

  /// Headline for personalized summary screen
  ///
  /// In en, this message translates to:
  /// **'Amazing Progress!'**
  String get summary_headline;

  /// Personalized message for parents of 2-year-olds
  ///
  /// In en, this message translates to:
  /// **'Great job, {name}! Your little one is building important skills through play. At age 2, every match they make strengthens their visual recognition and problem-solving abilities.'**
  String summary_message_age_2(Object name);

  /// Personalized message for parents of 3-year-olds
  ///
  /// In en, this message translates to:
  /// **'Wonderful, {name}! Your child is developing crucial cognitive skills. At age 3, these activities enhance their memory, focus, and logical thinking.'**
  String summary_message_age_3(Object name);

  /// Personalized message for parents of 4-year-olds
  ///
  /// In en, this message translates to:
  /// **'Excellent work, {name}! Your 4-year-old is mastering advanced problem-solving. These games are preparing them for kindergarten success.'**
  String summary_message_age_4(Object name);

  /// Personalized message for parents of 5+ year-olds
  ///
  /// In en, this message translates to:
  /// **'Fantastic, {name}! Your child is excelling at complex thinking. These skills will give them a strong foundation for school.'**
  String summary_message_age_5_plus(Object name);

  /// Label for badges section
  ///
  /// In en, this message translates to:
  /// **'Badges Earned'**
  String get summary_badges_earned;

  /// Stat label for badge count
  ///
  /// In en, this message translates to:
  /// **'Badges'**
  String get summary_badges;

  /// Stat label for games played
  ///
  /// In en, this message translates to:
  /// **'Games'**
  String get summary_games;

  /// Stat label for skill development
  ///
  /// In en, this message translates to:
  /// **'Skills'**
  String get summary_skills;

  /// Description of AAP approval
  ///
  /// In en, this message translates to:
  /// **'Our educational approach aligns with AAP guidelines for healthy screen time and early childhood development.'**
  String get trust_aap_description;

  /// Title for research-backed curriculum section
  ///
  /// In en, this message translates to:
  /// **'Research-Backed Curriculum'**
  String get trust_research_title;

  /// Description of research backing
  ///
  /// In en, this message translates to:
  /// **'Every game is designed based on peer-reviewed studies in cognitive development, featuring methods proven to enhance learning in children ages 2-5.'**
  String get trust_research_description;

  /// Name of first testimonial author
  ///
  /// In en, this message translates to:
  /// **'Sarah M., Mother of 3-year-old'**
  String get trust_testimonial_1_name;

  /// First parent testimonial
  ///
  /// In en, this message translates to:
  /// **'\"My daughter went from struggling with shapes to confidently identifying them everywhere. The progress in just 2 weeks amazed me!\"'**
  String get trust_testimonial_1_quote;

  /// Name of second testimonial author
  ///
  /// In en, this message translates to:
  /// **'Michael T., Father of 4-year-old'**
  String get trust_testimonial_2_name;

  /// Second parent testimonial
  ///
  /// In en, this message translates to:
  /// **'\"Finally, screen time I feel good about! My son is learning while having fun, and I can see real cognitive improvements.\"'**
  String get trust_testimonial_2_quote;

  /// Download stats title
  ///
  /// In en, this message translates to:
  /// **'Join 100,000+ Families'**
  String get trust_downloads_title;

  /// Download stats description
  ///
  /// In en, this message translates to:
  /// **'Trusted by parents in over 50 countries to give their children a head start in learning.'**
  String get trust_downloads_description;

  /// CTA headline on trust screen
  ///
  /// In en, this message translates to:
  /// **'Ready to unlock your child\'s full potential?'**
  String get trust_cta_headline;

  /// CTA button on trust screen
  ///
  /// In en, this message translates to:
  /// **'Start Free Trial'**
  String get trust_cta_button;

  /// Premium badge label
  ///
  /// In en, this message translates to:
  /// **'Premium Access'**
  String get paywall_premium_badge;

  /// Headline for paywall step 1
  ///
  /// In en, this message translates to:
  /// **'Unlock Your Child\'s Full Potential'**
  String get paywall_step1_headline;

  /// Value prop 1 title
  ///
  /// In en, this message translates to:
  /// **'15 Premium Games'**
  String get paywall_value_1_title;

  /// Value prop 1 description
  ///
  /// In en, this message translates to:
  /// **'Access all educational games designed for ages 2-5, covering shapes, colors, numbers, logic, and more'**
  String get paywall_value_1_description;

  /// Value prop 2 title
  ///
  /// In en, this message translates to:
  /// **'Track Progress'**
  String get paywall_value_2_title;

  /// Value prop 2 description
  ///
  /// In en, this message translates to:
  /// **'Detailed analytics showing your child\'s development and skill improvement over time'**
  String get paywall_value_2_description;

  /// Value prop 3 title
  ///
  /// In en, this message translates to:
  /// **'Personalized Learning'**
  String get paywall_value_3_title;

  /// Value prop 3 description
  ///
  /// In en, this message translates to:
  /// **'Games adapt to your child\'s age and skill level for optimal learning'**
  String get paywall_value_3_description;

  /// Value prop 4 title
  ///
  /// In en, this message translates to:
  /// **'New Content Monthly'**
  String get paywall_value_4_title;

  /// Value prop 4 description
  ///
  /// In en, this message translates to:
  /// **'Regular updates with new games and activities to keep learning fresh and engaging'**
  String get paywall_value_4_description;

  /// CTA button for paywall step 1
  ///
  /// In en, this message translates to:
  /// **'See Plans'**
  String get paywall_step1_cta;

  /// Payment security badge
  ///
  /// In en, this message translates to:
  /// **'Secure payment processing'**
  String get paywall_secure_payment;

  /// Free trial badge
  ///
  /// In en, this message translates to:
  /// **'7-Day Free Trial'**
  String get paywall_trial_badge;

  /// Headline for paywall step 2
  ///
  /// In en, this message translates to:
  /// **'Choose Your Plan'**
  String get paywall_step2_headline;

  /// Subheadline for paywall step 2
  ///
  /// In en, this message translates to:
  /// **'Start your 7-day free trial. Cancel anytime.'**
  String get paywall_step2_subheadline;

  /// Badge for best value plan
  ///
  /// In en, this message translates to:
  /// **'Best Value'**
  String get paywall_plan_best_value;

  /// Yearly plan title
  ///
  /// In en, this message translates to:
  /// **'Yearly'**
  String get paywall_plan_yearly_title;

  /// Yearly plan period
  ///
  /// In en, this message translates to:
  /// **'/year'**
  String get paywall_plan_yearly_period;

  /// Yearly plan monthly equivalent
  ///
  /// In en, this message translates to:
  /// **'Just €3.33/month'**
  String get paywall_plan_yearly_per_month;

  /// Yearly plan savings
  ///
  /// In en, this message translates to:
  /// **'Save 63% compared to monthly'**
  String get paywall_plan_yearly_savings;

  /// Monthly plan title
  ///
  /// In en, this message translates to:
  /// **'Monthly'**
  String get paywall_plan_monthly_title;

  /// Monthly plan period
  ///
  /// In en, this message translates to:
  /// **'/month'**
  String get paywall_plan_monthly_period;

  /// Weekly plan title
  ///
  /// In en, this message translates to:
  /// **'Weekly'**
  String get paywall_plan_weekly_title;

  /// Weekly plan period
  ///
  /// In en, this message translates to:
  /// **'/week'**
  String get paywall_plan_weekly_period;

  /// Weekly plan note
  ///
  /// In en, this message translates to:
  /// **'For short-term access'**
  String get paywall_plan_weekly_note;

  /// Trial reminder text
  ///
  /// In en, this message translates to:
  /// **'Your free trial starts today. You won\'t be charged until day 8. Cancel anytime before then with no cost.'**
  String get paywall_trial_reminder;

  /// CTA button for paywall step 2
  ///
  /// In en, this message translates to:
  /// **'Continue'**
  String get paywall_step2_cta;

  /// Terms and privacy text
  ///
  /// In en, this message translates to:
  /// **'By continuing, you agree to our Terms of Service and Privacy Policy'**
  String get paywall_terms;

  /// Urgency text
  ///
  /// In en, this message translates to:
  /// **'Limited Time Offer'**
  String get paywall_urgency_text;

  /// Headline for paywall step 3
  ///
  /// In en, this message translates to:
  /// **'You\'re One Step Away!'**
  String get paywall_step3_headline;

  /// Title for included features section
  ///
  /// In en, this message translates to:
  /// **'Everything You Get:'**
  String get paywall_step3_included_title;

  /// Included feature 1
  ///
  /// In en, this message translates to:
  /// **'All 15 premium educational games'**
  String get paywall_included_1;

  /// Included feature 2
  ///
  /// In en, this message translates to:
  /// **'Personalized learning paths for your child'**
  String get paywall_included_2;

  /// Included feature 3
  ///
  /// In en, this message translates to:
  /// **'Detailed progress tracking and insights'**
  String get paywall_included_3;

  /// Included feature 4
  ///
  /// In en, this message translates to:
  /// **'Monthly new content and activities'**
  String get paywall_included_4;

  /// Included feature 5
  ///
  /// In en, this message translates to:
  /// **'Ad-free experience for focused learning'**
  String get paywall_included_5;

  /// Included feature 6
  ///
  /// In en, this message translates to:
  /// **'Offline mode - learn anywhere, anytime'**
  String get paywall_included_6;

  /// Guarantee title
  ///
  /// In en, this message translates to:
  /// **'100% Risk-Free Guarantee'**
  String get paywall_guarantee_title;

  /// Guarantee text
  ///
  /// In en, this message translates to:
  /// **'Try it free for 7 days. If you\'re not completely satisfied, cancel before the trial ends and pay nothing. No questions asked.'**
  String get paywall_guarantee_text;

  /// CTA button for paywall step 3
  ///
  /// In en, this message translates to:
  /// **'Start My Free Trial'**
  String get paywall_step3_cta;

  /// Pre-paywall headline
  ///
  /// In en, this message translates to:
  /// **'Your Learning Journey is Ready!'**
  String get pre_paywall_headline;

  /// Pre-paywall subheadline with child's name
  ///
  /// In en, this message translates to:
  /// **'Here\'s what we\'ve prepared for {name}:'**
  String pre_paywall_subheadline_personalized(String name);

  /// Pre-paywall subheadline with child's age
  ///
  /// In en, this message translates to:
  /// **'Here\'s what we\'ve prepared for your {age}-year-old:'**
  String pre_paywall_subheadline_age(String age);

  /// Pre-paywall subheadline generic
  ///
  /// In en, this message translates to:
  /// **'Here\'s what we\'ve prepared for your child:'**
  String get pre_paywall_subheadline_generic;

  /// Pre-paywall card 1 title
  ///
  /// In en, this message translates to:
  /// **'Age-Appropriate Content'**
  String get pre_paywall_card_1_title;

  /// Pre-paywall card 1 subtitle with age
  ///
  /// In en, this message translates to:
  /// **'Perfect for {age} years old'**
  String pre_paywall_card_1_subtitle_age(String age);

  /// Pre-paywall card 1 subtitle generic
  ///
  /// In en, this message translates to:
  /// **'All 15 games work great for ages 2-5'**
  String get pre_paywall_card_1_subtitle_generic;

  /// Pre-paywall card 2 title
  ///
  /// In en, this message translates to:
  /// **'Learning Focus'**
  String get pre_paywall_card_2_title;

  /// Pre-paywall card 2 subtitle
  ///
  /// In en, this message translates to:
  /// **'Building skills through play'**
  String get pre_paywall_card_2_subtitle;

  /// Pre-paywall card 3 title
  ///
  /// In en, this message translates to:
  /// **'15 Educational Games'**
  String get pre_paywall_card_3_title;

  /// Pre-paywall card 3 subtitle
  ///
  /// In en, this message translates to:
  /// **'Shapes, colors, patterns, animals & more'**
  String get pre_paywall_card_3_subtitle;

  /// Pre-paywall key benefit statement
  ///
  /// In en, this message translates to:
  /// **'Transform screen time from guilt into growth—educational content you can feel good about.'**
  String get pre_paywall_key_benefit;

  /// Pre-paywall trust badge 1
  ///
  /// In en, this message translates to:
  /// **'Teacher Approved'**
  String get pre_paywall_trust_1;

  /// Pre-paywall trust badge 2
  ///
  /// In en, this message translates to:
  /// **'Ad-Free'**
  String get pre_paywall_trust_2;

  /// Pre-paywall trust badge 3 - Designed with child development specialists
  ///
  /// In en, this message translates to:
  /// **'Expert-Backed'**
  String get pre_paywall_trust_3;

  /// Pre-paywall primary CTA
  ///
  /// In en, this message translates to:
  /// **'Start 7-Day FREE Trial'**
  String get pre_paywall_cta_primary;

  /// Pre-paywall primary CTA subtext
  ///
  /// In en, this message translates to:
  /// **'Unlock all 15 games • No charge today'**
  String get pre_paywall_cta_primary_subtext;

  /// Pre-paywall secondary CTA
  ///
  /// In en, this message translates to:
  /// **'Continue with 5 free games'**
  String get pre_paywall_cta_secondary;

  /// Pre-paywall important note
  ///
  /// In en, this message translates to:
  /// **'Trial includes all 15 games. After trial: Keep 5 free games or subscribe for full access.'**
  String get pre_paywall_important_note;

  /// Message for rotating to landscape mode
  ///
  /// In en, this message translates to:
  /// **'Rotate your device to landscape'**
  String get onboarding_rotate_to_landscape;

  /// Message for rotating to portrait mode
  ///
  /// In en, this message translates to:
  /// **'Rotate your device to portrait'**
  String get onboarding_rotate_to_portrait;

  /// Title for demo game 1
  ///
  /// In en, this message translates to:
  /// **'Animal Shapes'**
  String get demo_game_1_title;

  /// Educational context for demo game 1
  ///
  /// In en, this message translates to:
  /// **'Help your child match animals to their shapes! This builds visual recognition skills essential for reading.'**
  String get demo_game_1_context;

  /// Title for demo game 2
  ///
  /// In en, this message translates to:
  /// **'Memory Match'**
  String get demo_game_2_title;

  /// Educational context for demo game 2
  ///
  /// In en, this message translates to:
  /// **'Find matching pairs to strengthen working memory - crucial for following instructions and problem-solving.'**
  String get demo_game_2_context;

  /// Title for demo game 3
  ///
  /// In en, this message translates to:
  /// **'Logic Puzzles'**
  String get demo_game_3_title;

  /// Educational context for demo game 3
  ///
  /// In en, this message translates to:
  /// **'Solve puzzles to develop logical thinking and pattern recognition skills.'**
  String get demo_game_3_context;

  /// Title for demo game 4
  ///
  /// In en, this message translates to:
  /// **'Pattern Fun'**
  String get demo_game_4_title;

  /// Educational context for demo game 4
  ///
  /// In en, this message translates to:
  /// **'Recognize patterns to build math readiness and sequencing skills.'**
  String get demo_game_4_context;

  /// Title for demo game 5
  ///
  /// In en, this message translates to:
  /// **'World Explorer'**
  String get demo_game_5_title;

  /// Educational context for demo game 5
  ///
  /// In en, this message translates to:
  /// **'Explore the world to expand vocabulary and cultural awareness.'**
  String get demo_game_5_context;

  /// Congratulations message after completing demo game
  ///
  /// In en, this message translates to:
  /// **'Amazing! You earned a badge!'**
  String get demo_game_congratulations;

  /// In-game paywall benefit: access to all games
  ///
  /// In en, this message translates to:
  /// **'Access to all {gameCount} games'**
  String paywall_benefit_all_games(int gameCount);

  /// In-game paywall benefit: age-appropriate content
  ///
  /// In en, this message translates to:
  /// **'Age-appropriate content'**
  String get paywall_benefit_age_appropriate;

  /// In-game paywall benefit: progress tracking
  ///
  /// In en, this message translates to:
  /// **'Progress tracking'**
  String get paywall_benefit_progress_tracking;

  /// In-game paywall benefit: offline play
  ///
  /// In en, this message translates to:
  /// **'Offline play supported'**
  String get paywall_benefit_offline_play;

  /// In-game paywall benefit: no ads
  ///
  /// In en, this message translates to:
  /// **'No ads, kid-safe'**
  String get paywall_benefit_no_ads;

  /// In-game paywall benefit: regular updates
  ///
  /// In en, this message translates to:
  /// **'Regular updates'**
  String get paywall_benefit_regular_updates;

  /// Button text to start free trial
  ///
  /// In en, this message translates to:
  /// **'Start Free Trial'**
  String get paywall_start_trial;

  /// Final confirmation screen benefit 1
  ///
  /// In en, this message translates to:
  /// **'Full access to all learning games'**
  String get paywall_step3_benefit_1;

  /// Final confirmation screen benefit 2
  ///
  /// In en, this message translates to:
  /// **'Ad-free, safe learning environment'**
  String get paywall_step3_benefit_2;

  /// Final confirmation screen benefit 3
  ///
  /// In en, this message translates to:
  /// **'Perfect for the whole family'**
  String get paywall_step3_benefit_3;

  /// Subscribe button text on final step
  ///
  /// In en, this message translates to:
  /// **'Subscribe Now'**
  String get paywall_subscribe_button;

  /// Trial explanation screen headline
  ///
  /// In en, this message translates to:
  /// **'Try All 15 Games Free for 7 Days'**
  String get trial_explanation_headline;

  /// Trial explanation feature 1
  ///
  /// In en, this message translates to:
  /// **'Full access to all 15 educational games'**
  String get trial_explanation_feature_1;

  /// Trial explanation feature 2
  ///
  /// In en, this message translates to:
  /// **'No charge for 7 days'**
  String get trial_explanation_feature_2;

  /// Trial explanation feature 3
  ///
  /// In en, this message translates to:
  /// **'We\'ll remind you 2 days before trial ends'**
  String get trial_explanation_feature_3;

  /// Trial explanation feature 4
  ///
  /// In en, this message translates to:
  /// **'Cancel anytime during trial - no cost'**
  String get trial_explanation_feature_4;

  /// Trial explanation subtext
  ///
  /// In en, this message translates to:
  /// **'You won\'t be charged until day 8 of your trial'**
  String get trial_explanation_subtext;

  /// Trial explanation CTA button
  ///
  /// In en, this message translates to:
  /// **'See Plans'**
  String get trial_explanation_cta;

  /// Unified paywall headline
  ///
  /// In en, this message translates to:
  /// **'Choose Your Plan'**
  String get unified_paywall_headline;

  /// Unified paywall subheadline
  ///
  /// In en, this message translates to:
  /// **'All plans unlock 15 educational games'**
  String get unified_paywall_subheadline;

  /// Yearly plan save badge
  ///
  /// In en, this message translates to:
  /// **'Save 60%'**
  String get unified_paywall_yearly_badge_save;

  /// Yearly plan trial badge
  ///
  /// In en, this message translates to:
  /// **'7-Day Free Trial'**
  String get unified_paywall_yearly_badge_trial;

  /// Yearly plan title
  ///
  /// In en, this message translates to:
  /// **'Yearly'**
  String get unified_paywall_yearly_title;

  /// Yearly plan monthly equivalent price
  ///
  /// In en, this message translates to:
  /// **'Just {monthlyEquivalent}/month'**
  String unified_paywall_yearly_price_breakdown(String monthlyEquivalent);

  /// Yearly plan savings text
  ///
  /// In en, this message translates to:
  /// **'Save 63% • Try free for 7 days'**
  String get unified_paywall_yearly_savings;

  /// Yearly plan feature 1
  ///
  /// In en, this message translates to:
  /// **'No charge for 7 days'**
  String get unified_paywall_yearly_feature_1;

  /// Yearly plan feature 2
  ///
  /// In en, this message translates to:
  /// **'Cancel anytime during trial'**
  String get unified_paywall_yearly_feature_2;

  /// Yearly plan feature 3
  ///
  /// In en, this message translates to:
  /// **'Full access to all 15 games'**
  String get unified_paywall_yearly_feature_3;

  /// Yearly plan button text
  ///
  /// In en, this message translates to:
  /// **'Start Free Trial'**
  String get unified_paywall_yearly_button;

  /// Monthly plan title
  ///
  /// In en, this message translates to:
  /// **'Monthly'**
  String get unified_paywall_monthly_title;

  /// Monthly plan weekly equivalent
  ///
  /// In en, this message translates to:
  /// **'{weeklyEquivalent}/week'**
  String unified_paywall_monthly_per_week(String weeklyEquivalent);

  /// Monthly plan savings text
  ///
  /// In en, this message translates to:
  /// **'Flexible monthly plan'**
  String get unified_paywall_monthly_savings;

  /// Monthly plan feature 1
  ///
  /// In en, this message translates to:
  /// **'Cancel anytime'**
  String get unified_paywall_monthly_feature_1;

  /// Monthly plan feature 2
  ///
  /// In en, this message translates to:
  /// **'Full access to all 15 games'**
  String get unified_paywall_monthly_feature_2;

  /// Monthly plan button text
  ///
  /// In en, this message translates to:
  /// **'Subscribe Monthly'**
  String get unified_paywall_monthly_button;

  /// Weekly plan title
  ///
  /// In en, this message translates to:
  /// **'Weekly'**
  String get unified_paywall_weekly_title;

  /// Weekly plan savings text
  ///
  /// In en, this message translates to:
  /// **'Try for just one week'**
  String get unified_paywall_weekly_savings;

  /// Weekly plan feature 1
  ///
  /// In en, this message translates to:
  /// **'Cancel anytime'**
  String get unified_paywall_weekly_feature_1;

  /// Weekly plan feature 2
  ///
  /// In en, this message translates to:
  /// **'Full access to all 15 games'**
  String get unified_paywall_weekly_feature_2;

  /// Weekly plan button text
  ///
  /// In en, this message translates to:
  /// **'Subscribe Weekly'**
  String get unified_paywall_weekly_button;

  /// Unified paywall trust element 1
  ///
  /// In en, this message translates to:
  /// **'Secure payment processing'**
  String get unified_paywall_trust_1;

  /// Unified paywall trust element 2
  ///
  /// In en, this message translates to:
  /// **'Manage in App/Play Store'**
  String get unified_paywall_trust_2;

  /// Unified paywall trust element 3
  ///
  /// In en, this message translates to:
  /// **'All plans include full access'**
  String get unified_paywall_trust_3;

  /// Unified paywall restore purchases
  ///
  /// In en, this message translates to:
  /// **'Restore Purchases'**
  String get unified_paywall_restore;

  /// Unified paywall terms text
  ///
  /// In en, this message translates to:
  /// **'By continuing, you agree to our Terms of Service and Privacy Policy'**
  String get unified_paywall_terms;

  /// Educational value screen headline - per publisher guidelines
  ///
  /// In en, this message translates to:
  /// **'Learning through play, not mindless entertainment'**
  String get educational_value_headline;

  /// Educational value point 1 title
  ///
  /// In en, this message translates to:
  /// **'Develops pattern recognition & logical thinking'**
  String get educational_value_point_1_title;

  /// Educational value point 1 description
  ///
  /// In en, this message translates to:
  /// **'Essential skills for math readiness and problem-solving'**
  String get educational_value_point_1_description;

  /// Educational value point 2 title
  ///
  /// In en, this message translates to:
  /// **'Strengthens visual discrimination skills'**
  String get educational_value_point_2_title;

  /// Educational value point 2 description
  ///
  /// In en, this message translates to:
  /// **'Helps children identify differences and similarities'**
  String get educational_value_point_2_description;

  /// Educational value point 3 title
  ///
  /// In en, this message translates to:
  /// **'Builds problem-solving abilities'**
  String get educational_value_point_3_title;

  /// Educational value point 3 description
  ///
  /// In en, this message translates to:
  /// **'Active engagement develops critical thinking'**
  String get educational_value_point_3_description;

  /// Educational value point 4 title
  ///
  /// In en, this message translates to:
  /// **'Designed for healthy screen time'**
  String get educational_value_point_4_title;

  /// Educational value point 4 description
  ///
  /// In en, this message translates to:
  /// **'Aligned with pediatric recommendation of 1 hour daily'**
  String get educational_value_point_4_description;

  /// Research backing statement - per publisher guidelines
  ///
  /// In en, this message translates to:
  /// **'Research shows: Matching activities improve spatial reasoning and cognitive development in children ages 2-5.'**
  String get educational_value_research;

  /// Research source note
  ///
  /// In en, this message translates to:
  /// **'Based on developmental psychology studies'**
  String get educational_value_research_source;

  /// Value carousel screen 1 headline - per publisher guidelines
  ///
  /// In en, this message translates to:
  /// **'Screen time that builds skills'**
  String get value_carousel_1_headline;

  /// Value carousel screen 1 description - per publisher guidelines
  ///
  /// In en, this message translates to:
  /// **'15 different matching games teaching shapes, colors, patterns, animals, professions, and cause-and-effect thinking.'**
  String get value_carousel_1_description;

  /// Value carousel screen 1 subtext
  ///
  /// In en, this message translates to:
  /// **'Not just entertainment—actual learning in every match.'**
  String get value_carousel_1_subtext;

  /// Benefit 1 title
  ///
  /// In en, this message translates to:
  /// **'Memory Development'**
  String get value_carousel_1_benefit_1_title;

  /// Benefit 1 description
  ///
  /// In en, this message translates to:
  /// **'Matching games that strengthen recall and recognition'**
  String get value_carousel_1_benefit_1_description;

  /// Benefit 2 title
  ///
  /// In en, this message translates to:
  /// **'Problem Solving'**
  String get value_carousel_1_benefit_2_title;

  /// Benefit 2 description
  ///
  /// In en, this message translates to:
  /// **'Puzzles that encourage logical thinking and strategy'**
  String get value_carousel_1_benefit_2_description;

  /// Benefit 3 title
  ///
  /// In en, this message translates to:
  /// **'Progressive Learning'**
  String get value_carousel_1_benefit_3_title;

  /// Benefit 3 description
  ///
  /// In en, this message translates to:
  /// **'Difficulty adapts as your child masters new skills'**
  String get value_carousel_1_benefit_3_description;

  /// Value carousel screen 2 headline - per publisher guidelines
  ///
  /// In en, this message translates to:
  /// **'15 ways to learn and grow'**
  String get value_carousel_2_headline;

  /// Value carousel screen 2 description - per publisher guidelines
  ///
  /// In en, this message translates to:
  /// **'From simple shape matching to understanding relationships between objects and situations—each game targets specific developmental skills.'**
  String get value_carousel_2_description;

  /// Game category 1
  ///
  /// In en, this message translates to:
  /// **'Shapes & Geometry'**
  String get value_carousel_2_category_1;

  /// Game category 2
  ///
  /// In en, this message translates to:
  /// **'Colors & Patterns'**
  String get value_carousel_2_category_2;

  /// Game category 3
  ///
  /// In en, this message translates to:
  /// **'Animals & Nature'**
  String get value_carousel_2_category_3;

  /// Game category 4
  ///
  /// In en, this message translates to:
  /// **'Professions & Roles'**
  String get value_carousel_2_category_4;

  /// Game category 5
  ///
  /// In en, this message translates to:
  /// **'Cause & Effect'**
  String get value_carousel_2_category_5;

  /// Additional categories indicator
  ///
  /// In en, this message translates to:
  /// **'And more...'**
  String get value_carousel_2_category_6;

  /// Feature 1 title
  ///
  /// In en, this message translates to:
  /// **'100% Ad-Free'**
  String get value_carousel_2_feature_1_title;

  /// Feature 1 description
  ///
  /// In en, this message translates to:
  /// **'No advertisements, no distractions, no inappropriate content'**
  String get value_carousel_2_feature_1_description;

  /// Feature 2 title
  ///
  /// In en, this message translates to:
  /// **'Age-Appropriate'**
  String get value_carousel_2_feature_2_title;

  /// Feature 2 description
  ///
  /// In en, this message translates to:
  /// **'Content designed specifically for ages 2-5'**
  String get value_carousel_2_feature_2_description;

  /// Feature 3 title
  ///
  /// In en, this message translates to:
  /// **'Active Learning'**
  String get value_carousel_2_feature_3_title;

  /// Feature 3 description
  ///
  /// In en, this message translates to:
  /// **'Engaging activities, not passive watching'**
  String get value_carousel_2_feature_3_description;

  /// Value carousel screen 3 headline - per publisher guidelines
  ///
  /// In en, this message translates to:
  /// **'Teacher Approved, Parent Trusted'**
  String get value_carousel_3_headline;

  /// Trust element 1
  ///
  /// In en, this message translates to:
  /// **'Ad-free learning environment'**
  String get value_carousel_3_trust_element_1;

  /// Trust element 2
  ///
  /// In en, this message translates to:
  /// **'No data collection'**
  String get value_carousel_3_trust_element_2;

  /// Trust element 3
  ///
  /// In en, this message translates to:
  /// **'Based on child development expert recommendations'**
  String get value_carousel_3_trust_element_3;

  /// Trust element 4
  ///
  /// In en, this message translates to:
  /// **'Safe for young children'**
  String get value_carousel_3_trust_element_4;

  /// Feature 1 title
  ///
  /// In en, this message translates to:
  /// **'Progress Tracking'**
  String get value_carousel_3_feature_1_title;

  /// Feature 1 description
  ///
  /// In en, this message translates to:
  /// **'See which skills your child is developing'**
  String get value_carousel_3_feature_1_description;

  /// Feature 2 title
  ///
  /// In en, this message translates to:
  /// **'Achievement Rewards'**
  String get value_carousel_3_feature_2_title;

  /// Feature 2 description
  ///
  /// In en, this message translates to:
  /// **'Earn stars and badges that motivate continued learning'**
  String get value_carousel_3_feature_2_description;

  /// Feature 3 title
  ///
  /// In en, this message translates to:
  /// **'Personalized Experience'**
  String get value_carousel_3_feature_3_title;

  /// Feature 3 description
  ///
  /// In en, this message translates to:
  /// **'Games that adapt to your child\'s skill level'**
  String get value_carousel_3_feature_3_description;

  /// Summary screen headline
  ///
  /// In en, this message translates to:
  /// **'Perfect! Here\'s Your Personalized Learning Path'**
  String get summary_headline_new;

  /// Learning path section title
  ///
  /// In en, this message translates to:
  /// **'What Your Child Will Learn:'**
  String get summary_learning_path_title;

  /// Cognitive skill title
  ///
  /// In en, this message translates to:
  /// **'Cognitive Development'**
  String get summary_skill_cognitive;

  /// Cognitive skill description
  ///
  /// In en, this message translates to:
  /// **'Memory games and problem-solving activities perfect for age {age}'**
  String summary_skill_cognitive_desc(int age);

  /// Visual skill title
  ///
  /// In en, this message translates to:
  /// **'Visual Perception'**
  String get summary_skill_visual;

  /// Visual skill description
  ///
  /// In en, this message translates to:
  /// **'Shape recognition and spatial awareness games for {age}-year-olds'**
  String summary_skill_visual_desc(int age);

  /// Exploration skill title
  ///
  /// In en, this message translates to:
  /// **'Exploration & Discovery'**
  String get summary_skill_exploration;

  /// Exploration skill description
  ///
  /// In en, this message translates to:
  /// **'Interactive games that encourage curiosity at age {age}'**
  String summary_skill_exploration_desc(int age);

  /// Next step callout
  ///
  /// In en, this message translates to:
  /// **'Next: Try Premium free for 7 days to unlock all games!'**
  String get summary_next_step;

  /// Trial badge text
  ///
  /// In en, this message translates to:
  /// **'7-Day Free Trial'**
  String get trial_badge;

  /// Trial explanation headline
  ///
  /// In en, this message translates to:
  /// **'Try Premium Free for 7 Days'**
  String get trial_headline;

  /// Trial explanation description
  ///
  /// In en, this message translates to:
  /// **'Get full access to all premium games and features. Cancel anytime during your trial—no charges if you cancel before it ends.'**
  String get trial_description;

  /// Trial feature 1 title
  ///
  /// In en, this message translates to:
  /// **'All Premium Games'**
  String get trial_feature_1_title;

  /// Trial feature 1 description
  ///
  /// In en, this message translates to:
  /// **'Access every learning game in our library'**
  String get trial_feature_1_description;

  /// Trial feature 2 title
  ///
  /// In en, this message translates to:
  /// **'Ad-Free Experience'**
  String get trial_feature_2_title;

  /// Trial feature 2 description
  ///
  /// In en, this message translates to:
  /// **'Safe learning environment with no advertisements'**
  String get trial_feature_2_description;

  /// Trial feature 3 title
  ///
  /// In en, this message translates to:
  /// **'Progress Tracking'**
  String get trial_feature_3_title;

  /// Trial feature 3 description
  ///
  /// In en, this message translates to:
  /// **'See your child\'s development and achievements'**
  String get trial_feature_3_description;

  /// Trial feature 4 title
  ///
  /// In en, this message translates to:
  /// **'Regular Updates'**
  String get trial_feature_4_title;

  /// Trial feature 4 description
  ///
  /// In en, this message translates to:
  /// **'New games and features added regularly'**
  String get trial_feature_4_description;

  /// How it works section title
  ///
  /// In en, this message translates to:
  /// **'How It Works:'**
  String get trial_how_it_works_title;

  /// Trial step 1
  ///
  /// In en, this message translates to:
  /// **'Start your free 7-day trial today'**
  String get trial_step_1;

  /// Trial step 2
  ///
  /// In en, this message translates to:
  /// **'Enjoy full access to all premium features'**
  String get trial_step_2;

  /// Trial step 3
  ///
  /// In en, this message translates to:
  /// **'Cancel anytime—no charges before trial ends'**
  String get trial_step_3;

  /// Trial CTA button
  ///
  /// In en, this message translates to:
  /// **'Start My Free Trial'**
  String get trial_cta;

  /// Trial disclaimer text
  ///
  /// In en, this message translates to:
  /// **'Free for 7 days, then your selected plan rate. Cancel anytime.'**
  String get trial_disclaimer;

  /// Notification permission headline - per publisher guidelines
  ///
  /// In en, this message translates to:
  /// **'Never miss a learning moment'**
  String get notification_permission_headline;

  /// Notification permission description
  ///
  /// In en, this message translates to:
  /// **'We\'ll send gentle reminders for:'**
  String get notification_permission_description;

  /// Notification benefit 1 title
  ///
  /// In en, this message translates to:
  /// **'Trial Reminders'**
  String get notification_benefit_1_title;

  /// Notification benefit 1 description
  ///
  /// In en, this message translates to:
  /// **'Get notified before your trial ends so you never lose access'**
  String get notification_benefit_1_description;

  /// Notification benefit 2 title
  ///
  /// In en, this message translates to:
  /// **'Learning Milestones'**
  String get notification_benefit_2_title;

  /// Notification benefit 2 description
  ///
  /// In en, this message translates to:
  /// **'Celebrate when your child reaches new achievements'**
  String get notification_benefit_2_description;

  /// Notification benefit 3 title
  ///
  /// In en, this message translates to:
  /// **'Engagement Tips'**
  String get notification_benefit_3_title;

  /// Notification benefit 3 description
  ///
  /// In en, this message translates to:
  /// **'Get suggestions for keeping learning fun and engaging'**
  String get notification_benefit_3_description;

  /// Notification privacy note
  ///
  /// In en, this message translates to:
  /// **'We respect your privacy. You can disable notifications anytime in settings.'**
  String get notification_privacy_note;

  /// Enable notifications button
  ///
  /// In en, this message translates to:
  /// **'Enable Notifications'**
  String get notification_enable_button;

  /// Skip notifications button
  ///
  /// In en, this message translates to:
  /// **'Maybe Later'**
  String get notification_maybe_later;

  /// Subscription management screen title
  ///
  /// In en, this message translates to:
  /// **'Manage Subscription'**
  String get subscription_management_title;

  /// Active subscription status
  ///
  /// In en, this message translates to:
  /// **'Premium Active'**
  String get subscription_status_active;

  /// Active subscription description
  ///
  /// In en, this message translates to:
  /// **'You have full access to all premium features'**
  String get subscription_status_active_description;

  /// Inactive subscription status
  ///
  /// In en, this message translates to:
  /// **'Free Version'**
  String get subscription_status_inactive;

  /// Inactive subscription description
  ///
  /// In en, this message translates to:
  /// **'Upgrade to premium for full access to all games'**
  String get subscription_status_inactive_description;

  /// Actions section title
  ///
  /// In en, this message translates to:
  /// **'Actions'**
  String get subscription_actions_title;

  /// Restore purchases title
  ///
  /// In en, this message translates to:
  /// **'Restore Purchases'**
  String get subscription_restore_title;

  /// Restore purchases description
  ///
  /// In en, this message translates to:
  /// **'Already subscribed on another device? Restore your purchases here.'**
  String get subscription_restore_description;

  /// Restore button text
  ///
  /// In en, this message translates to:
  /// **'Restore'**
  String get subscription_restore_button;

  /// Manage subscription title
  ///
  /// In en, this message translates to:
  /// **'Manage Your Subscription'**
  String get subscription_manage_title;

  /// Manage subscription description
  ///
  /// In en, this message translates to:
  /// **'View, change, or cancel your subscription through your app store account.'**
  String get subscription_manage_description;

  /// Manage subscription button
  ///
  /// In en, this message translates to:
  /// **'Open Subscription Settings'**
  String get subscription_manage_button;

  /// Help section title
  ///
  /// In en, this message translates to:
  /// **'Help & Information'**
  String get subscription_help_title;

  /// Cancel guide title
  ///
  /// In en, this message translates to:
  /// **'How to Cancel'**
  String get subscription_cancel_title;

  /// Cancel guide description
  ///
  /// In en, this message translates to:
  /// **'You can cancel your subscription anytime through your App Store or Google Play account settings. You\'ll keep access until the end of your billing period.'**
  String get subscription_cancel_description;

  /// Payment failure help title
  ///
  /// In en, this message translates to:
  /// **'Payment Issues'**
  String get subscription_payment_failure_title;

  /// Payment failure help description
  ///
  /// In en, this message translates to:
  /// **'If your payment fails, update your payment method in your app store account. We\'ll retry the payment automatically.'**
  String get subscription_payment_failure_description;

  /// Next button text
  ///
  /// In en, this message translates to:
  /// **'Next'**
  String get next_button;

  /// Back button text
  ///
  /// In en, this message translates to:
  /// **'Back'**
  String get back_button;

  /// Parent priorities question - per publisher guidelines
  ///
  /// In en, this message translates to:
  /// **'What\'s most important to you?'**
  String get onboarding_priority_question;

  /// Priority option 1
  ///
  /// In en, this message translates to:
  /// **'Educational screen time'**
  String get onboarding_priority_1;

  /// Priority option 1 subtext
  ///
  /// In en, this message translates to:
  /// **'Learning while playing'**
  String get onboarding_priority_1_sub;

  /// Priority option 2
  ///
  /// In en, this message translates to:
  /// **'School readiness'**
  String get onboarding_priority_2;

  /// Priority option 2 subtext
  ///
  /// In en, this message translates to:
  /// **'Preparing for preschool/kindergarten'**
  String get onboarding_priority_2_sub;

  /// Priority option 3
  ///
  /// In en, this message translates to:
  /// **'Keeping them engaged'**
  String get onboarding_priority_3;

  /// Priority option 3 subtext
  ///
  /// In en, this message translates to:
  /// **'Productive, happy screen time'**
  String get onboarding_priority_3_sub;

  /// Priority option 4
  ///
  /// In en, this message translates to:
  /// **'Learning through play'**
  String get onboarding_priority_4;

  /// Priority option 4 subtext
  ///
  /// In en, this message translates to:
  /// **'Fun that builds skills'**
  String get onboarding_priority_4_sub;

  /// Transition screen message
  ///
  /// In en, this message translates to:
  /// **'Thank you for sharing!'**
  String get onboarding_transition_message;

  /// Transition screen submessage
  ///
  /// In en, this message translates to:
  /// **'We\'re creating the perfect experience for your {age}-year-old...'**
  String onboarding_transition_submessage(int age);

  /// Summary results headline - per publisher guidelines
  ///
  /// In en, this message translates to:
  /// **'Perfect for your {age}-year old!'**
  String summary_result_headline(int age);

  /// Summary card 1 title
  ///
  /// In en, this message translates to:
  /// **'What They\'ll Learn:'**
  String get summary_card_1_title;

  /// Learning point 1
  ///
  /// In en, this message translates to:
  /// **'Pattern recognition through 15 different games'**
  String get summary_card_1_point_1;

  /// Learning point 2
  ///
  /// In en, this message translates to:
  /// **'Visual discrimination skills'**
  String get summary_card_1_point_2;

  /// Learning point 3
  ///
  /// In en, this message translates to:
  /// **'Logical thinking and problem-solving'**
  String get summary_card_1_point_3;

  /// Learning point 4
  ///
  /// In en, this message translates to:
  /// **'Real-world connections (animals, professions, nature)'**
  String get summary_card_1_point_4;

  /// Summary card 2 title
  ///
  /// In en, this message translates to:
  /// **'Why It Works:'**
  String get summary_card_2_title;

  /// Why it works point 1
  ///
  /// In en, this message translates to:
  /// **'Active learning beats passive watching'**
  String get summary_card_2_point_1;

  /// Why it works point 2
  ///
  /// In en, this message translates to:
  /// **'Immediate feedback keeps them engaged'**
  String get summary_card_2_point_2;

  /// Why it works point 3
  ///
  /// In en, this message translates to:
  /// **'Variety prevents boredom'**
  String get summary_card_2_point_3;

  /// Why it works point 4
  ///
  /// In en, this message translates to:
  /// **'Aligned with screen time guidelines'**
  String get summary_card_2_point_4;

  /// Summary screen CTA button
  ///
  /// In en, this message translates to:
  /// **'See what\'s included'**
  String get summary_cta;

  /// Free trial explanation headline - per publisher guidelines
  ///
  /// In en, this message translates to:
  /// **'Try 5 games free, unlock 10 more'**
  String get free_trial_headline;

  /// Free section title
  ///
  /// In en, this message translates to:
  /// **'Start with 5 free matching games'**
  String get free_trial_free_section;

  /// Free feature 1
  ///
  /// In en, this message translates to:
  /// **'Shapes, colors, and more'**
  String get free_trial_free_point_1;

  /// Free feature 2
  ///
  /// In en, this message translates to:
  /// **'No time limit'**
  String get free_trial_free_point_2;

  /// Free feature 3
  ///
  /// In en, this message translates to:
  /// **'No credit card needed'**
  String get free_trial_free_point_3;

  /// Premium section title
  ///
  /// In en, this message translates to:
  /// **'Unlock all 15 games with a subscription'**
  String get free_trial_premium_section;

  /// Premium feature 1
  ///
  /// In en, this message translates to:
  /// **'Full game library'**
  String get free_trial_premium_point_1;

  /// Premium feature 2
  ///
  /// In en, this message translates to:
  /// **'New learning categories'**
  String get free_trial_premium_point_2;

  /// Premium feature 3
  ///
  /// In en, this message translates to:
  /// **'Cancel anytime'**
  String get free_trial_premium_point_3;

  /// Bottom message on free trial screen
  ///
  /// In en, this message translates to:
  /// **'Try the free games first—unlock more anytime!'**
  String get free_trial_bottom_message;

  /// Primary CTA - start free games
  ///
  /// In en, this message translates to:
  /// **'Start with free games'**
  String get free_trial_cta_primary;

  /// Secondary CTA - view subscriptions
  ///
  /// In en, this message translates to:
  /// **'See subscription options'**
  String get free_trial_cta_secondary;

  /// Paywall section 1 headline - per publisher guidelines
  ///
  /// In en, this message translates to:
  /// **'Ready to unlock all 15 learning games?'**
  String get paywall_section1_headline;

  /// Paywall feature 1
  ///
  /// In en, this message translates to:
  /// **'15 educational matching games'**
  String get paywall_section1_feature_1;

  /// Paywall feature 2
  ///
  /// In en, this message translates to:
  /// **'Shape, color, pattern & logic games'**
  String get paywall_section1_feature_2;

  /// Paywall feature 3
  ///
  /// In en, this message translates to:
  /// **'Animals, professions, nature themes'**
  String get paywall_section1_feature_3;

  /// Paywall feature 4
  ///
  /// In en, this message translates to:
  /// **'Cause-and-effect learning'**
  String get paywall_section1_feature_4;

  /// Paywall feature 5
  ///
  /// In en, this message translates to:
  /// **'No ads, no distractions'**
  String get paywall_section1_feature_5;

  /// Paywall feature 6
  ///
  /// In en, this message translates to:
  /// **'Teacher Approved'**
  String get paywall_section1_feature_6;

  /// Teacher approved badge text
  ///
  /// In en, this message translates to:
  /// **'Teacher Approved'**
  String get paywall_section2_badge;

  /// Social proof supporting text
  ///
  /// In en, this message translates to:
  /// **'Educators recognize Brainy Bunny for its developmental approach to early learning.'**
  String get paywall_section2_text;

  /// Weekly plan title
  ///
  /// In en, this message translates to:
  /// **'Weekly'**
  String get paywall_section3_weekly_title;

  /// Weekly plan subtext
  ///
  /// In en, this message translates to:
  /// **'Try it out'**
  String get paywall_section3_weekly_subtext;

  /// Weekly plan feature
  ///
  /// In en, this message translates to:
  /// **'Cancel anytime'**
  String get paywall_section3_weekly_feature;

  /// Weekly plan button
  ///
  /// In en, this message translates to:
  /// **'Subscribe'**
  String get paywall_section3_weekly_button;

  /// Yearly plan title
  ///
  /// In en, this message translates to:
  /// **'Yearly'**
  String get paywall_section3_yearly_title;

  /// Yearly plan savings badge
  ///
  /// In en, this message translates to:
  /// **'BEST VALUE - Save 60%'**
  String get paywall_section3_yearly_badge;

  /// Yearly plan trial highlight
  ///
  /// In en, this message translates to:
  /// **'7-day FREE trial'**
  String get paywall_section3_yearly_highlight;

  /// Yearly plan monthly breakdown
  ///
  /// In en, this message translates to:
  /// **'Just {monthlyEquivalent}/month'**
  String paywall_section3_yearly_breakdown(String monthlyEquivalent);

  /// Yearly plan fine print
  ///
  /// In en, this message translates to:
  /// **'Then {yearlyPrice} annually'**
  String paywall_section3_yearly_fine_print(String yearlyPrice);

  /// Yearly plan feature
  ///
  /// In en, this message translates to:
  /// **'Cancel during trial - no charge'**
  String get paywall_section3_yearly_feature;

  /// Yearly plan button
  ///
  /// In en, this message translates to:
  /// **'Start FREE trial'**
  String get paywall_section3_yearly_button;

  /// Monthly plan title
  ///
  /// In en, this message translates to:
  /// **'Monthly'**
  String get paywall_section3_monthly_title;

  /// Monthly plan subtext
  ///
  /// In en, this message translates to:
  /// **'Flexible option'**
  String get paywall_section3_monthly_subtext;

  /// Monthly plan feature
  ///
  /// In en, this message translates to:
  /// **'Cancel anytime'**
  String get paywall_section3_monthly_feature;

  /// Monthly plan button
  ///
  /// In en, this message translates to:
  /// **'Subscribe'**
  String get paywall_section3_monthly_button;

  /// Trust element 1
  ///
  /// In en, this message translates to:
  /// **'Secure payment'**
  String get paywall_trust_element_1;

  /// Trust element 2
  ///
  /// In en, this message translates to:
  /// **'Cancel anytime during trial'**
  String get paywall_trust_element_2;

  /// Trust element 3
  ///
  /// In en, this message translates to:
  /// **'Manage subscription in App Store/Play Store'**
  String get paywall_trust_element_3;

  /// Trust element 4
  ///
  /// In en, this message translates to:
  /// **'Charged only after trial ends (for yearly)'**
  String get paywall_trust_element_4;

  /// Important disclaimer below pricing
  ///
  /// In en, this message translates to:
  /// **'You won\'t be charged during your 7-day trial. Cancel anytime in your device settings.'**
  String get paywall_disclaimer;

  /// Link to dismiss paywall and use free version
  ///
  /// In en, this message translates to:
  /// **'Continue with free games'**
  String get paywall_continue_free_link;

  /// Parent gate dialog title - per publisher guidelines
  ///
  /// In en, this message translates to:
  /// **'Parent Verification Required'**
  String get parent_gate_title;

  /// Parent gate instruction
  ///
  /// In en, this message translates to:
  /// **'This purchase requires an adult. Please solve this problem:'**
  String get parent_gate_instruction;

  /// Parent gate input placeholder
  ///
  /// In en, this message translates to:
  /// **'Enter answer'**
  String get parent_gate_input_placeholder;

  /// Parent gate cancel button
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get parent_gate_cancel;

  /// Parent gate verify button
  ///
  /// In en, this message translates to:
  /// **'Verify'**
  String get parent_gate_verify;

  /// Parent gate error message
  ///
  /// In en, this message translates to:
  /// **'Incorrect answer. Please try again.'**
  String get parent_gate_error;

  /// Notification type 1
  ///
  /// In en, this message translates to:
  /// **'New game unlocks'**
  String get notification_type_1;

  /// Notification type 2
  ///
  /// In en, this message translates to:
  /// **'Learning streak milestones'**
  String get notification_type_2;

  /// Notification type 3
  ///
  /// In en, this message translates to:
  /// **'Trial ending reminder (if applicable)'**
  String get notification_type_3;

  /// Notification type 4
  ///
  /// In en, this message translates to:
  /// **'Daily learning encouragement'**
  String get notification_type_4;

  /// Special callout for trial users
  ///
  /// In en, this message translates to:
  /// **'We\'ll remind you 2 days before your trial ends, so you\'re never surprised.'**
  String get notification_trial_callout;

  /// Notification benefit 1
  ///
  /// In en, this message translates to:
  /// **'Stay consistent with learning'**
  String get notification_benefit_1;

  /// Notification benefit 2
  ///
  /// In en, this message translates to:
  /// **'Never miss trial deadline'**
  String get notification_benefit_2;

  /// Notification benefit 3
  ///
  /// In en, this message translates to:
  /// **'Celebrate progress together'**
  String get notification_benefit_3;

  /// Enable notifications button
  ///
  /// In en, this message translates to:
  /// **'Enable notifications'**
  String get notification_cta_enable;

  /// Skip notifications button
  ///
  /// In en, this message translates to:
  /// **'Not now'**
  String get notification_cta_skip;

  /// Title when loading subscription products
  ///
  /// In en, this message translates to:
  /// **'Loading Subscriptions'**
  String get subscription_error_loading_title;

  /// Description when loading subscription products
  ///
  /// In en, this message translates to:
  /// **'Please wait while we load subscription options...'**
  String get subscription_error_loading_description;

  /// Title when offline
  ///
  /// In en, this message translates to:
  /// **'No Internet Connection'**
  String get subscription_error_offline_title;

  /// Description when offline
  ///
  /// In en, this message translates to:
  /// **'Please check your internet connection and try again. You need to be online to subscribe.'**
  String get subscription_error_offline_description;

  /// Title when IAP service not available
  ///
  /// In en, this message translates to:
  /// **'Subscriptions Not Available'**
  String get subscription_error_not_available_title;

  /// Description when IAP service not available
  ///
  /// In en, this message translates to:
  /// **'In-app purchases are not available on this device. Please try again later or contact support.'**
  String get subscription_error_not_available_description;

  /// Title when products not found in store
  ///
  /// In en, this message translates to:
  /// **'Products Not Available'**
  String get subscription_error_products_not_found_title;

  /// Description when products not found
  ///
  /// In en, this message translates to:
  /// **'We couldn\'t load subscription products from the store. Please try again later.'**
  String get subscription_error_products_not_found_description;

  /// Title for unknown error
  ///
  /// In en, this message translates to:
  /// **'Something Went Wrong'**
  String get subscription_error_unknown_title;

  /// Description for unknown error
  ///
  /// In en, this message translates to:
  /// **'An unexpected error occurred. Please try again.'**
  String get subscription_error_unknown_description;

  /// Button to retry loading subscriptions
  ///
  /// In en, this message translates to:
  /// **'Try Again'**
  String get subscription_error_retry;

  /// Button to continue with free version
  ///
  /// In en, this message translates to:
  /// **'Continue with Free Games'**
  String get subscription_error_continue_free;

  /// Generic loading message
  ///
  /// In en, this message translates to:
  /// **'Loading...'**
  String get subscription_loading;

  /// Goal option: preschool preparation
  ///
  /// In en, this message translates to:
  /// **'Prepare for preschool/kindergarten'**
  String get goal_preschool_title;

  /// Goal option description: preschool
  ///
  /// In en, this message translates to:
  /// **'Building readiness skills'**
  String get goal_preschool_description;

  /// Goal option: cognitive development
  ///
  /// In en, this message translates to:
  /// **'Develop cognitive abilities'**
  String get goal_cognitive_title;

  /// Goal option description: cognitive
  ///
  /// In en, this message translates to:
  /// **'Pattern recognition & problem-solving'**
  String get goal_cognitive_description;

  /// Goal option: replace screen time
  ///
  /// In en, this message translates to:
  /// **'Replace passive screen time'**
  String get goal_replace_screen_time_title;

  /// Goal option description: screen time
  ///
  /// In en, this message translates to:
  /// **'Active learning instead of videos'**
  String get goal_replace_screen_time_description;

  /// Goal option: keep engaged
  ///
  /// In en, this message translates to:
  /// **'Keep them engaged & learning'**
  String get goal_keep_engaged_title;

  /// Goal option description: engagement
  ///
  /// In en, this message translates to:
  /// **'Fun that actually builds skills'**
  String get goal_keep_engaged_description;

  /// Personalized summary headline for age 2
  ///
  /// In en, this message translates to:
  /// **'Perfect for your 2-year-old explorer'**
  String get summary_age2_headline;

  /// Age 2 learning card title
  ///
  /// In en, this message translates to:
  /// **'What they\'ll learn'**
  String get summary_age2_card1_title;

  /// Age 2 learning point 1
  ///
  /// In en, this message translates to:
  /// **'Basic shape recognition (circles, squares, triangles)'**
  String get summary_age2_card1_point1;

  /// Age 2 learning point 2
  ///
  /// In en, this message translates to:
  /// **'Simple color matching'**
  String get summary_age2_card1_point2;

  /// Age 2 learning point 3
  ///
  /// In en, this message translates to:
  /// **'Hand-eye coordination through drag-and-drop'**
  String get summary_age2_card1_point3;

  /// Age 2 learning point 4
  ///
  /// In en, this message translates to:
  /// **'Cause and effect understanding'**
  String get summary_age2_card1_point4;

  /// Age 2 why it works card title
  ///
  /// In en, this message translates to:
  /// **'Why it works for age 2'**
  String get summary_age2_card2_title;

  /// Age 2 why it works point 1
  ///
  /// In en, this message translates to:
  /// **'Extra-large pieces perfect for tiny fingers'**
  String get summary_age2_card2_point1;

  /// Age 2 why it works point 2
  ///
  /// In en, this message translates to:
  /// **'Simple 1-2 pair matching to start'**
  String get summary_age2_card2_point2;

  /// Age 2 why it works point 3
  ///
  /// In en, this message translates to:
  /// **'Instant positive feedback builds confidence'**
  String get summary_age2_card2_point3;

  /// Age 2 why it works point 4
  ///
  /// In en, this message translates to:
  /// **'Sessions designed for 5-10 minute attention spans'**
  String get summary_age2_card2_point4;

  /// Personalized summary headline for age 3
  ///
  /// In en, this message translates to:
  /// **'Designed for your curious 3-year-old'**
  String get summary_age3_headline;

  /// Age 3 learning card title
  ///
  /// In en, this message translates to:
  /// **'What they\'ll learn'**
  String get summary_age3_card1_title;

  /// Age 3 learning point 1
  ///
  /// In en, this message translates to:
  /// **'Advanced shape recognition and sorting'**
  String get summary_age3_card1_point1;

  /// Age 3 learning point 2
  ///
  /// In en, this message translates to:
  /// **'Pattern identification'**
  String get summary_age3_card1_point2;

  /// Age 3 learning point 3
  ///
  /// In en, this message translates to:
  /// **'Color mixing and matching concepts'**
  String get summary_age3_card1_point3;

  /// Age 3 learning point 4
  ///
  /// In en, this message translates to:
  /// **'Early problem-solving skills'**
  String get summary_age3_card1_point4;

  /// Age 3 why it works card title
  ///
  /// In en, this message translates to:
  /// **'Why it works for age 3'**
  String get summary_age3_card2_title;

  /// Age 3 why it works point 1
  ///
  /// In en, this message translates to:
  /// **'Progressive difficulty grows with their skills'**
  String get summary_age3_card2_point1;

  /// Age 3 why it works point 2
  ///
  /// In en, this message translates to:
  /// **'Builds on preschool learning concepts'**
  String get summary_age3_card2_point2;

  /// Age 3 why it works point 3
  ///
  /// In en, this message translates to:
  /// **'Celebrates small wins to boost motivation'**
  String get summary_age3_card2_point3;

  /// Age 3 why it works point 4
  ///
  /// In en, this message translates to:
  /// **'Perfect for emerging independence'**
  String get summary_age3_card2_point4;

  /// Personalized summary headline for age 4
  ///
  /// In en, this message translates to:
  /// **'Tailored for your smart 4-year-old'**
  String get summary_age4_headline;

  /// Age 4 learning card title
  ///
  /// In en, this message translates to:
  /// **'What they\'ll learn'**
  String get summary_age4_card1_title;

  /// Age 4 learning point 1
  ///
  /// In en, this message translates to:
  /// **'Complex pattern recognition'**
  String get summary_age4_card1_point1;

  /// Age 4 learning point 2
  ///
  /// In en, this message translates to:
  /// **'Categorical thinking (animals, professions, objects)'**
  String get summary_age4_card1_point2;

  /// Age 4 learning point 3
  ///
  /// In en, this message translates to:
  /// **'Spatial reasoning and relationships'**
  String get summary_age4_card1_point3;

  /// Age 4 learning point 4
  ///
  /// In en, this message translates to:
  /// **'Pre-reading visual discrimination skills'**
  String get summary_age4_card1_point4;

  /// Age 4 why it works card title
  ///
  /// In en, this message translates to:
  /// **'Why it works for age 4'**
  String get summary_age4_card2_title;

  /// Age 4 why it works point 1
  ///
  /// In en, this message translates to:
  /// **'Challenges that match pre-K curriculum'**
  String get summary_age4_card2_point1;

  /// Age 4 why it works point 2
  ///
  /// In en, this message translates to:
  /// **'Multiple rounds build sustained focus'**
  String get summary_age4_card2_point2;

  /// Age 4 why it works point 3
  ///
  /// In en, this message translates to:
  /// **'Vocabulary expansion through themed games'**
  String get summary_age4_card2_point3;

  /// Age 4 why it works point 4
  ///
  /// In en, this message translates to:
  /// **'Prepares for kindergarten readiness'**
  String get summary_age4_card2_point4;

  /// Personalized summary headline for age 5+
  ///
  /// In en, this message translates to:
  /// **'Engaging games for your 5+ year-old'**
  String get summary_age5_headline;

  /// Age 5+ learning card title
  ///
  /// In en, this message translates to:
  /// **'What they\'ll learn'**
  String get summary_age5_card1_title;

  /// Age 5+ learning point 1
  ///
  /// In en, this message translates to:
  /// **'Advanced categorization and sorting'**
  String get summary_age5_card1_point1;

  /// Age 5+ learning point 2
  ///
  /// In en, this message translates to:
  /// **'Abstract pattern completion'**
  String get summary_age5_card1_point2;

  /// Age 5+ learning point 3
  ///
  /// In en, this message translates to:
  /// **'Critical thinking and strategy'**
  String get summary_age5_card1_point3;

  /// Age 5+ learning point 4
  ///
  /// In en, this message translates to:
  /// **'Visual memory enhancement'**
  String get summary_age5_card1_point4;

  /// Age 5+ why it works card title
  ///
  /// In en, this message translates to:
  /// **'Why it works for age 5+'**
  String get summary_age5_card2_title;

  /// Age 5+ why it works point 1
  ///
  /// In en, this message translates to:
  /// **'Kindergarten-level cognitive challenges'**
  String get summary_age5_card2_point1;

  /// Age 5+ why it works point 2
  ///
  /// In en, this message translates to:
  /// **'Builds confidence for school success'**
  String get summary_age5_card2_point2;

  /// Age 5+ why it works point 3
  ///
  /// In en, this message translates to:
  /// **'Reinforces classroom learning at home'**
  String get summary_age5_card2_point3;

  /// Age 5+ why it works point 4
  ///
  /// In en, this message translates to:
  /// **'Keeps advanced learners engaged'**
  String get summary_age5_card2_point4;

  /// Parental gate overlay title
  ///
  /// In en, this message translates to:
  /// **'Parental Verification'**
  String get parental_gate_overlay_title;

  /// Parental gate overlay instruction
  ///
  /// In en, this message translates to:
  /// **'Please solve this problem to continue:'**
  String get parental_gate_overlay_instruction;

  /// Error message when purchase verification fails
  ///
  /// In en, this message translates to:
  /// **'Purchase verification failed. Please restart the app.'**
  String get error_purchase_verification_failed;

  /// Paywall step 2 savings badge
  ///
  /// In en, this message translates to:
  /// **'Save 60%'**
  String get paywall_step2_badge_save;

  /// Paywall step 2 trial badge
  ///
  /// In en, this message translates to:
  /// **'7-Day Trial'**
  String get paywall_step2_badge_trial;

  /// Paywall step 2 yearly plan title
  ///
  /// In en, this message translates to:
  /// **'Yearly'**
  String get paywall_step2_yearly_title;

  /// Paywall step 2 yearly monthly equivalent
  ///
  /// In en, this message translates to:
  /// **'Just {price}/month'**
  String paywall_step2_yearly_per_month(String price);

  /// Paywall step 2 yearly savings text
  ///
  /// In en, this message translates to:
  /// **'Save 63% • Try free for 7 days'**
  String get paywall_step2_yearly_savings;

  /// Paywall step 2 yearly feature 1
  ///
  /// In en, this message translates to:
  /// **'No charge for 7 days'**
  String get paywall_step2_yearly_feature1;

  /// Paywall step 2 yearly feature 2
  ///
  /// In en, this message translates to:
  /// **'Cancel anytime during trial'**
  String get paywall_step2_yearly_feature2;

  /// Paywall step 2 yearly feature 3
  ///
  /// In en, this message translates to:
  /// **'We\'ll remind you 2 days before trial ends'**
  String get paywall_step2_yearly_feature3;

  /// Paywall step 2 yearly feature 4
  ///
  /// In en, this message translates to:
  /// **'Full access to all 15 games'**
  String get paywall_step2_yearly_feature4;

  /// Paywall step 2 yearly button text
  ///
  /// In en, this message translates to:
  /// **'Start FREE Trial'**
  String get paywall_step2_yearly_button;

  /// Paywall step 2 monthly plan title
  ///
  /// In en, this message translates to:
  /// **'Monthly'**
  String get paywall_step2_monthly_title;

  /// Paywall step 2 monthly weekly equivalent
  ///
  /// In en, this message translates to:
  /// **'{price}/week'**
  String paywall_step2_monthly_per_week(String price);

  /// Paywall step 2 monthly savings text
  ///
  /// In en, this message translates to:
  /// **'Flexible monthly plan'**
  String get paywall_step2_monthly_savings;

  /// Paywall step 2 monthly feature 1
  ///
  /// In en, this message translates to:
  /// **'Cancel anytime'**
  String get paywall_step2_monthly_feature1;

  /// Paywall step 2 monthly feature 2
  ///
  /// In en, this message translates to:
  /// **'Full access to all 15 games'**
  String get paywall_step2_monthly_feature2;

  /// Paywall step 2 monthly button text
  ///
  /// In en, this message translates to:
  /// **'Subscribe Monthly'**
  String get paywall_step2_monthly_button;

  /// Paywall step 2 weekly plan title
  ///
  /// In en, this message translates to:
  /// **'Weekly'**
  String get paywall_step2_weekly_title;

  /// Paywall step 2 weekly savings text
  ///
  /// In en, this message translates to:
  /// **'Try for just one week'**
  String get paywall_step2_weekly_savings;

  /// Paywall step 2 weekly feature 1
  ///
  /// In en, this message translates to:
  /// **'Cancel anytime'**
  String get paywall_step2_weekly_feature1;

  /// Paywall step 2 weekly feature 2
  ///
  /// In en, this message translates to:
  /// **'Full access to all 15 games'**
  String get paywall_step2_weekly_feature2;

  /// Paywall step 2 weekly button text
  ///
  /// In en, this message translates to:
  /// **'Subscribe Weekly'**
  String get paywall_step2_weekly_button;

  /// Pre-paywall headline with child's name
  ///
  /// In en, this message translates to:
  /// **'Unlock All Games for {childName}!'**
  String locked_game_headline_personalized(String childName);

  /// Pre-paywall headline without child's name
  ///
  /// In en, this message translates to:
  /// **'Unlock All 15 Educational Games!'**
  String get locked_game_headline_generic;

  /// Pre-paywall card 1 title with age
  ///
  /// In en, this message translates to:
  /// **'Perfect for Your {age}-Year-Old'**
  String locked_game_card1_title_age(int age);

  /// Pre-paywall card 1 title without age
  ///
  /// In en, this message translates to:
  /// **'Age-Appropriate Learning'**
  String get locked_game_card1_title_generic;

  /// Pre-paywall card 2 title
  ///
  /// In en, this message translates to:
  /// **'Building Cognitive Abilities'**
  String get locked_game_card2_title;

  /// Pre-paywall card 3 title
  ///
  /// In en, this message translates to:
  /// **'{count} More Games to Explore'**
  String locked_game_card3_title(int count);

  /// Pre-paywall card 3 subtitle
  ///
  /// In en, this message translates to:
  /// **'Unlock the full collection of educational games'**
  String get locked_game_card3_subtitle;

  /// Generic skill 1
  ///
  /// In en, this message translates to:
  /// **'Shape and color recognition'**
  String get locked_game_age_skill_1_generic;

  /// Generic skill 2
  ///
  /// In en, this message translates to:
  /// **'Problem-solving abilities'**
  String get locked_game_age_skill_2_generic;

  /// Generic skill 3
  ///
  /// In en, this message translates to:
  /// **'Hand-eye coordination'**
  String get locked_game_age_skill_3_generic;

  /// Generic skill 4
  ///
  /// In en, this message translates to:
  /// **'Memory and focus'**
  String get locked_game_age_skill_4_generic;

  /// Age 2 skill 1
  ///
  /// In en, this message translates to:
  /// **'Basic shape recognition'**
  String get locked_game_age_skill_1_age2;

  /// Age 2 skill 2
  ///
  /// In en, this message translates to:
  /// **'Simple color matching'**
  String get locked_game_age_skill_2_age2;

  /// Age 2 skill 3
  ///
  /// In en, this message translates to:
  /// **'Hand-eye coordination'**
  String get locked_game_age_skill_3_age2;

  /// Age 2 skill 4
  ///
  /// In en, this message translates to:
  /// **'Cause and effect understanding'**
  String get locked_game_age_skill_4_age2;

  /// Age 3 skill 1
  ///
  /// In en, this message translates to:
  /// **'Advanced shape sorting'**
  String get locked_game_age_skill_1_age3;

  /// Age 3 skill 2
  ///
  /// In en, this message translates to:
  /// **'Pattern identification'**
  String get locked_game_age_skill_2_age3;

  /// Age 3 skill 3
  ///
  /// In en, this message translates to:
  /// **'Color mixing concepts'**
  String get locked_game_age_skill_3_age3;

  /// Age 3 skill 4
  ///
  /// In en, this message translates to:
  /// **'Early problem-solving'**
  String get locked_game_age_skill_4_age3;

  /// Age 4 skill 1
  ///
  /// In en, this message translates to:
  /// **'Complex pattern recognition'**
  String get locked_game_age_skill_1_age4;

  /// Age 4 skill 2
  ///
  /// In en, this message translates to:
  /// **'Categorical thinking'**
  String get locked_game_age_skill_2_age4;

  /// Age 4 skill 3
  ///
  /// In en, this message translates to:
  /// **'Spatial reasoning'**
  String get locked_game_age_skill_3_age4;

  /// Age 4 skill 4
  ///
  /// In en, this message translates to:
  /// **'Pre-reading visual skills'**
  String get locked_game_age_skill_4_age4;

  /// Age 5+ skill 1
  ///
  /// In en, this message translates to:
  /// **'Advanced categorization'**
  String get locked_game_age_skill_1_age5;

  /// Age 5+ skill 2
  ///
  /// In en, this message translates to:
  /// **'Abstract pattern completion'**
  String get locked_game_age_skill_2_age5;

  /// Age 5+ skill 3
  ///
  /// In en, this message translates to:
  /// **'Critical thinking'**
  String get locked_game_age_skill_3_age5;

  /// Age 5+ skill 4
  ///
  /// In en, this message translates to:
  /// **'Visual memory enhancement'**
  String get locked_game_age_skill_4_age5;

  /// Card 2 default content
  ///
  /// In en, this message translates to:
  /// **'Develop essential cognitive skills through play'**
  String get locked_game_card2_content_default;

  /// Card 2 content for school readiness goal
  ///
  /// In en, this message translates to:
  /// **'Build skills for preschool and kindergarten success'**
  String get locked_game_card2_content_school;

  /// Card 2 content for cognitive development goal
  ///
  /// In en, this message translates to:
  /// **'Enhance memory, focus, and problem-solving abilities'**
  String get locked_game_card2_content_cognitive;

  /// Card 2 content for screen time replacement goal
  ///
  /// In en, this message translates to:
  /// **'Quality educational content that parents can feel good about'**
  String get locked_game_card2_content_screentime;

  /// Card 2 content for engagement goal
  ///
  /// In en, this message translates to:
  /// **'Keep your child engaged with fun, educational activities'**
  String get locked_game_card2_content_engagement;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>[
        'ar',
        'ca',
        'de',
        'en',
        'es',
        'fr',
        'hi',
        'id',
        'it',
        'ja',
        'ko',
        'pt',
        'ru',
        'zh'
      ].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when language+script codes are specified.
  switch (locale.languageCode) {
    case 'zh':
      {
        switch (locale.scriptCode) {
          case 'Hans':
            return AppLocalizationsZhHans();
          case 'Hant':
            return AppLocalizationsZhHant();
        }
        break;
      }
  }

  // Lookup logic when language+country codes are specified.
  switch (locale.languageCode) {
    case 'es':
      {
        switch (locale.countryCode) {
          case 'MX':
            return AppLocalizationsEsMx();
        }
        break;
      }
    case 'fr':
      {
        switch (locale.countryCode) {
          case 'CA':
            return AppLocalizationsFrCa();
        }
        break;
      }
    case 'pt':
      {
        switch (locale.countryCode) {
          case 'BR':
            return AppLocalizationsPtBr();
        }
        break;
      }
  }

  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'ar':
      return AppLocalizationsAr();
    case 'ca':
      return AppLocalizationsCa();
    case 'de':
      return AppLocalizationsDe();
    case 'en':
      return AppLocalizationsEn();
    case 'es':
      return AppLocalizationsEs();
    case 'fr':
      return AppLocalizationsFr();
    case 'hi':
      return AppLocalizationsHi();
    case 'id':
      return AppLocalizationsId();
    case 'it':
      return AppLocalizationsIt();
    case 'ja':
      return AppLocalizationsJa();
    case 'ko':
      return AppLocalizationsKo();
    case 'pt':
      return AppLocalizationsPt();
    case 'ru':
      return AppLocalizationsRu();
    case 'zh':
      return AppLocalizationsZh();
  }

  throw FlutterError(
      'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
      'an issue with the localizations generation tool. Please file an issue '
      'on GitHub with a reproducible sample app and the gen-l10n configuration '
      'that was used.');
}

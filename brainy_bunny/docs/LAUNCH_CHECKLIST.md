# Brainy Bunny - Launch Checklist

## Overview

This is the complete pre-launch checklist for Brainy Bunny. All items must be completed and verified before submitting to App Store.

**Target Launch Date:** [TBD]
**Version:** 1.0
**Build Number:** [TBD]

---

## Table of Contents

1. [Pre-Launch Setup](#pre-launch-setup)
2. [App Store Configuration](#app-store-configuration)
3. [Backend Services](#backend-services)
4. [Testing & QA](#testing--qa)
5. [Analytics & Monitoring](#analytics--monitoring)
6. [Legal & Compliance](#legal--compliance)
7. [Marketing Assets](#marketing-assets)
8. [Submission](#submission)
9. [Post-Launch](#post-launch)

---

## Pre-Launch Setup

### Code Completion

- [x] Phase 1-2: Foundation & Analytics ✅
- [x] Phase 3: Onboarding Flow (16 screens) ✅
- [x] Phase 4: Subscription System ✅
- [x] Phase 5: App Integration ✅
- [x] Phase 6: Bug Fixes ✅
- [x] Phase 7: Localization Infrastructure ✅
- [ ] Phase 8: Comprehensive Testing
- [ ] Phase 9: Final Polish

### Localization

**Tier 1 (Launch Critical):**
- [ ] Spanish (Spain) - `es`
- [ ] Spanish (Mexico) - `es_MX`
- [ ] French (France) - `fr`
- [ ] German - `de`
- [ ] Portuguese (Brazil) - `pt_BR`
- [ ] Italian - `it`

**Status:**
- [ ] Translations received from professional service
- [ ] Validation script run (0 errors)
- [ ] Native speaker review completed
- [ ] Tested in-app for all languages
- [ ] Screenshots created for each language

### Version & Build Configuration

- [ ] Version number set: `1.0.0`
- [ ] Build number incremented
- [ ] Bundle ID confirmed: `com.goodkarmalab.brainybunny`
- [ ] Signing certificates valid (check expiration)
- [ ] Provisioning profiles updated
- [ ] Push notification entitlement (if needed)
- [ ] In-App Purchase capability enabled

---

## App Store Configuration

### App Store Connect Setup

**App Information:**
- [ ] App name: "Brainy Bunny"
- [ ] Subtitle: "Educational Games for Kids 2-5" (max 30 chars)
- [ ] Primary category: Education
- [ ] Secondary category: Family/Kids
- [ ] Age rating completed (4+)
- [ ] Content rights verified

**App Privacy:**
- [ ] Privacy Policy URL added
- [ ] Data collection practices declared
- [ ] Third-party tracking disclosed (Adjust, Meta)
- [ ] COPPA compliance confirmed
- [ ] GDPR compliance confirmed

**Subscription Products (RevenueCat):**
- [ ] Weekly subscription created: `brainy_weekly` (€4.99/week)
- [ ] Monthly subscription created: `brainy_monthly` (€8.99/month)
- [ ] Yearly subscription created: `brainy_yearly` (€44.99/year)
- [ ] 7-day free trial configured for all tiers
- [ ] Subscription group created and ranked
- [ ] Localized descriptions for all 6 languages
- [ ] Introductory offer set (7-day free trial)
- [ ] Subscription pricing verified in all regions

**App Description:**
- [ ] English description written (4000 char max)
- [ ] Translated for all 6 Tier 1 languages
- [ ] Keywords optimized (100 char max)
- [ ] Support URL added
- [ ] Marketing URL added (optional)

**App Screenshots:**
- [ ] 6.7" iPhone (1290 x 2796) - 6-10 screenshots
- [ ] 6.5" iPhone (1284 x 2778) - 6-10 screenshots
- [ ] 5.5" iPhone (1242 x 2208) - 6-10 screenshots
- [ ] 12.9" iPad Pro (2048 x 2732) - 6-10 screenshots
- [ ] All screenshots localized for 6 languages
- [ ] App preview video (optional but recommended)

**What's New:**
- [ ] Version 1.0 release notes written
- [ ] Translated for all languages

### Pricing & Availability

- [ ] Pricing tier selected for IAP
- [ ] Countries/regions selected
- [ ] Pre-order (optional)
- [ ] Educational discount (optional)

---

## Backend Services

### Firebase

**Project Setup:**
- [ ] Firebase project created: `brainy_bunny`
- [ ] iOS app registered
- [ ] `GoogleService-Info.plist` added to project
- [ ] Firebase SDK initialized in app

**Firestore:**
- [ ] Database created
- [ ] Security rules deployed
- [ ] Collections structure verified:
  - `users/{userId}`
  - `subscription_events/{eventId}`
- [ ] Indexes created (if needed)

**Cloud Functions:**
- [ ] RevenueCat webhook function deployed
- [ ] Environment variables set:
  - `REVENUECAT_WEBHOOK_AUTH` token
- [ ] Function URL configured in RevenueCat
- [ ] Test webhook sent successfully
- [ ] Error logging configured

**Firebase Authentication:**
- [ ] Anonymous auth enabled
- [ ] Auth state persistence configured

**Firebase Crashlytics:**
- [ ] Crashlytics enabled
- [ ] Test crash sent and received
- [ ] Alert emails configured

**Firebase Analytics:**
- [ ] Analytics enabled
- [ ] Debug view tested
- [ ] Key events defined:
  - `onboarding_started`
  - `onboarding_completed`
  - `subscription_purchased`
  - `trial_started`

### RevenueCat

**Project Setup:**
- [ ] RevenueCat project created
- [ ] iOS app added
- [ ] App Store Connect API key added
- [ ] SDK key copied to app

**Products Configuration:**
- [ ] All 3 subscription products added
- [ ] Entitlements configured: `premium`
- [ ] Offerings created: `default`
- [ ] Packages configured:
  - Weekly
  - Monthly
  - Yearly (default)

**Webhooks:**
- [ ] Webhook URL set (Cloud Functions URL)
- [ ] Authorization header set
- [ ] Test webhook sent successfully
- [ ] All 8 events enabled:
  - INITIAL_PURCHASE
  - RENEWAL
  - CANCELLATION
  - UNCANCELLATION
  - EXPIRATION
  - BILLING_ISSUE
  - PRODUCT_CHANGE
  - TRANSFER

**Testing:**
- [ ] Sandbox mode tested
- [ ] Real purchase tested (small amount)
- [ ] Restore purchases tested

### Adjust

**App Setup:**
- [ ] App created in Adjust dashboard
- [ ] App token copied to app
- [ ] Environment set: `production`

**Events:**
- [ ] Event tokens created for:
  - `app_install`
  - `onboarding_completed`
  - `subscription_purchased`
  - `trial_started`
- [ ] Event tokens added to app code

**SKAdNetwork:**
- [ ] Adjust SKAdNetwork IDs added to `Info.plist`
- [ ] Conversion values configured

**Attribution:**
- [ ] Test install tracked successfully
- [ ] Events appearing in dashboard
- [ ] Revenue tracking verified

### Meta Events Manager (Optional)

**If using Meta ads:**
- [ ] Meta app created
- [ ] Facebook SDK integrated (native)
- [ ] Events configured
- [ ] Test event sent
- [ ] Aggregated Event Measurement configured

---

## Testing & QA

### Manual Testing

**Complete test flows (use TESTING_GUIDE.md):**
- [ ] Test Case 1: First Launch - Complete Flow
- [ ] Test Case 2: Demo Games - Match Completion
- [ ] Test Case 3: Onboarding State Persistence
- [ ] Test Case 4: Onboarding Skip Functionality
- [ ] Test Case 6: Sandbox Subscription Purchase
- [ ] Test Case 7: Restore Purchases
- [ ] Test Case 9: Orientation Transitions
- [ ] Test Case 11: Menu Music Playback
- [ ] Test Case 12: Game Music Transition
- [ ] Test Case 19: Network Errors
- [ ] Test Case 20: App Termination During Onboarding

**Device Testing:**
- [ ] iPhone 8 (minimum spec)
- [ ] iPhone 12/13 (mid-range)
- [ ] iPhone 15 Pro (latest)
- [ ] iPad (landscape/portrait)
- [ ] iPad Pro 12.9" (large screen)

**iOS Versions:**
- [ ] iOS 14 (minimum supported)
- [ ] iOS 15
- [ ] iOS 16
- [ ] iOS 17 (current)
- [ ] iOS 18 beta (if available)

### Automated Testing

**Unit Tests:**
- [ ] Models: `OnboardingState`, `OnboardingProgress`
- [ ] Services: `SubscriptionManager`, `AudioService`
- [ ] All tests passing

**Integration Tests:**
- [ ] Onboarding flow navigation
- [ ] Subscription purchase flow
- [ ] Analytics event tracking

### Performance Testing

**Memory:**
- [ ] No memory leaks detected
- [ ] Memory usage < 250 MB during gameplay
- [ ] Memory drops after exiting games

**Performance:**
- [ ] 60 FPS maintained on iPhone 8
- [ ] Launch time < 3 seconds
- [ ] Demo game load time < 2 seconds
- [ ] No frame drops during animations

**Network:**
- [ ] Works offline (except purchases)
- [ ] Handles poor network gracefully
- [ ] No crashes on network errors

### Edge Cases

- [ ] Low storage (< 500 MB free)
- [ ] Airplane mode during onboarding
- [ ] Force quit and restart at each screen
- [ ] Rapid button tapping (no duplicate actions)
- [ ] Device rotation during transitions
- [ ] Background/foreground during purchase

### Localization Testing

**For each of 6 languages:**
- [ ] All strings translated (no English fallbacks)
- [ ] No text overflow or truncation
- [ ] Currency symbols correct
- [ ] Screenshots match language
- [ ] App Store description proofread

### Accessibility Testing

- [ ] VoiceOver works on all screens
- [ ] Dynamic Type support (text sizing)
- [ ] High contrast mode compatible
- [ ] Color blind friendly (no color-only info)
- [ ] Haptic feedback working

---

## Analytics & Monitoring

### Pre-Launch Verification

**Adjust:**
- [ ] Test install attributed correctly
- [ ] All key events firing
- [ ] Revenue tracking accurate
- [ ] Dashboard access configured

**Firebase Analytics:**
- [ ] DebugView showing events in real-time
- [ ] User properties set
- [ ] Audiences created (trial users, subscribers)

**RevenueCat:**
- [ ] Customer profiles created on purchase
- [ ] Subscription status accurate
- [ ] Charts/metrics displaying correctly
- [ ] Integrations active (Adjust, Slack)

**Firebase Crashlytics:**
- [ ] Test crash sent and received
- [ ] Crash-free users metric visible
- [ ] Alert emails configured
- [ ] Slack/Discord webhook (optional)

### Dashboards Setup

- [ ] Adjust dashboard bookmarked
- [ ] Firebase console bookmarked
- [ ] RevenueCat dashboard bookmarked
- [ ] App Store Connect bookmarked

### Alerts Configuration

**Critical Alerts:**
- [ ] Crash rate > 1% → Email + Slack
- [ ] Subscription webhook failure → Email immediately
- [ ] Cloud Functions error rate > 5% → Email

**Important Alerts:**
- [ ] Trial conversion < 30% → Email daily
- [ ] Subscription cancellation spike → Email
- [ ] Network error rate > 10% → Email

---

## Legal & Compliance

### Privacy Policy

- [ ] Privacy policy written
- [ ] Covers all data collection:
  - Anonymous user ID (Firebase Auth)
  - Purchase history (RevenueCat)
  - Analytics events (Adjust, Firebase)
  - Device info (iOS version, model)
  - Attribution data (IDFA)
- [ ] GDPR compliant (EU users)
- [ ] COPPA compliant (children under 13)
- [ ] Hosted publicly (website URL in App Store)
- [ ] Last updated date current

### Terms of Service

- [ ] Terms of service written
- [ ] Covers:
  - Subscription terms
  - Auto-renewal disclosure
  - Cancellation policy
  - Refund policy
  - Content ownership
  - Prohibited uses
- [ ] Hosted publicly (website URL in App Store)

### COPPA Compliance

Children's app requirements:
- [ ] No personal data collected from children
- [ ] Parental gate for purchases ✅ (Apple handles)
- [ ] No behavioral advertising
- [ ] No third-party analytics tracking children
- [ ] Age-appropriate content ✅
- [ ] Privacy policy includes COPPA disclosure

### GDPR Compliance

EU user requirements:
- [ ] Data collection disclosed clearly
- [ ] User can export their data (support email)
- [ ] User can delete their data (support email)
- [ ] Cookie consent not required (native app)
- [ ] Data processing agreement with third parties

### App Store Guidelines

**Verify compliance with:**
- [ ] 2.3.8: Accurate metadata
- [ ] 2.3.10: Accurate screenshots
- [ ] 3.1.1: In-App Purchase (not third-party)
- [ ] 3.1.2: Subscriptions properly disclosed
- [ ] 3.1.3: No misleading trial offers
- [ ] 5.1.1: Privacy policy required
- [ ] 5.1.2: Data use disclosure

### Subscription Disclosure

**Required by Apple:**
- [ ] Trial duration clearly stated (7 days)
- [ ] Post-trial price clearly stated
- [ ] Auto-renewal clearly disclosed
- [ ] Cancellation instructions provided
- [ ] "Restore Purchases" button available ✅

---

## Marketing Assets

### App Icon

- [ ] 1024x1024 App Store icon
- [ ] No transparency
- [ ] No rounded corners (Apple adds)
- [ ] Visually distinct
- [ ] Tested on different backgrounds

### Screenshots

**iPhone:**
- [ ] 6.7" (1290 x 2796) - 6 screenshots minimum
  1. Home screen with games
  2. Onboarding welcome
  3. Demo game in action
  4. Badges collected
  5. Subscription benefits
  6. Social proof
- [ ] 6.5" (1284 x 2778) - same 6
- [ ] 5.5" (1242 x 2208) - same 6

**iPad:**
- [ ] 12.9" (2048 x 2732) - 6 screenshots
  1. Home screen landscape
  2. Multiple games visible
  3. Large screen gameplay
  4. Parent dashboard
  5. Settings
  6. Premium content

**Localized:**
- [ ] Spanish screenshots (es, es_MX)
- [ ] French screenshots (fr)
- [ ] German screenshots (de)
- [ ] Portuguese screenshots (pt_BR)
- [ ] Italian screenshots (it)

### App Preview Video (Optional)

- [ ] 15-30 second video
- [ ] Shows onboarding → gameplay → benefits
- [ ] Captions in English
- [ ] Localized versions for each language
- [ ] Proper aspect ratios for each device

### Press Kit

- [ ] App icon (various sizes)
- [ ] Screenshots (high res)
- [ ] App description (short & long)
- [ ] Feature list
- [ ] Founder photo & bio
- [ ] Company logo
- [ ] Press release draft

---

## Submission

### Pre-Submission

- [ ] All above checklists complete
- [ ] Final build created in Xcode
- [ ] Archive uploaded to App Store Connect
- [ ] Build processing complete (wait 30-60 min)
- [ ] TestFlight beta testing complete (optional)

### App Store Connect Submission

**Version Information:**
- [ ] What's New text entered (all languages)
- [ ] Build selected
- [ ] Screenshots uploaded (all languages)
- [ ] App preview video uploaded (if using)

**App Review Information:**
- [ ] Contact information (email, phone)
- [ ] Demo account (if app requires login)
- [ ] Notes for reviewer:
  - How to test subscription (sandbox)
  - Special setup instructions
  - Testing guidance

**Release Options:**
- [ ] Choose release type:
  - [ ] Automatic release after approval
  - [ ] Manual release (choose date)
  - [ ] Phased release (7 days)

**Submission:**
- [ ] Click "Submit for Review"
- [ ] Confirmation email received

### Post-Submission

**While in Review:**
- [ ] Monitor App Store Connect status
- [ ] Respond to review questions within 24 hours
- [ ] Test crash reports (if any)
- [ ] Prepare launch announcement

**If Rejected:**
- [ ] Read rejection reason carefully
- [ ] Fix issues
- [ ] Respond to reviewer or resubmit
- [ ] Common rejections:
  - Misleading screenshots
  - Missing subscription disclosure
  - Privacy policy issues
  - Crash on launch

---

## Post-Launch

### Day 1 (Launch Day)

**Immediately After Approval:**
- [ ] Release app (if manual release)
- [ ] Verify app appears in App Store
- [ ] Test download and install
- [ ] Announce launch:
  - [ ] Social media (Twitter, LinkedIn, Instagram)
  - [ ] Email list (if exists)
  - [ ] Product Hunt submission
  - [ ] Press outreach

**Monitoring:**
- [ ] Watch crash rate in Crashlytics
- [ ] Monitor analytics dashboards
- [ ] Check for customer support requests
- [ ] Monitor App Store ratings/reviews

### Week 1

**Daily Checks:**
- [ ] Crash-free users % (target: >99.5%)
- [ ] DAU (Daily Active Users)
- [ ] Onboarding completion rate (target: >80%)
- [ ] Trial start rate (target: >30%)
- [ ] Subscription conversion (target: >40% trial → paid)

**Analytics Review:**
- [ ] Which onboarding screen has highest drop-off?
- [ ] Which demo games most engaging?
- [ ] Which subscription tier most popular?
- [ ] Average session duration
- [ ] Retention: Day 1, Day 3, Day 7

**User Feedback:**
- [ ] Read all App Store reviews
- [ ] Respond to reviews (especially negative)
- [ ] Collect feedback via support email
- [ ] Identify top 3 issues

### Week 2-4

**Optimization:**
- [ ] A/B test paywall variations (if needed)
- [ ] Optimize ASO (keywords, description)
- [ ] Fix critical bugs (if any)
- [ ] Plan first update (v1.1)

**Marketing:**
- [ ] Apple Search Ads campaign (if budget)
- [ ] Meta ads campaign (if budget)
- [ ] Content marketing (blog posts)
- [ ] Influencer outreach (parenting bloggers)

### Month 2-3

**Feature Development:**
- [ ] Plan v1.1 features based on feedback
- [ ] Add Tier 2 languages (Chinese, Japanese, Russian)
- [ ] Implement user-requested features
- [ ] Improve onboarding based on data

**Growth:**
- [ ] Expand to Android (if successful on iOS)
- [ ] Partner with schools/educators
- [ ] Referral program
- [ ] Family plan subscription tier

---

## Key Metrics to Track

### User Acquisition

| Metric | Target | Critical |
|--------|--------|----------|
| Daily installs | 50+ | >10 |
| Install → Open | >90% | >70% |
| Cost per install (CPI) | <€2 | <€5 |

### Onboarding

| Metric | Target | Critical |
|--------|--------|----------|
| Onboarding start % | >95% | >80% |
| Completion rate | >85% | >70% |
| Time to complete | 8-12 min | <20 min |
| Drop-off screen | Screen 13-15 | Screen 1-5 |

### Monetization

| Metric | Target | Critical |
|--------|--------|----------|
| Trial start rate | 35% | >20% |
| Trial → Paid | 70% | >40% |
| Overall conversion | 25% | >10% |
| MRR growth | +20%/month | >0% |
| Churn rate | <5%/month | <15% |

### Engagement

| Metric | Target | Critical |
|--------|--------|----------|
| DAU/MAU ratio | >30% | >15% |
| Session duration | 15+ min | >5 min |
| Sessions per user | 3+/week | 1+/week |
| Games per session | 3+ | 1+ |

### Technical

| Metric | Target | Critical |
|--------|--------|----------|
| Crash-free users | >99.5% | >98% |
| App launch time | <2 sec | <5 sec |
| Network errors | <1% | <5% |
| App Store rating | >4.5 ⭐ | >4.0 ⭐ |

---

## Emergency Contacts

### Critical Issues

**Crash rate >5%:**
1. Check Crashlytics for top crash
2. Release hotfix build immediately
3. Expedited review request

**Subscription webhook down:**
1. Check Cloud Functions logs
2. Verify RevenueCat status page
3. Manual verification if needed

**App Store rejection:**
1. Read rejection carefully
2. Fix within 24 hours
3. Respond to reviewer

### Support Contacts

- **Developer:** [Your name/email]
- **Firebase Support:** firebase.google.com/support
- **RevenueCat Support:** <EMAIL>
- **Adjust Support:** <EMAIL>
- **Apple Developer Support:** developer.apple.com/contact

---

## Sign-Off

**Development Lead:** __________ Date: ______

**QA Lead:** __________ Date: ______

**Product Owner:** __________ Date: ______

**Final Approval:** __________ Date: ______

---

**🚀 Ready to launch when all checkboxes complete!**

---

**End of Launch Checklist**

{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98db75f4a219a745e087b4f7af1bfb729a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9809ab00a945b63d1a8d86134c390d52d3", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982ce98b8f19a9c589999ef29ee3f27089", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987f795a8b71c4addb0fe5c83ef8177e9f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982ce98b8f19a9c589999ef29ee3f27089", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9800940e177cbb349a552840c880775ec1", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985861d49eb5c79c3cdcc214e6c21347ea", "guid": "bfdfe7dc352907fc980b868725387e98a57c0f86598c785db8170754d6d503b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2b6b8ad7fec2e5b8c1d9788803a0c62", "guid": "bfdfe7dc352907fc980b868725387e98bf48778075be950da008da1511795e02", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832332a0ee7ee1244308d224fc6991e98", "guid": "bfdfe7dc352907fc980b868725387e981d252589ce30b8f4ec7a2f30a7e74de1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fce0ce364675e48b4e2a5dabebcad969", "guid": "bfdfe7dc352907fc980b868725387e988b1fe20934993d90321d02bf8eaf80e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e9adb9ffc06b585c5d82f52637e5d3e", "guid": "bfdfe7dc352907fc980b868725387e98733925956d709f73f9dba5457f09d670"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba2bb143e643523041951db270df8a6f", "guid": "bfdfe7dc352907fc980b868725387e9880da50f1cdb028c00b9bed9950a6a7d8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6b57d2ccdd69e0d3762b9271f76d95a", "guid": "bfdfe7dc352907fc980b868725387e98413709d939dc86b7ec2887d668badc3a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98105a0d3b9fb718dd9589454e09765745", "guid": "bfdfe7dc352907fc980b868725387e98ec38863241c222e871faa158998282ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8ff4bc94e15dc46a4323fc53068d4f4", "guid": "bfdfe7dc352907fc980b868725387e98ff1ede2ddd78a1d3c1d272707dd31443", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d00756c2869ec136888852de328bd57", "guid": "bfdfe7dc352907fc980b868725387e98d6abf8f63d66b550a89bac64d04afc92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984504a20a35a399adc4345266c9bbee88", "guid": "bfdfe7dc352907fc980b868725387e98004b11f2929981edaab0232bcb431921", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed53318a7cf8302085a95b9ad51b139e", "guid": "bfdfe7dc352907fc980b868725387e9837037ae9818672b8a03f6d197012203b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838174a6bff791ee1bca806bbf0bbf782", "guid": "bfdfe7dc352907fc980b868725387e98b8440a2725f28d8f8cae00d68c356d4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803aa52d094eab708ff90a8d2cbb60658", "guid": "bfdfe7dc352907fc980b868725387e984062b93182f52d5997d1da61a4cf06f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822c351f56ea4789a8fb811a8039c9a0b", "guid": "bfdfe7dc352907fc980b868725387e98cdb7fbe7e5a457780ecc58111838ad0f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f57cc1eb0de13de66823bfaee64d12a", "guid": "bfdfe7dc352907fc980b868725387e98e1624455a2b7f23959b2b327b9303358"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f83bc74af5bffb6b340906b02fe276f", "guid": "bfdfe7dc352907fc980b868725387e98fe54ad33168ed8d10624c1ec661fc035"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e49e272cd027815067cee7389901a4c", "guid": "bfdfe7dc352907fc980b868725387e98e5a3d73242309dad557332b9b9429a43", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ab6be6b2a7a1ea2b931530f6dee4c28", "guid": "bfdfe7dc352907fc980b868725387e988fb07d2dcae5ecef17896ee20622091f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee8ae44fa5d34baa1b66794dbfd38c39", "guid": "bfdfe7dc352907fc980b868725387e984c5f8f40867093864ee70a47a9a1acf2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989db0677bef107f87bfbbb3dbc503ec9f", "guid": "bfdfe7dc352907fc980b868725387e98d954a327df9cbf41c17a1c75e188d82a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883014f0185c9e5c2106ff1a7ab194b84", "guid": "bfdfe7dc352907fc980b868725387e98f0e73df28348d20e579e241dd99222c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c09f18945bdf35fbb651a97c1f298cf1", "guid": "bfdfe7dc352907fc980b868725387e9855c5d40448e18326d7330fb4a8129e45", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e985cde8822fb5f4bd5b31e428846ed8e1b", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988e5feb919dc2f5e7e5092da33fcad758", "guid": "bfdfe7dc352907fc980b868725387e9874c72093a6ae1e932c3c92af09c2c7ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f53829a58bce0fd6a35393f9253477ba", "guid": "bfdfe7dc352907fc980b868725387e986ce564568d681529e76065a831579516"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2fea9611035b7a1d6895db169819690", "guid": "bfdfe7dc352907fc980b868725387e98149dca969bbb65a460b4623f8d98e45b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e19097f020fa01e76a87d0ba88b7306", "guid": "bfdfe7dc352907fc980b868725387e987bd24787ca0cf031726bc27c59ab3019"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98583fd1e5249ca3965b2fd9a5ea34f5fb", "guid": "bfdfe7dc352907fc980b868725387e989ee5a8f7c1651cdd5976e35f43ff32f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865bd634e310c3b2cc927951c67f7138d", "guid": "bfdfe7dc352907fc980b868725387e9829442a249336ea4d339adf19ce1d04dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824ede37de59c477334eb91cbf0d9770d", "guid": "bfdfe7dc352907fc980b868725387e986f12a06338ae6b69165f63648625328e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcd346617f199ce999d451fd6b9e6943", "guid": "bfdfe7dc352907fc980b868725387e989a115869eff1cb112555a88c51ce122f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98240f247d393d6af5eea3374fb2384031", "guid": "bfdfe7dc352907fc980b868725387e982d166ff2f7e99fc9124fc1180460c251"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9832c18fa18f284be2bac026a6d6338c16", "guid": "bfdfe7dc352907fc980b868725387e9874cfb80f4def9d0c34b0e8ae7e1d9d75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ead8bcf67f55a7c0adf2d1bad051bd69", "guid": "bfdfe7dc352907fc980b868725387e98d16c7a1c4512783362190643cc0779fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98231586a49f9b2178088cad5d20e523fc", "guid": "bfdfe7dc352907fc980b868725387e98df297a215bd5b7177a9639083c1600f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa2c04ccbeee50cb2ce11e8305bf7ee8", "guid": "bfdfe7dc352907fc980b868725387e98c074394c2168d4d940cffea20972e34f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b4c68608296472088cc80b82738cd5c", "guid": "bfdfe7dc352907fc980b868725387e9825dbe8eacd49ea3efd1b51284be93c61"}], "guid": "bfdfe7dc352907fc980b868725387e9815ae91032e5a323166806ebdd6bc9d0c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986d33a47f1e1031e7e6a671aab8d386e4", "guid": "bfdfe7dc352907fc980b868725387e9831787cf821eaba9b810b503a9efe4222"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3fca7813f8514b6c61a2edbce16745f", "guid": "bfdfe7dc352907fc980b868725387e98c677e29bf6bfcceafed9c58fd3226042"}], "guid": "bfdfe7dc352907fc980b868725387e9872cdb8ea96448ad5dbf5b14a60df4fd0", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e983787682b31c7f28e9026e40e1dace1b3", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e98822f2593d4913498e46006386e396ccf", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
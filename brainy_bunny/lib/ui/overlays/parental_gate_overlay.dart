// lib/ui/overlays/parental_gate_overlay.dart
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flame/game.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:brainy_bunny/constants/app_constants.dart';
import 'package:brainy_bunny/services/purchase_manager.dart';

/// Parental gate overlay for in-game purchases (landscape)
/// Math problem verification before allowing purchase
class ParentalGateOverlay extends StatefulWidget {
  final GameWidget gameWidget;

  const ParentalGateOverlay({
    super.key,
    required this.gameWidget,
  });

  @override
  State<ParentalGateOverlay> createState() => _ParentalGateOverlayState();
}

class _ParentalGateOverlayState extends State<ParentalGateOverlay>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  final TextEditingController _answerController = TextEditingController();
  late int _num1;
  late int _num2;
  late int _correctAnswer;
  bool _showError = false;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _generateMathProblem();
    _animationController.forward();
  }

  void _generateMathProblem() {
    final random = Random();
    _num1 = 10 + random.nextInt(20);
    _num2 = 10 + random.nextInt(20);
    _correctAnswer = _num1 + _num2;
    if (kDebugMode) {
      print('Generated math problem: $_num1 + $_num2 = $_correctAnswer');
    }
  }

  void _checkAnswer() async {
    final userAnswer = int.tryParse(_answerController.text);

    if (userAnswer == _correctAnswer) {
      if (kDebugMode) {
        print('✅ Age verification passed');
      }
      _closeOverlay();

      // Age verified, now try to initialize purchase
      await _attemptPurchase();
    } else {
      setState(() {
        _showError = true;
      });

      // Reset after showing error
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) {
          setState(() {
            _showError = false;
            _answerController.clear();
            _generateMathProblem();
          });
        }
      });
    }
  }

  Future<void> _attemptPurchase() async {
    try {
      final purchaseManager = PurchaseManager();

      // Try to initialize purchase manager
      if (!purchaseManager.isInitialized) {
        await purchaseManager.initialize();
      }

      // Check if we successfully initialized and can proceed
      if (purchaseManager.isInitialized) {
        if (kDebugMode) {
          print('✅ Purchase manager initialized, proceeding with subscription');
        }
        // TODO: Trigger actual purchase flow here
        // For now, just close
      } else {
        // Initialization failed, show error
        _showPurchaseError();
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error initializing purchase: $e');
      }
      _showPurchaseError();
    }
  }

  void _showPurchaseError() {
    final game = widget.gameWidget.game;
    if (game != null) {
      game.overlays.add('purchaseError');
    }
  }

  void _closeOverlay() {
    final game = widget.gameWidget.game;
    if (game != null && game.overlays.isActive(AppConstants.AGE_VERIFICATION_OVERLAY)) {
      game.overlays.remove(AppConstants.AGE_VERIFICATION_OVERLAY);
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _answerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.black.withValues(alpha: 0.8),
      child: Center(
        child: ScaleTransition(
          scale: _scaleAnimation,
          child: Container(
            width: MediaQuery.of(context).size.width * 0.6,
            padding: const EdgeInsets.all(32),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(24),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Title
                const Icon(
                  Icons.shield_outlined,
                  size: 48,
                  color: Color(0xFF4A90E2),
                ),
                const SizedBox(height: 16),
                Text(
                  AppLocalizations.of(context)!.parental_gate_overlay_title,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1E3A5F),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 12),
                Text(
                  AppLocalizations.of(context)!.parental_gate_overlay_instruction,
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey.shade700,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 32),

                // Math problem
                Container(
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: _showError ? Colors.red.shade300 : Colors.blue.shade200,
                      width: 2,
                    ),
                  ),
                  child: Column(
                    children: [
                      Text(
                        '$_num1 + $_num2 = ?',
                        style: const TextStyle(
                          fontSize: 36,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF1E3A5F),
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Answer input
                      SizedBox(
                        width: 200,
                        child: TextField(
                          controller: _answerController,
                          keyboardType: TextInputType.number,
                          textAlign: TextAlign.center,
                          style: const TextStyle(
                            fontSize: 28,
                            fontWeight: FontWeight.bold,
                          ),
                          decoration: InputDecoration(
                            hintText: 'Your answer',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: _showError ? Colors.red : Colors.blue.shade300,
                                width: 2,
                              ),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: _showError ? Colors.red : Colors.blue.shade300,
                                width: 2,
                              ),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(
                                color: _showError ? Colors.red : Colors.blue.shade600,
                                width: 2,
                              ),
                            ),
                            filled: true,
                            fillColor: Colors.white,
                          ),
                          onSubmitted: (_) => _checkAnswer(),
                        ),
                      ),

                      if (_showError) ...[
                        const SizedBox(height: 12),
                        Text(
                          'Incorrect answer. Please try again.',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.red.shade700,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),

                const SizedBox(height: 32),

                // Buttons
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: _closeOverlay,
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          side: BorderSide(color: Colors.grey.shade400, width: 2),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: Text(
                          'Cancel',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Colors.grey.shade700,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _checkAnswer,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF4A90E2),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          elevation: 2,
                        ),
                        child: const Text(
                          'Verify',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

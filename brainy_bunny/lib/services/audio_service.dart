import 'dart:math';
import 'package:flame_audio/flame_audio.dart';
import 'package:flutter/foundation.dart';
import 'package:brainy_bunny/constants/app_constants.dart';

/// Service to handle audio playback with separate menu and game music
class AudioService {
  // Singleton pattern
  static final AudioService _instance = AudioService._internal();

  static AudioService get instance => _instance;

  bool _isInitialized = false;
  bool _isMusicPlaying = false;
  bool _isMusicPaused = false;
  double _musicVolume = 0.5;
  String? _currentMusicFile;
  String? _currentMusicType; // 'menu' or 'game'

  // Random number generator for game music selection
  final Random _random = Random();

  // Private constructor for singleton
  AudioService._internal();

  /// Initialize the audio service by preloading all audio files
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await FlameAudio.audioCache.loadAll(AppConstants.AUDIO_FILES);
      _isInitialized = true;

      if (kDebugMode) {
        print('Audio files loaded successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading audio files: $e');
      }
    }
  }

  /// Play a sound effect once
  void playSound(String soundName) {
    if (!_isInitialized) return;

    try {
      FlameAudio.play(soundName);
    } catch (e) {
      if (kDebugMode) {
        print('Error playing sound $soundName: $e');
      }
    }
  }

  /// Play menu background music
  void playMenuMusic() {
    if (!_isInitialized) return;

    // Only change music if we're not already playing menu music
    if (_isMusicPlaying && _currentMusicType == 'menu' && !_isMusicPaused) {
      if (kDebugMode) {
        print('Menu music already playing and not paused');
      }
      return;
    }

    try {
      // Stop current music if playing (especially important for game music)
      if (_isMusicPlaying) {
        FlameAudio.bgm.stop();
        if (kDebugMode) {
          print('Stopped previous music: $_currentMusicFile');
        }
      }

      // Add small delay to ensure clean stop, then start menu music
      Future.delayed(const Duration(milliseconds: 100), () {
        try {
          FlameAudio.bgm.play('Menu.mp3', volume: _musicVolume);
          _isMusicPlaying = true;
          _isMusicPaused = false;
          _currentMusicFile = 'Menu.mp3';
          _currentMusicType = 'menu';

          if (kDebugMode) {
            print('✅ Started playing menu music: Menu.mp3');
          }
        } catch (e) {
          if (kDebugMode) {
            print('❌ Error in delayed menu music start: $e');
          }
        }
      });
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error playing menu music: $e');
      }
    }
  }

  /// Play random game background music
  void playGameMusic() {
    if (!_isInitialized) return;

    try {
      // Stop current music if playing (important for switching from menu music)
      if (_isMusicPlaying) {
        FlameAudio.bgm.stop();
        if (kDebugMode) {
          print('Stopped previous music: $_currentMusicFile');
        }
      }

      // Select random game music (1-3)
      final musicIndex = _random.nextInt(3) + 1;
      final musicFileName = 'Background_$musicIndex.mp3';

      FlameAudio.bgm.play(musicFileName, volume: _musicVolume);
      _isMusicPlaying = true;
      _isMusicPaused = false;
      _currentMusicFile = musicFileName;
      _currentMusicType = 'game';

      if (kDebugMode) {
        print('Started playing random game music: $musicFileName');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error playing game music: $e');
      }
    }
  }

  /// Play background music (legacy method - now defaults to menu music)
  void playMusic(String musicName) {
    if (!_isInitialized || _isMusicPlaying) return;

    try {
      FlameAudio.bgm.play(musicName, volume: _musicVolume);
      _isMusicPlaying = true;
      _isMusicPaused = false;
      _currentMusicFile = musicName;
      _currentMusicType = 'custom';

      if (kDebugMode) {
        print('Started playing custom music: $musicName');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error playing music $musicName: $e');
      }
    }
  }

  /// Stop background music
  void stopMusic() {
    if (!_isInitialized) return;

    try {
      FlameAudio.bgm.stop();
      _isMusicPlaying = false;
      _isMusicPaused = false;
      _currentMusicFile = null;
      _currentMusicType = null;

      if (kDebugMode) {
        print('Music stopped');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error stopping music: $e');
      }
    }
  }

  /// Pause background music
  void pauseMusic() {
    if (!_isInitialized || !_isMusicPlaying || _isMusicPaused) return;

    try {
      FlameAudio.bgm.pause();
      _isMusicPaused = true;

      if (kDebugMode) {
        print('Music paused');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error pausing music: $e');
      }
    }
  }

  /// Resume background music
  void resumeMusic() {
    if (!_isInitialized || !_isMusicPlaying || !_isMusicPaused) return;

    try {
      FlameAudio.bgm.resume();
      _isMusicPaused = false;

      if (kDebugMode) {
        print('Music resumed: $_currentMusicFile');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error resuming music: $e');
      }
    }
  }

  /// Resume music with specific type (for app lifecycle management)
  void resumeMusicOfType(String type) {
    if (!_isInitialized) return;

    if (kDebugMode) {
      print('Attempting to resume $type music. Current: $_currentMusicType, Paused: $_isMusicPaused');
    }

    // If we have paused music of the requested type, resume it
    if (_isMusicPaused && _currentMusicType == type) {
      resumeMusic();
    } else if (!_isMusicPlaying || _currentMusicType != type) {
      // If no music is playing or wrong type, start the appropriate music
      if (type == 'menu') {
        playMenuMusic();
      } else if (type == 'game') {
        playGameMusic();
      }
    }
  }

  /// Set music volume (0.0 to 1.0)
  void setMusicVolume(double volume) {
    if (!_isInitialized) return;

    try {
      _musicVolume = volume.clamp(0.0, 1.0);
      FlameAudio.bgm.audioPlayer.setVolume(_musicVolume);
    } catch (e) {
      if (kDebugMode) {
        print('Error setting music volume: $e');
      }
    }
  }

  /// Check if music is currently playing (and not paused)
  bool get isMusicPlaying => _isMusicPlaying && !_isMusicPaused;

  /// Check if music is paused
  bool get isMusicPaused => _isMusicPaused;

  /// Get current music volume
  double get musicVolume => _musicVolume;

  /// Get current music file
  String? get currentMusicFile => _currentMusicFile;

  /// Get current music type
  String? get currentMusicType => _currentMusicType;

  /// Check if currently playing menu music
  bool get isPlayingMenuMusic => _currentMusicType == 'menu';

  /// Force play menu music (ignores current state)
  void forcePlayMenuMusic() {
    if (!_isInitialized) return;

    if (kDebugMode) {
      print('🔄 Force playing menu music (ignoring current state)');
    }

    try {
      // Always stop current music first
      if (_isMusicPlaying) {
        FlameAudio.bgm.stop();
        if (kDebugMode) {
          print('Force stopped previous music: $_currentMusicFile');
        }
      }

      // Reset state
      _isMusicPlaying = false;
      _isMusicPaused = false;
      _currentMusicFile = null;
      _currentMusicType = null;

      // Wait a moment for clean stop
      Future.delayed(const Duration(milliseconds: 150), () {
        try {
          FlameAudio.bgm.play('Menu.mp3', volume: _musicVolume);
          _isMusicPlaying = true;
          _isMusicPaused = false;
          _currentMusicFile = 'Menu.mp3';
          _currentMusicType = 'menu';

          if (kDebugMode) {
            print('✅ Force started menu music: Menu.mp3');
          }
        } catch (e) {
          if (kDebugMode) {
            print('❌ Error in force play menu music: $e');
          }
        }
      });
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error force playing menu music: $e');
      }
    }
  }

  /// Dispose all audio resources to prevent memory leaks
  void dispose() {
    try {
      if (kDebugMode) {
        print('🧹 Disposing audio service resources');
      }

      // Stop all music
      if (_isMusicPlaying) {
        FlameAudio.bgm.stop();
      }

      // Clear audio cache to free memory
      FlameAudio.audioCache.clearAll();

      // Reset state
      _isMusicPlaying = false;
      _isMusicPaused = false;
      _currentMusicFile = null;
      _currentMusicType = null;
      _isInitialized = false;

      if (kDebugMode) {
        print('✅ Audio service disposed successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error disposing audio service: $e');
      }
    }
  }
}
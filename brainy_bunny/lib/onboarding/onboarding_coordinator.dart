// lib/onboarding/onboarding_coordinator.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:brainy_bunny/models/onboarding_state.dart';
import 'package:brainy_bunny/models/onboarding_progress.dart';
import 'package:brainy_bunny/constants/onboarding_constants.dart';
import 'package:brainy_bunny/services/analytics/analytics_manager.dart';

/// Enum for onboarding screen types
enum OnboardingScreenType {
  welcome,
  nameInput,
  childAge,
  educationalPhilosophy,
  orientationTransitionToGames,
  demoGame1,
  demoGame2,
  demoGame3,
  demoGame4,
  demoGame5,
  orientationTransitionFromGames,
  personalizedSummary,
  trustProof,
  trialExplanation,
  unifiedPaywall,
  // Deprecated - keeping for migration
  paywallStep1,
  paywallStep2,
  paywallStep3,
}

/// Coordinator for managing onboarding flow navigation and state
class OnboardingCoordinator {
  final BuildContext context;
  final OnboardingState state;
  final OnboardingProgress progress;
  final VoidCallback onComplete;

  OnboardingCoordinator({
    required this.context,
    required this.state,
    required this.progress,
    required this.onComplete,
  });

  /// Get the total number of screens
  int get totalScreens => OnboardingScreenType.values.length;

  /// Get current screen type
  OnboardingScreenType get currentScreenType {
    if (progress.currentScreenIndex >= OnboardingScreenType.values.length) {
      return OnboardingScreenType.paywallStep3;
    }
    return OnboardingScreenType.values[progress.currentScreenIndex];
  }

  /// Check if current screen is portrait
  bool get isCurrentScreenPortrait {
    final screen = currentScreenType;
    return screen != OnboardingScreenType.demoGame1 &&
           screen != OnboardingScreenType.demoGame2 &&
           screen != OnboardingScreenType.demoGame3 &&
           screen != OnboardingScreenType.demoGame4 &&
           screen != OnboardingScreenType.demoGame5;
  }

  /// Initialize coordinator
  Future<void> initialize() async {
    // Track onboarding started if first time
    if (progress.currentScreenIndex == 0 && !progress.isCompleted) {
      await AnalyticsManager.instance.trackOnboardingStarted();
      state.startedAt = DateTime.now();
      await state.save();
    }

    // Set initial orientation
    await _setOrientation(isCurrentScreenPortrait);
  }

  /// Navigate to next screen
  Future<void> goToNextScreen() async {
    // Mark current screen as completed
    progress.completeScreen(progress.currentScreenIndex);
    await progress.save();

    // Track screen completion
    await AnalyticsManager.instance.trackOnboardingScreenView(
      currentScreenType.toString(),
    );

    // Move to next screen
    progress.currentScreenIndex++;
    await progress.save();

    // Check if orientation needs to change
    final needsOrientationChange = _needsOrientationChange();
    if (needsOrientationChange) {
      await _setOrientation(isCurrentScreenPortrait);
    }

    // Check if onboarding is complete
    if (progress.currentScreenIndex >= totalScreens) {
      await _completeOnboarding();
    }
  }

  /// Navigate to previous screen
  Future<void> goToPreviousScreen() async {
    if (progress.currentScreenIndex > 0) {
      progress.currentScreenIndex--;
      await progress.save();

      // Check if orientation needs to change
      final needsOrientationChange = _needsOrientationChange();
      if (needsOrientationChange) {
        await _setOrientation(isCurrentScreenPortrait);
      }
    }
  }

  /// Navigate to specific screen
  Future<void> goToScreen(OnboardingScreenType screenType) async {
    final targetIndex = OnboardingScreenType.values.indexOf(screenType);

    if (targetIndex >= 0 && targetIndex < totalScreens) {
      progress.currentScreenIndex = targetIndex;
      await progress.save();

      // Check if orientation needs to change
      final needsOrientationChange = _needsOrientationChange();
      if (needsOrientationChange) {
        await _setOrientation(isCurrentScreenPortrait);
      }
    }
  }

  /// Mark demo game as completed
  Future<void> completeDemoGame(int gameIndex, String badgeName) async {
    progress.completeDemoGame(gameIndex);
    progress.addBadge(badgeName);
    await progress.save();

    // Track demo game completion
    await AnalyticsManager.instance.trackDemoGamePlayed(gameIndex: gameIndex);
  }

  /// Complete onboarding
  Future<void> _completeOnboarding() async {
    progress.complete();
    state.completedAt = DateTime.now();

    await progress.save();
    await state.save();

    // Track onboarding completion
    await AnalyticsManager.instance.trackOnboardingCompleted();

    // Call completion callback
    onComplete();
  }

  /// Set device orientation
  Future<void> _setOrientation(bool portrait) async {
    try {
      if (portrait) {
        await SystemChrome.setPreferredOrientations([
          DeviceOrientation.portraitUp,
        ]);
      } else {
        await SystemChrome.setPreferredOrientations([
          DeviceOrientation.landscapeLeft,
          DeviceOrientation.landscapeRight,
        ]);
      }

      // Small delay to allow orientation to settle
      await Future.delayed(const Duration(milliseconds: 300));
    } catch (e) {
      print('❌ Failed to set orientation: $e');
    }
  }

  /// Check if orientation change is needed between screens
  bool _needsOrientationChange() {
    if (progress.currentScreenIndex == 0) return false;

    final currentScreen = currentScreenType;
    final previousScreen = OnboardingScreenType.values[progress.currentScreenIndex - 1];

    // Portrait screens: welcome, nameInput, childAge, educationalPhilosophy, orientationTransition
    // Landscape screens: demoGame1-5
    final currentIsPortrait = currentScreen != OnboardingScreenType.demoGame1 &&
                              currentScreen != OnboardingScreenType.demoGame2 &&
                              currentScreen != OnboardingScreenType.demoGame3 &&
                              currentScreen != OnboardingScreenType.demoGame4 &&
                              currentScreen != OnboardingScreenType.demoGame5;

    final previousIsPortrait = previousScreen != OnboardingScreenType.demoGame1 &&
                               previousScreen != OnboardingScreenType.demoGame2 &&
                               previousScreen != OnboardingScreenType.demoGame3 &&
                               previousScreen != OnboardingScreenType.demoGame4 &&
                               previousScreen != OnboardingScreenType.demoGame5;

    return currentIsPortrait != previousIsPortrait;
  }

  /// Reset onboarding (for testing)
  Future<void> reset() async {
    await OnboardingState.clear();
    await OnboardingProgress.clear();
    progress.currentScreenIndex = 0;
    progress.completedScreens.clear();
    progress.completedDemoGames.clear();
    progress.earnedBadges.clear();
    progress.isCompleted = false;
  }

  /// Skip to paywall (admin/testing feature)
  Future<void> skipToPaywall() async {
    await goToScreen(OnboardingScreenType.paywallStep1);
  }

  /// Get progress percentage
  double getProgressPercentage() {
    return progress.getCompletionPercentage(totalScreens: totalScreens);
  }

  /// Get earned badges
  List<String> getEarnedBadges() {
    return List.from(progress.earnedBadges);
  }

  /// Check if can proceed to next screen
  bool canProceed() {
    switch (currentScreenType) {
      case OnboardingScreenType.nameInput:
        return state.isNameValid(state.parentName);

      case OnboardingScreenType.childAge:
        return state.childAge != null;

      case OnboardingScreenType.demoGame1:
      case OnboardingScreenType.demoGame2:
      case OnboardingScreenType.demoGame3:
      case OnboardingScreenType.demoGame4:
      case OnboardingScreenType.demoGame5:
        final gameIndex = progress.currentScreenIndex - OnboardingScreenType.demoGame1.index;
        return progress.isDemoGameCompleted(gameIndex);

      default:
        return true;
    }
  }

  /// Dispose and reset orientation
  Future<void> dispose() async {
    // Reset to landscape (default app orientation)
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
  }
}

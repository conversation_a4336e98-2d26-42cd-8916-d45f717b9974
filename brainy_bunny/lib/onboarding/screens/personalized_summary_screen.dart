// lib/onboarding/screens/personalized_summary_screen.dart
import 'package:flutter/material.dart';
import 'package:brainy_bunny/models/onboarding_state.dart';
import 'package:brainy_bunny/models/onboarding_progress.dart';
import '../../l10n/app_localizations.dart';

/// Personalized summary screen showing child's achievements and personalized message
class PersonalizedSummaryScreen extends StatefulWidget {
  final OnboardingState state;
  final OnboardingProgress progress;
  final VoidCallback onContinue;

  const PersonalizedSummaryScreen({
    super.key,
    required this.state,
    required this.progress,
    required this.onContinue,
  });

  @override
  State<PersonalizedSummaryScreen> createState() =>
      _PersonalizedSummaryScreenState();
}

class _PersonalizedSummaryScreenState
    extends State<PersonalizedSummaryScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onManualContinue() {
    widget.onContinue();
  }

  // Get personalized content based on child age
  Map<String, dynamic> _getAgeSpecificContent(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final childAge = widget.state.childAge ?? 3;

    // Age-specific headlines and content from l10n
    if (childAge == 2) {
      return {
        'headline': l10n.summary_age2_headline,
        'card1Title': l10n.summary_age2_card1_title,
        'card1Points': [
          l10n.summary_age2_card1_point1,
          l10n.summary_age2_card1_point2,
          l10n.summary_age2_card1_point3,
          l10n.summary_age2_card1_point4,
        ],
        'card2Title': l10n.summary_age2_card2_title,
        'card2Points': [
          l10n.summary_age2_card2_point1,
          l10n.summary_age2_card2_point2,
          l10n.summary_age2_card2_point3,
          l10n.summary_age2_card2_point4,
        ],
      };
    } else if (childAge == 3) {
      return {
        'headline': l10n.summary_age3_headline,
        'card1Title': l10n.summary_age3_card1_title,
        'card1Points': [
          l10n.summary_age3_card1_point1,
          l10n.summary_age3_card1_point2,
          l10n.summary_age3_card1_point3,
          l10n.summary_age3_card1_point4,
        ],
        'card2Title': l10n.summary_age3_card2_title,
        'card2Points': [
          l10n.summary_age3_card2_point1,
          l10n.summary_age3_card2_point2,
          l10n.summary_age3_card2_point3,
          l10n.summary_age3_card2_point4,
        ],
      };
    } else if (childAge == 4) {
      return {
        'headline': l10n.summary_age4_headline,
        'card1Title': l10n.summary_age4_card1_title,
        'card1Points': [
          l10n.summary_age4_card1_point1,
          l10n.summary_age4_card1_point2,
          l10n.summary_age4_card1_point3,
          l10n.summary_age4_card1_point4,
        ],
        'card2Title': l10n.summary_age4_card2_title,
        'card2Points': [
          l10n.summary_age4_card2_point1,
          l10n.summary_age4_card2_point2,
          l10n.summary_age4_card2_point3,
          l10n.summary_age4_card2_point4,
        ],
      };
    } else {
      // 5+ years
      return {
        'headline': l10n.summary_age5_headline,
        'card1Title': l10n.summary_age5_card1_title,
        'card1Points': [
          l10n.summary_age5_card1_point1,
          l10n.summary_age5_card1_point2,
          l10n.summary_age5_card1_point3,
          l10n.summary_age5_card1_point4,
        ],
        'card2Title': l10n.summary_age5_card2_title,
        'card2Points': [
          l10n.summary_age5_card2_point1,
          l10n.summary_age5_card2_point2,
          l10n.summary_age5_card2_point3,
          l10n.summary_age5_card2_point4,
        ],
      };
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final content = _getAgeSpecificContent(context);

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Progress indicator
              Row(
                children: [
                  Text(
                    '6/7',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: LinearProgressIndicator(
                      value: 6 / 7,
                      backgroundColor: Colors.grey.shade200,
                      valueColor: const AlwaysStoppedAnimation<Color>(
                        Color(0xFF4A90E2),
                      ),
                      minHeight: 6,
                      borderRadius: BorderRadius.circular(3),
                    ),
                  ),
                ],
              ),

              SizedBox(height: size.height * 0.04),

              // Headline with age personalization
              Text(
                content['headline'] as String,
                style: const TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2C3E50),
                  height: 1.3,
                ),
                textAlign: TextAlign.center,
              ),

              SizedBox(height: size.height * 0.04),

              // Two summary cards
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      // Card 1: What They'll Learn
                      _SummaryCard(
                        title: content['card1Title'] as String,
                        color: const Color(0xFF4A90E2),
                        points: List<String>.from(content['card1Points']),
                      ),

                      const SizedBox(height: 20),

                      // Card 2: Why It Works
                      _SummaryCard(
                        title: content['card2Title'] as String,
                        color: const Color(0xFF4CAF50),
                        points: List<String>.from(content['card2Points']),
                      ),
                    ],
                  ),
                ),
              ),

              SizedBox(height: size.height * 0.03),

              // Continue button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _onManualContinue,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF4A90E2),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 18),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    elevation: 2,
                  ),
                  child: const Text(
                    'Start Learning Journey',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _SummaryCard extends StatelessWidget {
  final String title;
  final Color color;
  final List<String> points;

  const _SummaryCard({
    required this.title,
    required this.color,
    required this.points,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 16),
          ...points.map((point) => Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(
                      Icons.check_circle,
                      color: color,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        point,
                        style: TextStyle(
                          fontSize: 15,
                          color: Colors.grey.shade800,
                          height: 1.4,
                        ),
                      ),
                    ),
                  ],
                ),
              )),
        ],
      ),
    );
  }
}

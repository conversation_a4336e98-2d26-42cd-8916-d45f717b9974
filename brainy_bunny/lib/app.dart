// lib/app.dart
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:brainy_bunny/ui/screens/splash_screen.dart';
import 'package:flutter/foundation.dart';
import 'flutter_gen/gen_l10n/app_localizations.dart';

class BrainyBunnyApp extends StatelessWidget {
  const BrainyBunnyApp({super.key});

  @override
  Widget build(BuildContext context) {
    if (kDebugMode) {
      print('🎮 Building BrainyBunnyApp widget');
    }

    return MaterialApp(
      title: 'Brainy Bunny',
      debugShowCheckedModeBanner: false,

      // Localization delegates
      localizationsDelegates: const [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],

      // Supported locales (18 languages)
      supportedLocales: const [
        Locale('en'), // English
        Locale('ar'), // Arabic
        Locale('es'), // Spanish (Spain)
        Locale('es', 'MX'), // Spanish (Mexico)
        Locale('pt', 'BR'), // Portuguese (Brazil)
        Locale('pt'), // Portuguese (Portugal)
        Locale('ko'), // Korean
        Locale('zh', 'Hans'), // Chinese Simplified
        Locale('zh', 'Hant'), // Chinese Traditional
        Locale('hi'), // Hindi
        Locale('id'), // Indonesian
        Locale('ru'), // Russian
        Locale('fr'), // French (France)
        Locale('fr', 'CA'), // French (Canada)
        Locale('de'), // German
        Locale('ja'), // Japanese
        Locale('it'), // Italian
        Locale('ca'), // Catalan
      ],

      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
        // Enhanced theme for better UX
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
        textButtonTheme: TextButtonThemeData(
          style: TextButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
        cardTheme: const CardThemeData(
          elevation: 4,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.circular(12)),
          ),
        ),
        dialogTheme: const DialogThemeData(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.circular(16)),
          ),
        ),
      ),
      home: const SplashScreen(), // Changed from HomeScreen to SplashScreen

      // Error handling for the entire app
      builder: (context, child) {
        // Handle any widget errors gracefully
        ErrorWidget.builder = (FlutterErrorDetails errorDetails) {
          if (kDebugMode) {
            print('Widget error caught: ${errorDetails.exception}');
            print('Stack trace: ${errorDetails.stack}');
          }

          return MaterialApp(
            home: Scaffold(
              body: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(Icons.error_outline, size: 64, color: Colors.red),
                    const SizedBox(height: 16),
                    const Text(
                      'Something went wrong',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    if (kDebugMode) ...[
                      Text(
                        errorDetails.exception.toString(),
                        style: const TextStyle(fontSize: 12),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                    ],
                    ElevatedButton(
                      onPressed: () {
                        // Try to restart the app
                        Navigator.of(context).pushAndRemoveUntil(
                          MaterialPageRoute(builder: (context) => const SplashScreen()),
                              (route) => false,
                        );
                      },
                      child: const Text('Restart'),
                    ),
                  ],
                ),
              ),
            ),
          );
        };

        return child ?? const SizedBox.shrink();
      },
    );
  }
}
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:brainy_bunny/ui/screens/home_screen.dart';
import 'package:brainy_bunny/services/app_initialization_service.dart';
import 'package:brainy_bunny/models/onboarding_progress.dart';
import 'package:brainy_bunny/onboarding/onboarding_screen.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _logoController;
  late AnimationController _flashController;
  late Animation<double> _logoOpacityAnimation;
  late Animation<double> _logoScaleAnimation;
  late Animation<double> _flashAnimation;

  bool _initializationComplete = false;
  bool _showError = false;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();

    // Logo animation controller
    _logoController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // Flash animation controller
    _flashController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // Logo fade in animation
    _logoOpacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: Curves.easeInOut,
    ));

    // Logo scale animation
    _logoScaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: Curves.elasticOut,
    ));

    // Flash animation
    _flashAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _flashController,
      curve: Curves.easeInOut,
    ));

    _startSplashSequence();
  }

  Future<void> _startSplashSequence() async {
    // Start logo animation immediately - no hardcoded delays
    _logoController.forward();

    // Start initialization in the background
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    try {
      if (kDebugMode) {
        print('🎬 Starting app initialization from splash screen...');
      }

      // Initialize all services in the background with timeout protection
      final success = await AppInitializationService.instance.initialize().timeout(
        const Duration(seconds: 15), // Maximum 15 seconds for initialization
        onTimeout: () {
          if (kDebugMode) {
            print('⏰ App initialization timed out after 15 seconds');
            print('   Proceeding to home screen with partial initialization');
          }
          return false; // Return false on timeout, but don't throw
        },
      );

      if (!mounted) return;

      // Always proceed to home screen, even if initialization failed/timed out
      _initializationComplete = true;

      // Wait for logo animation to complete (minimum 1 second for UX)
      await Future.delayed(const Duration(milliseconds: 1000));

      if (mounted) {
        // Flash effect before navigation
        _flashController.forward();
        await Future.delayed(const Duration(milliseconds: 300));

        if (mounted) {
          if (kDebugMode) {
            print('🏠 Navigating to home screen (initialization success: $success)');
          }
          _navigateToHome();
        }
      }

    } catch (e) {
      if (kDebugMode) {
        print('❌ Splash screen initialization error: $e');
        print('   Proceeding to home screen anyway');
      }

      if (mounted) {
        // Even on error, proceed to home screen after showing logo
        _initializationComplete = true;
        await Future.delayed(const Duration(milliseconds: 1000));

        if (mounted) {
          _flashController.forward();
          await Future.delayed(const Duration(milliseconds: 300));

          if (mounted) {
            _navigateToHome();
          }
        }
      }
    }
  }

  Future<void> _navigateToHome() async {
    // Check if user has completed onboarding
    final progress = await OnboardingProgress.load();

    if (!progress.isCompleted) {
      // User needs to go through onboarding
      if (kDebugMode) {
        print('🎯 First launch - navigating to onboarding');
      }

      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => OnboardingScreen(
              onComplete: () {
                // After onboarding completes, navigate to home
                Navigator.of(context).pushReplacement(
                  MaterialPageRoute(
                    builder: (context) => const HomeScreen(),
                  ),
                );
              },
            ),
          ),
        );
      }
    } else {
      // User has completed onboarding, go straight to home
      if (kDebugMode) {
        print('🏠 Returning user - navigating to home screen');
      }

      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => const HomeScreen(),
          ),
        );
      }
    }
  }

  void _retryInitialization() {
    setState(() {
      _showError = false;
      _errorMessage = '';
    });

    // Reset initialization service and try again
    AppInitializationService.instance.reset();
    _initializeApp();
  }

  @override
  void dispose() {
    _logoController.dispose();
    _flashController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: _showError ? _buildErrorView() : _buildSplashView(),
    );
  }

  Widget _buildSplashView() {
    return AnimatedBuilder(
      animation: Listenable.merge([_logoController, _flashController]),
      builder: (context, child) {
        return Stack(
          children: [
            // Main logo
            Center(
              child: Transform.scale(
                scale: _logoScaleAnimation.value,
                child: Opacity(
                  opacity: _logoOpacityAnimation.value,
                  child: Container(
                    width: MediaQuery.of(context).size.width * 0.9,
                    height: MediaQuery.of(context).size.height * 0.6,
                    child: Image.asset(
                      'assets/images/good-karma-lab-logo.jpeg',
                      fit: BoxFit.contain,
                    ),
                  ),
                ),
              ),
            ),

            // Loading indicator
            if (!_initializationComplete)
              const Positioned(
                bottom: 100,
                left: 0,
                right: 0,
                child: Center(
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                  ),
                ),
              ),

            // Flash overlay
            if (_flashAnimation.value > 0)
              Container(
                width: double.infinity,
                height: double.infinity,
                color: Colors.white.withOpacity(_flashAnimation.value),
              ),
          ],
        );
      },
    );
  }

  Widget _buildErrorView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            const Text(
              'Initialization Failed',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage,
              style: const TextStyle(fontSize: 14),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: _retryInitialization,
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }
}

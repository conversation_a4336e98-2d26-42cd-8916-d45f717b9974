// lib/onboarding/screens/child_age_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:brainy_bunny/models/onboarding_state.dart';
import 'package:brainy_bunny/constants/onboarding_constants.dart';

/// Child age selection screen
class ChildAgeScreen extends StatefulWidget {
  final OnboardingState state;
  final VoidCallback onContinue;

  const ChildAgeScreen({
    super.key,
    required this.state,
    required this.onContinue,
  });

  @override
  State<ChildAgeScreen> createState() => _ChildAgeScreenState();
}

class _ChildAgeScreenState extends State<ChildAgeScreen> {
  int? _selectedAge;

  @override
  void initState() {
    super.initState();
    _selectedAge = widget.state.childAge;
  }

  void _selectAge(int age) {
    setState(() {
      _selectedAge = age;
    });

    widget.state.childAge = age;
    widget.state.save();

    // Auto-advance after selection with delay
    Future.delayed(
      const Duration(milliseconds: OnboardingConstants.AGE_SELECTION_DELAY),
      () {
        if (mounted) {
          widget.onContinue();
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final size = MediaQuery.of(context).size;

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Progress indicator
              Row(
                children: [
                  Text(
                    '1/3',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: LinearProgressIndicator(
                      value: 1 / 3,
                      backgroundColor: Colors.grey.shade200,
                      valueColor: const AlwaysStoppedAnimation<Color>(
                        Color(0xFF4A90E2),
                      ),
                      minHeight: 6,
                      borderRadius: BorderRadius.circular(3),
                    ),
                  ),
                ],
              ),

              SizedBox(height: size.height * 0.06),

              // Birthday cake icon
              Center(
                child: Container(
                  width: 80,
                  height: 80,
                  decoration: const BoxDecoration(
                    color: Color(0xFFFFF3E0),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.cake,
                    size: 40,
                    color: Color(0xFFFF9800),
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Headline
              const Text(
                'How old is your little one?',
                style: TextStyle(
                  fontSize: 26,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1E3A5F),
                  height: 1.3,
                ),
              ),

              SizedBox(height: size.height * 0.05),

              // Age option cards
              Expanded(
                child: GridView.count(
                  crossAxisCount: 2,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                  childAspectRatio: 1.1,
                  children: [
                    _AgeCard(
                      age: 2,
                      emoji: '🧸',
                      ageText: '2 years',
                      descriptor: '"Just starting\nto explore"',
                      isSelected: _selectedAge == 2,
                      onTap: () => _selectAge(2),
                    ),
                    _AgeCard(
                      age: 3,
                      emoji: '🚲',
                      ageText: '3 years',
                      descriptor: '"Building\nconfidence"',
                      isSelected: _selectedAge == 3,
                      onTap: () => _selectAge(3),
                    ),
                    _AgeCard(
                      age: 4,
                      emoji: '🖍️',
                      ageText: '4 years',
                      descriptor: '"Ready for\nchallenges"',
                      isSelected: _selectedAge == 4,
                      onTap: () => _selectAge(4),
                    ),
                    _AgeCard(
                      age: 5,
                      emoji: '🎒',
                      ageText: '5+ years',
                      descriptor: '"Preparing\nfor school"',
                      isSelected: _selectedAge == 5,
                      onTap: () => _selectAge(5),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Bottom text
              const Center(
                child: Text(
                  'All games work beautifully\nfor ages 2-5',
                  style: TextStyle(
                    fontSize: 14,
                    color: Color(0xFF999999),
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

              const SizedBox(height: 24),
            ],
          ),
        ),
      ),
    );
  }
}

class _AgeCard extends StatefulWidget {
  final int age;
  final String emoji;
  final String ageText;
  final String descriptor;
  final bool isSelected;
  final VoidCallback onTap;

  const _AgeCard({
    required this.age,
    required this.emoji,
    required this.ageText,
    required this.descriptor,
    required this.isSelected,
    required this.onTap,
  });

  @override
  State<_AgeCard> createState() => _AgeCardState();
}

class _AgeCardState extends State<_AgeCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(_AgeCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isSelected && !oldWidget.isSelected) {
      _controller.forward();
    } else if (!widget.isSelected && oldWidget.isSelected) {
      _controller.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    return ScaleTransition(
      scale: _scaleAnimation,
      child: InkWell(
        onTap: widget.onTap,
        borderRadius: BorderRadius.circular(16),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: widget.isSelected
                  ? const Color(0xFF4CAF50)
                  : const Color(0xFFE0E0E0),
              width: widget.isSelected ? 3 : 2,
            ),
          ),
          child: Stack(
            children: [
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    widget.emoji,
                    style: const TextStyle(fontSize: 40),
                  ),
                  const SizedBox(height: 6),
                  Text(
                    widget.ageText,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF333333),
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    widget.descriptor,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
              if (widget.isSelected)
                const Positioned(
                  top: 0,
                  right: 0,
                  child: Icon(
                    Icons.check_circle,
                    color: Color(0xFF4CAF50),
                    size: 24,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}

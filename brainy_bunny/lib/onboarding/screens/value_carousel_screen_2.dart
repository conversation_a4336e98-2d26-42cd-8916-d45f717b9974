// lib/onboarding/screens/value_carousel_screen_2.dart
import 'package:flutter/material.dart';
import '../../l10n/app_localizations.dart';

/// Value Carousel Screen 2: Built for little learners
/// Shows toddler-friendly interaction design
class ValueCarouselScreen2 extends StatefulWidget {
  final VoidCallback onContinue;
  final VoidCallback onBack;
  final VoidCallback onSkip;

  const ValueCarouselScreen2({
    super.key,
    required this.onContinue,
    required this.onBack,
    required this.onSkip,
  });

  @override
  State<ValueCarouselScreen2> createState() => _ValueCarouselScreen2State();
}

class _ValueCarouselScreen2State extends State<ValueCarouselScreen2>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            children: [
              // Top bar with progress and skip
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '4/7',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  TextButton(
                    onPressed: widget.onSkip,
                    child: Text(
                      'Skip',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ),
                ],
              ),

              SizedBox(height: size.height * 0.02),

              // Icon - Hand tapping with sparkle
              ScaleTransition(
                scale: Tween<double>(begin: 0.8, end: 1.0).animate(
                  CurvedAnimation(
                    parent: _animationController,
                    curve: Curves.easeOutBack,
                  ),
                ),
                child: Container(
                  width: 80,
                  height: 80,
                  decoration: const BoxDecoration(
                    color: Color(0xFFFFF3E0),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.touch_app,
                    size: 40,
                    color: Color(0xFFFF9800),
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Headline
              const Text(
                'Built for little learners',
                style: TextStyle(
                  fontSize: 26,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2C3E50),
                  height: 1.3,
                ),
                textAlign: TextAlign.center,
              ),

              SizedBox(height: size.height * 0.04),

              // Features with checkmarks
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      _buildFeature('Tap, drag, match—simple interactions'),
                      const SizedBox(height: 16),
                      _buildFeature('Immediate visual feedback'),
                      const SizedBox(height: 16),
                      _buildFeature('Progressive difficulty'),
                      const SizedBox(height: 16),
                      _buildFeature('Short sessions (5-15 min)'),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Subtext
              Text(
                'Perfect for car rides, waiting rooms, or quiet time at home',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                  fontStyle: FontStyle.italic,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 24),

              // Navigation
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Back button
                  TextButton.icon(
                    onPressed: widget.onBack,
                    icon: const Icon(Icons.arrow_back, size: 20),
                    label: const Text(
                      'Back',
                      style: TextStyle(fontSize: 16),
                    ),
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.grey.shade600,
                    ),
                  ),

                  // Page indicator
                  Row(
                    children: [
                      _PageDot(isActive: false),
                      const SizedBox(width: 8),
                      _PageDot(isActive: true),
                      const SizedBox(width: 8),
                      _PageDot(isActive: false),
                    ],
                  ),

                  // Continue button
                  ElevatedButton(
                    onPressed: widget.onContinue,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFFFF9800),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 32,
                        vertical: 14,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          'Next',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        SizedBox(width: 8),
                        Icon(Icons.arrow_forward, size: 20),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFeature(String text) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Icon(
          Icons.check_circle,
          color: Color(0xFF4CAF50),
          size: 24,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            text,
            style: const TextStyle(
              fontSize: 16,
              color: Color(0xFF2C3E50),
              height: 1.4,
            ),
          ),
        ),
      ],
    );
  }
}

class _PageDot extends StatelessWidget {
  final bool isActive;

  const _PageDot({required this.isActive});

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      width: isActive ? 24 : 8,
      height: 8,
      decoration: BoxDecoration(
        color: isActive ? const Color(0xFF27AE60) : Colors.grey.shade300,
        borderRadius: BorderRadius.circular(4),
      ),
    );
  }
}

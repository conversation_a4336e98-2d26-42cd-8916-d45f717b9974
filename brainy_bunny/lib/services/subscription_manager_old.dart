// lib/services/subscription_manager.dart
import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:brainy_bunny/constants/app_constants.dart';
import 'package:brainy_bunny/constants/subscription_constants.dart';
import 'package:brainy_bunny/services/analytics/analytics_manager.dart';
import 'package:brainy_bunny/services/purchase_manager.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

/// Subscription manager using native in_app_purchase for subscriptions
/// RevenueCat is used ONLY for customer tracking, not purchase management
/// Handles trial period, subscription tiers, and grandfathering of lifetime users
class SubscriptionManager {
  // Singleton pattern
  static final SubscriptionManager _instance = SubscriptionManager._internal();
  factory SubscriptionManager() => _instance;
  SubscriptionManager._internal();

  // State
  bool _isInitialized = false;
  bool _hasActiveSubscription = false;
  bool _isLifetimeUser = false; // Grandfathered users
  String? _activeProductId;
  DateTime? _expirationDate;
  bool _isInTrialPeriod = false;

  // Streams
  final _subscriptionStateController = StreamController<bool>.broadcast();
  Stream<bool> get subscriptionStream => _subscriptionStateController.stream;

  // Getters
  bool get isInitialized => _isInitialized;
  bool get hasActiveSubscription => _hasActiveSubscription || _isLifetimeUser;
  bool get isLifetimeUser => _isLifetimeUser;
  bool get isInTrialPeriod => _isInTrialPeriod;
  String? get activeProductId => _activeProductId;
  DateTime? get expirationDate => _expirationDate;

  /// Get active subscription tier
  String? get activeSubscriptionTier {
    if (_isLifetimeUser) return SubscriptionConstants.TIER_LIFETIME;
    return _activeProductId;
  }

  /// Initialize RevenueCat and load subscription status
  Future<void> initialize({required String apiKey}) async {
    if (_isInitialized) return;

    try {
      if (kDebugMode) {
        print('🔧 === SUBSCRIPTION MANAGER INITIALIZATION ===');
      }

      // Configure RevenueCat
      final configuration = PurchasesConfiguration(apiKey);
      await Purchases.configure(configuration);

      // Enable debug logging in debug mode
      if (kDebugMode) {
        await Purchases.setLogLevel(LogLevel.debug);
        print('   RevenueCat configured with API key');
      }

      // Set up listener for subscription status changes
      Purchases.addCustomerInfoUpdateListener(_handleCustomerInfoUpdate);

      // Check for grandfathered lifetime users
      await _checkLifetimeUser();

      // Load current subscription status
      await _loadSubscriptionStatus();

      _isInitialized = true;

      if (kDebugMode) {
        print('✅ === SUBSCRIPTION INITIALIZATION COMPLETE ===');
        print('   Active subscription: $_hasActiveSubscription');
        print('   Lifetime user: $_isLifetimeUser');
        print('   In trial: $_isInTrialPeriod');
        print('   Product ID: $_activeProductId');
        print('   Expiration: $_expirationDate');
        print('============================================');
      }

    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('❌ Subscription initialization error: $e');
        print('   Stack trace: $stackTrace');
      }

      _isInitialized = true;
      _hasActiveSubscription = false;
    }
  }

  /// Check if user is a grandfathered lifetime user
  Future<void> _checkLifetimeUser() async {
    try {
      // Check the old PurchaseManager's storage for lifetime purchase
      // This would have been set by the old one-time purchase system
      final legacyPurchase = await _checkLegacyPurchase();

      if (legacyPurchase) {
        _isLifetimeUser = true;

        if (kDebugMode) {
          print('🎁 User is grandfathered as lifetime premium user');
        }

        // Track grandfathered user
        await AnalyticsManager.instance.trackEvent(
          eventName: 'user_grandfathered',
          parameters: {
            'source': 'lifetime_purchase',
          },
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Error checking lifetime user status: $e');
      }
    }
  }

  /// Check legacy one-time purchase from old PurchaseManager
  Future<bool> _checkLegacyPurchase() async {
    try {
      // Import and use the old PurchaseManager to check status
      // This ensures we don't duplicate the checking logic
      final purchaseManager = PurchaseManager();
      if (!purchaseManager.isInitialized) {
        await purchaseManager.initialize();
      }

      return purchaseManager.isPurchased;
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Error checking legacy purchase: $e');
      }
      return false;
    }
  }

  /// Load current subscription status from RevenueCat
  Future<void> _loadSubscriptionStatus() async {
    try {
      final customerInfo = await Purchases.getCustomerInfo();
      _updateSubscriptionState(customerInfo);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading subscription status: $e');
      }
    }
  }

  /// Handle customer info updates from RevenueCat
  void _handleCustomerInfoUpdate(CustomerInfo customerInfo) {
    if (kDebugMode) {
      print('🔄 Customer info updated');
    }
    _updateSubscriptionState(customerInfo);
  }

  /// Update subscription state from CustomerInfo
  void _updateSubscriptionState(CustomerInfo customerInfo) {
    try {
      // Check for active entitlement
      final premiumEntitlement = customerInfo.entitlements.active['premium'];

      if (premiumEntitlement != null) {
        _hasActiveSubscription = true;
        _activeProductId = premiumEntitlement.productIdentifier;
        _expirationDate = premiumEntitlement.expirationDate != null
            ? DateTime.parse(premiumEntitlement.expirationDate!)
            : null;

        // Check if in trial period
        _isInTrialPeriod = premiumEntitlement.periodType == PeriodType.trial;

        if (kDebugMode) {
          print('✅ Active subscription found:');
          print('   Product: $_activeProductId');
          print('   Expiration: $_expirationDate');
          print('   In trial: $_isInTrialPeriod');
        }
      } else {
        _hasActiveSubscription = false;
        _activeProductId = null;
        _expirationDate = null;
        _isInTrialPeriod = false;

        if (kDebugMode) {
          print('❌ No active subscription found');
        }
      }

      // Notify listeners
      _subscriptionStateController.add(_hasActiveSubscription || _isLifetimeUser);

    } catch (e) {
      if (kDebugMode) {
        print('❌ Error updating subscription state: $e');
      }
    }
  }

  /// Get available packages/offerings
  Future<Offerings?> getOfferings() async {
    try {
      if (kDebugMode) {
        print('📦 Fetching available offerings...');
      }

      final offerings = await Purchases.getOfferings();

      if (offerings.current != null) {
        if (kDebugMode) {
          print('✅ Current offering loaded:');
          print('   Packages: ${offerings.current!.availablePackages.length}');

          for (var package in offerings.current!.availablePackages) {
            print('   - ${package.identifier}: ${package.storeProduct.priceString}');
          }
        }

        return offerings;
      } else {
        if (kDebugMode) {
          print('⚠️ No current offering available');
        }
        return null;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error fetching offerings: $e');
      }
      return null;
    }
  }

  /// Purchase a subscription package
  Future<bool> purchasePackage(Package package) async {
    try {
      if (kDebugMode) {
        print('🛒 Initiating purchase for: ${package.identifier}');
        print('   Product: ${package.storeProduct.identifier}');
        print('   Price: ${package.storeProduct.priceString}');
      }

      // Track purchase attempt
      await AnalyticsManager.instance.trackEvent(
        eventName: 'subscription_purchase_initiated',
        parameters: {
          'product_id': package.storeProduct.identifier,
          'price': package.storeProduct.price.toString(),
          'currency': package.storeProduct.currencyCode,
        },
      );

      final purchaseResult = await Purchases.purchasePackage(package);

      // Check if purchase was successful
      final premiumEntitlement = purchaseResult.customerInfo.entitlements.active['premium'];

      if (premiumEntitlement != null) {
        if (kDebugMode) {
          print('✅ Purchase successful!');
        }

        // Track successful purchase
        await AnalyticsManager.instance.trackSubscriptionPurchased(
          productId: package.storeProduct.identifier,
          price: package.storeProduct.price,
          currency: package.storeProduct.currencyCode,
          isTrialConversion: _isInTrialPeriod,
        );

        return true;
      } else {
        if (kDebugMode) {
          print('⚠️ Purchase completed but no active entitlement');
        }
        return false;
      }
    } on PurchasesErrorCode catch (error) {
      if (kDebugMode) {
        print('❌ Purchase error: ${error.name}');
      }

      // Track purchase failure (excluding user cancellations)
      if (error != PurchasesErrorCode.purchaseCancelledError) {
        await AnalyticsManager.instance.trackEvent(
          eventName: 'subscription_purchase_failed',
          parameters: {
            'error': error.name,
            'product_id': package.storeProduct.identifier,
          },
        );
      }

      return false;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Unexpected purchase error: $e');
      }

      await AnalyticsManager.instance.trackEvent(
        eventName: 'subscription_purchase_error',
        parameters: {
          'error': e.toString(),
        },
      );

      return false;
    }
  }

  /// Restore previous purchases
  Future<bool> restorePurchases() async {
    try {
      if (kDebugMode) {
        print('🔄 Restoring purchases...');
      }

      final customerInfo = await Purchases.restorePurchases();

      _updateSubscriptionState(customerInfo);

      if (_hasActiveSubscription || _isLifetimeUser) {
        if (kDebugMode) {
          print('✅ Purchases restored successfully');
        }

        await AnalyticsManager.instance.trackEvent(
          eventName: 'subscription_restored',
          parameters: {
            'product_id': _activeProductId ?? 'lifetime',
            'is_lifetime': _isLifetimeUser.toString(),
          },
        );

        return true;
      } else {
        if (kDebugMode) {
          print('⚠️ No purchases to restore');
        }
        return false;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error restoring purchases: $e');
      }

      await AnalyticsManager.instance.trackEvent(
        eventName: 'subscription_restore_failed',
        parameters: {
          'error': e.toString(),
        },
      );

      return false;
    }
  }

  /// Check if a game/content is unlocked
  bool isContentUnlocked(int contentIndex) {
    // Free content is always available
    if (contentIndex < 5) {
      return true;
    }

    // Premium content requires active subscription or lifetime access
    return _hasActiveSubscription || _isLifetimeUser;
  }

  /// Get human-readable subscription status
  String getSubscriptionStatusText() {
    if (_isLifetimeUser) {
      return 'Lifetime Premium';
    }

    if (!_hasActiveSubscription) {
      return 'No Active Subscription';
    }

    if (_isInTrialPeriod) {
      final daysLeft = _expirationDate != null
          ? _expirationDate!.difference(DateTime.now()).inDays
          : 7;
      return 'Free Trial ($daysLeft days left)';
    }

    String planName = 'Premium';
    if (_activeProductId == SubscriptionConstants.SUBSCRIPTION_WEEKLY) {
      planName = 'Weekly';
    } else if (_activeProductId == SubscriptionConstants.SUBSCRIPTION_MONTHLY) {
      planName = 'Monthly';
    } else if (_activeProductId == SubscriptionConstants.SUBSCRIPTION_YEARLY) {
      planName = 'Yearly';
    }

    return '$planName Subscription';
  }

  /// Sync subscription status from server (Firestore)
  /// This provides server-side verification of subscription status
  Future<void> syncWithServer() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        if (kDebugMode) {
          print('⚠️ Cannot sync with server - no authenticated user');
        }
        return;
      }

      if (kDebugMode) {
        print('🔄 Syncing subscription status with server...');
      }

      final userDoc = await FirebaseFirestore.instance
          .collection('users')
          .doc(user.uid)
          .get();

      if (userDoc.exists) {
        final data = userDoc.data();
        if (data != null) {
          final serverHasSubscription = data['hasActiveSubscription'] ?? false;
          final subscriptionData = data['subscription'] as Map<String, dynamic>?;

          if (serverHasSubscription && subscriptionData != null) {
            // Server confirms active subscription
            final productId = subscriptionData['productId'] as String?;
            final status = subscriptionData['status'] as String?;
            final expirationTimestamp = subscriptionData['expirationAt'] as Timestamp?;

            if (status == 'active' || status == 'cancelled' || status == 'billing_issue') {
              final previousState = _hasActiveSubscription;

              _hasActiveSubscription = true;
              _activeProductId = productId;

              if (expirationTimestamp != null) {
                _expirationDate = expirationTimestamp.toDate();
              }

              if (kDebugMode) {
                print('✅ Server-verified active subscription: $productId');
                print('   Status: $status');
                print('   Expiration: $_expirationDate');
              }

              // Notify listeners if state changed
              if (previousState != _hasActiveSubscription) {
                _subscriptionStateController.add(_hasActiveSubscription || _isLifetimeUser);
              }
            }
          } else {
            // Server says no active subscription
            if (_hasActiveSubscription && !_isLifetimeUser) {
              if (kDebugMode) {
                print('⚠️ Server override: Subscription not active on server');
                print('   Client had active: $_hasActiveSubscription');
                print('   Server says: $serverHasSubscription');
              }

              _hasActiveSubscription = false;
              _activeProductId = null;
              _expirationDate = null;

              _subscriptionStateController.add(false);

              // Log discrepancy for investigation
              await AnalyticsManager.instance.trackEvent(
                eventName: 'subscription_server_override',
                parameters: {
                  'client_status': 'active',
                  'server_status': 'inactive',
                },
              );
            }
          }
        }
      } else {
        if (kDebugMode) {
          print('⚠️ No server record found for user');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error syncing with server: $e');
      }

      // Track sync errors
      await AnalyticsManager.instance.trackEvent(
        eventName: 'subscription_sync_error',
        parameters: {
          'error': e.toString(),
        },
      );
    }
  }

  /// Reconcile RevenueCat and server status
  /// Call this periodically to ensure consistency
  Future<void> reconcileWithRevenueCat() async {
    try {
      if (kDebugMode) {
        print('🔄 Reconciling RevenueCat with server...');
      }

      // Get latest customer info from RevenueCat
      final customerInfo = await Purchases.getCustomerInfo();
      final revenueCatHasActive = customerInfo.entitlements.active.containsKey('premium');

      // Get server status
      await syncWithServer();

      // Compare and log discrepancies
      if (_hasActiveSubscription != revenueCatHasActive) {
        if (kDebugMode) {
          print('⚠️ DISCREPANCY: RevenueCat vs Server mismatch');
          print('   RevenueCat: $revenueCatHasActive');
          print('   Server: $_hasActiveSubscription');
          print('   Lifetime user: $_isLifetimeUser');
        }

        // Trust RevenueCat as source of truth (unless lifetime user)
        if (!_isLifetimeUser) {
          _updateSubscriptionState(customerInfo);
        }

        // Log for investigation
        await AnalyticsManager.instance.trackEvent(
          eventName: 'subscription_reconciliation_mismatch',
          parameters: {
            'revenuecat_status': revenueCatHasActive.toString(),
            'server_status': _hasActiveSubscription.toString(),
            'is_lifetime': _isLifetimeUser.toString(),
          },
        );
      } else {
        if (kDebugMode) {
          print('✅ RevenueCat and server are in sync');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Reconciliation error: $e');
      }

      await AnalyticsManager.instance.trackEvent(
        eventName: 'subscription_reconciliation_error',
        parameters: {
          'error': e.toString(),
        },
      );
    }
  }

  /// Dispose resources
  void dispose() {
    try {
      if (!_subscriptionStateController.isClosed) {
        _subscriptionStateController.close();
      }

      if (kDebugMode) {
        print('SubscriptionManager disposed');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error disposing SubscriptionManager: $e');
      }
    }
  }
}


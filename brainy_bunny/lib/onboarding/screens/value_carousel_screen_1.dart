// lib/onboarding/screens/value_carousel_screen_1.dart
import 'package:flutter/material.dart';
import '../../l10n/app_localizations.dart';

/// Value Carousel Screen 1: Variety
/// Shows the 15 games and categories
class ValueCarouselScreen1 extends StatefulWidget {
  final VoidCallback onContinue;
  final VoidCallback onSkip;

  const ValueCarouselScreen1({
    super.key,
    required this.onContinue,
    required this.onSkip,
  });

  @override
  State<ValueCarouselScreen1> createState() => _ValueCarouselScreen1State();
}

class _ValueCarouselScreen1State extends State<ValueCarouselScreen1>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;

  final List<Map<String, dynamic>> _categories = [
    {'icon': '🟦', 'name': 'Shapes & Geometry'},
    {'icon': '🎨', 'name': 'Colors & Patterns'},
    {'icon': '🐾', 'name': 'Animals & Nature'},
    {'icon': '👨‍⚕️', 'name': 'Professions & Roles'},
    {'icon': '✨', 'name': 'Cause & Effect'},
    {'icon': '💭', 'name': 'And more...'},
  ];

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            children: [
              // Top bar with progress and skip
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '3/7',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  TextButton(
                    onPressed: widget.onSkip,
                    child: Text(
                      'Skip',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ),
                ],
              ),

              SizedBox(height: size.height * 0.02),

              // Icon
              ScaleTransition(
                scale: Tween<double>(begin: 0.8, end: 1.0).animate(
                  CurvedAnimation(
                    parent: _animationController,
                    curve: Curves.easeOutBack,
                  ),
                ),
                child: Container(
                  width: 80,
                  height: 80,
                  decoration: const BoxDecoration(
                    color: Color(0xFFE8F4F8),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.grid_4x4,
                    size: 40,
                    color: Color(0xFF4A90E2),
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Headline
              const Text(
                '15 games, 15 ways to grow',
                style: TextStyle(
                  fontSize: 26,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2C3E50),
                  height: 1.3,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 16),

              // Description
              Text(
                'From shapes to cause-and-effect—each game builds specific skills',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey.shade700,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),

              SizedBox(height: size.height * 0.04),

              // Category badges with stagger animation
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: _categories.asMap().entries.map((entry) {
                      final index = entry.key;
                      final category = entry.value;
                      return _buildCategoryBadge(category, index * 0.1);
                    }).toList(),
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Subtext
              Text(
                'Not just fun—actual learning in every match',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                  fontStyle: FontStyle.italic,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 24),

              // Navigation
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Page indicator
                  Row(
                    children: [
                      _PageDot(isActive: true),
                      const SizedBox(width: 8),
                      _PageDot(isActive: false),
                      const SizedBox(width: 8),
                      _PageDot(isActive: false),
                    ],
                  ),

                  // Continue button
                  ElevatedButton(
                    onPressed: widget.onContinue,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF4A90E2),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 32,
                        vertical: 14,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          'Next',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        SizedBox(width: 8),
                        Icon(Icons.arrow_forward, size: 20),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryBadge(Map<String, dynamic> category, double delay) {
    final delayedAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Interval(
        delay,
        1.0,
        curve: Curves.easeOut,
      ),
    );

    return FadeTransition(
      opacity: delayedAnimation,
      child: SlideTransition(
        position: Tween<Offset>(
          begin: const Offset(-0.3, 0),
          end: Offset.zero,
        ).animate(delayedAnimation),
        child: Container(
          margin: const EdgeInsets.only(bottom: 12),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: const Color(0xFFF0F8FF),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: const Color(0xFF4A90E2).withOpacity(0.3),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              Text(
                category['icon'],
                style: const TextStyle(fontSize: 24),
              ),
              const SizedBox(width: 12),
              Text(
                category['name'],
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF2C3E50),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _PageDot extends StatelessWidget {
  final bool isActive;

  const _PageDot({required this.isActive});

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      width: isActive ? 24 : 8,
      height: 8,
      decoration: BoxDecoration(
        color: isActive ? const Color(0xFF4A90E2) : Colors.grey.shade300,
        borderRadius: BorderRadius.circular(4),
      ),
    );
  }
}

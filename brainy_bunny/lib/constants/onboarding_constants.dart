// lib/constants/onboarding_constants.dart

/// Constants for the onboarding flow
class OnboardingConstants {
  // Total number of onboarding screens
  static const int TOTAL_SCREENS = 14;

  // Demo game configuration
  static const int DEMO_GAMES_COUNT = 3;
  static const List<int> DEMO_GAME_INDICES = [0, 1, 2]; // Games 1-3 are demo games

  // Demo game match counts (full game with all 5 matches)
  static const int DEMO_GAME_MATCHES = 5;

  // Screen indices for orientation management
  static const int FIRST_DEMO_GAME_SCREEN = 5;
  static const int LAST_DEMO_GAME_SCREEN = 7;
  static const int ORIENTATION_TRANSITION_TO_GAMES = 4;
  static const int ORIENTATION_TRANSITION_FROM_GAMES = 8;

  // Age options for child age selection
  static const List<int> AGE_OPTIONS = [2, 3, 4, 5];

  // Progress indicator steps
  static const int PERSONALIZATION_STEPS = 4;

  // Auto-advance timing (milliseconds)
  static const int PHILOSOPHY_AUTO_ADVANCE_DELAY = 5000;
  static const int AGE_SELECTION_DELAY = 500;
  static const int TRANSITION_ANIMATION_DURATION = 2000;

  // Name validation
  static const int MIN_NAME_LENGTH = 2;
  static const int MAX_NAME_LENGTH = 20;

  // Badge names for demo games
  static const List<String> DEMO_GAME_BADGES = [
    'Shape Detective',
    'Memory Master',
    'Logic Star',
  ];

  // Demo game configurations
  static const Map<int, DemoGameConfig> DEMO_GAME_CONFIGS = {
    0: DemoGameConfig(
      gameIndex: 0,
      matchCount: 5,
      skillKey: 'demo_game_1_skill',
      scienceKey: 'demo_game_1_science',
      citationKey: 'demo_game_1_citation',
      badgeKey: 'demo_game_1_badge',
    ),
    1: DemoGameConfig(
      gameIndex: 1,
      matchCount: 5,
      skillKey: 'demo_game_2_skill',
      scienceKey: 'demo_game_2_science',
      citationKey: 'demo_game_2_citation',
      badgeKey: 'demo_game_2_badge',
    ),
    2: DemoGameConfig(
      gameIndex: 2,
      matchCount: 5,
      skillKey: 'demo_game_3_skill',
      scienceKey: 'demo_game_3_science',
      citationKey: 'demo_game_3_citation',
      badgeKey: 'demo_game_3_badge',
    ),
  };
}

/// Configuration for a demo game
class DemoGameConfig {
  final int gameIndex;
  final int matchCount;
  final String skillKey;
  final String scienceKey;
  final String citationKey;
  final String badgeKey;

  const DemoGameConfig({
    required this.gameIndex,
    required this.matchCount,
    required this.skillKey,
    required this.scienceKey,
    required this.citationKey,
    required this.badgeKey,
  });
}

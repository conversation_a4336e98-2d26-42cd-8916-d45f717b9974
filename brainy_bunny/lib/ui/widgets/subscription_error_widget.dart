// lib/ui/widgets/subscription_error_widget.dart
import 'package:flutter/material.dart';
import '../../l10n/app_localizations.dart';

/// Widget to display subscription loading/error states
/// Shows appropriate messages for offline, unavailable products, etc.
class SubscriptionErrorWidget extends StatelessWidget {
  final SubscriptionErrorType errorType;
  final String? errorMessage;
  final VoidCallback? onRetry;
  final VoidCallback? onContinue;

  const SubscriptionErrorWidget({
    super.key,
    required this.errorType,
    this.errorMessage,
    this.onRetry,
    this.onContinue,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Icon
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: _getIconColor().withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              _getIcon(),
              size: 40,
              color: _getIconColor(),
            ),
          ),

          const SizedBox(height: 24),

          // Title
          Text(
            _getTitle(l10n),
            style: const TextStyle(
              fontSize: 22,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2C3E50),
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 12),

          // Description
          Text(
            _getDescription(l10n),
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade700,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),

          if (errorMessage != null) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.shade200),
              ),
              child: Text(
                errorMessage!,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.red.shade900,
                  fontFamily: 'monospace',
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],

          const SizedBox(height: 24),

          // Action buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (onRetry != null) ...[
                ElevatedButton.icon(
                  onPressed: onRetry,
                  icon: const Icon(Icons.refresh),
                  label: Text(l10n.subscription_error_retry),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF4A90E2),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 14,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ],
              if (onRetry != null && onContinue != null)
                const SizedBox(width: 12),
              if (onContinue != null) ...[
                OutlinedButton(
                  onPressed: onContinue,
                  style: OutlinedButton.styleFrom(
                    foregroundColor: const Color(0xFF4A90E2),
                    side: const BorderSide(color: Color(0xFF4A90E2)),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 14,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Text(l10n.subscription_error_continue_free),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  IconData _getIcon() {
    switch (errorType) {
      case SubscriptionErrorType.loading:
        return Icons.hourglass_empty;
      case SubscriptionErrorType.offline:
        return Icons.wifi_off;
      case SubscriptionErrorType.notAvailable:
        return Icons.error_outline;
      case SubscriptionErrorType.productsNotFound:
        return Icons.shopping_cart_outlined;
      case SubscriptionErrorType.unknown:
        return Icons.warning_amber;
    }
  }

  Color _getIconColor() {
    switch (errorType) {
      case SubscriptionErrorType.loading:
        return const Color(0xFF4A90E2);
      case SubscriptionErrorType.offline:
        return Colors.orange;
      case SubscriptionErrorType.notAvailable:
        return Colors.red;
      case SubscriptionErrorType.productsNotFound:
        return Colors.orange;
      case SubscriptionErrorType.unknown:
        return Colors.red;
    }
  }

  String _getTitle(AppLocalizations l10n) {
    switch (errorType) {
      case SubscriptionErrorType.loading:
        return l10n.subscription_error_loading_title;
      case SubscriptionErrorType.offline:
        return l10n.subscription_error_offline_title;
      case SubscriptionErrorType.notAvailable:
        return l10n.subscription_error_not_available_title;
      case SubscriptionErrorType.productsNotFound:
        return l10n.subscription_error_products_not_found_title;
      case SubscriptionErrorType.unknown:
        return l10n.subscription_error_unknown_title;
    }
  }

  String _getDescription(AppLocalizations l10n) {
    switch (errorType) {
      case SubscriptionErrorType.loading:
        return l10n.subscription_error_loading_description;
      case SubscriptionErrorType.offline:
        return l10n.subscription_error_offline_description;
      case SubscriptionErrorType.notAvailable:
        return l10n.subscription_error_not_available_description;
      case SubscriptionErrorType.productsNotFound:
        return l10n.subscription_error_products_not_found_description;
      case SubscriptionErrorType.unknown:
        return l10n.subscription_error_unknown_description;
    }
  }
}

/// Loading indicator for subscription operations
class SubscriptionLoadingWidget extends StatelessWidget {
  final String? message;

  const SubscriptionLoadingWidget({super.key, this.message});

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4A90E2)),
          ),
          const SizedBox(height: 16),
          Text(
            message ?? l10n.subscription_loading,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade700,
            ),
          ),
        ],
      ),
    );
  }
}

/// Types of subscription errors
enum SubscriptionErrorType {
  loading, // Still loading products
  offline, // No internet connection
  notAvailable, // IAP service not available
  productsNotFound, // Products not configured in store
  unknown, // Generic error
}

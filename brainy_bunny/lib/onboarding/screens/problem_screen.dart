// lib/onboarding/screens/problem_screen.dart
import 'package:flutter/material.dart';
import '../../l10n/app_localizations.dart';

/// Problem Screen - Screen 2
/// Establishes empathy, acknowledges parent concerns
class ProblemScreen extends StatefulWidget {
  final VoidCallback onContinue;

  const ProblemScreen({
    super.key,
    required this.onContinue,
  });

  @override
  State<ProblemScreen> createState() => _ProblemScreenState();
}

class _ProblemScreenState extends State<ProblemScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final size = MediaQuery.of(context).size;

    return Scaffold(
      backgroundColor: Colors.white,
      body: Safe<PERSON>rea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const SizedBox(height: 16),

              // Headline - more empathetic, no icon
              FadeTransition(
                opacity: _animationController,
                child: Text(
                  l10n.onboarding_problem_headline,
                  style: const TextStyle(
                    fontSize: 26,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF333333),
                    height: 1.3,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

              const SizedBox(height: 24),

              // Problem points - reordered, no scrolling
              Column(
                children: [
                  _buildProblemPoint(
                    context,
                    icon: Icons.tv,
                    title: l10n.onboarding_problem_point_1_title,
                    description: l10n.onboarding_problem_point_1_description,
                    statistic: l10n.onboarding_problem_point_1_statistic,
                    delay: 0.2,
                  ),
                  const SizedBox(height: 8),
                  _buildProblemPoint(
                    context,
                    icon: Icons.trending_down,
                    title: l10n.onboarding_problem_point_4_title,
                    description: l10n.onboarding_problem_point_4_description,
                    statistic: l10n.onboarding_problem_point_4_statistic,
                    delay: 0.4,
                  ),
                  const SizedBox(height: 8),
                  _buildProblemPoint(
                    context,
                    icon: Icons.ads_click,
                    title: l10n.onboarding_problem_point_3_title,
                    description: l10n.onboarding_problem_point_3_description,
                    statistic: l10n.onboarding_problem_point_3_statistic,
                    delay: 0.6,
                  ),
                  const SizedBox(height: 8),
                  _buildProblemPoint(
                    context,
                    icon: Icons.refresh,
                    title: l10n.onboarding_problem_point_2_title,
                    description: l10n.onboarding_problem_point_2_description,
                    statistic: l10n.onboarding_problem_point_2_statistic,
                    delay: 0.8,
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Scientific proof badges with logos
              _buildScientificProofWithLogos(context),

              const SizedBox(height: 12),

              // Subtext
              Text(
                l10n.onboarding_problem_subtext,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                ),
                textAlign: TextAlign.center,
              ),

              SizedBox(height: size.height * 0.02),

              // Continue button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: widget.onContinue,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF4A90E2),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 18),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(28),
                    ),
                    elevation: 2,
                  ),
                  child: Text(
                    l10n.onboarding_problem_cta,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProblemPoint(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String description,
    required String statistic,
    required double delay,
  }) {
    final delayedAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Interval(
        delay,
        1.0,
        curve: Curves.easeOut,
      ),
    );

    return FadeTransition(
      opacity: delayedAnimation,
      child: SlideTransition(
        position: Tween<Offset>(
          begin: const Offset(0, 0.3),
          end: Offset.zero,
        ).animate(delayedAnimation),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: const Color(0xFFFFF5F5),
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
              color: const Color(0xFFFFE5E5),
              width: 1,
            ),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: const Color(0xFFF44336).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(
                  icon,
                  color: const Color(0xFFF44336),
                  size: 20,
                ),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF333333),
                        height: 1.2,
                      ),
                    ),
                    const SizedBox(height: 3),
                    Text(
                      description,
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.grey.shade700,
                        height: 1.3,
                      ),
                    ),
                    const SizedBox(height: 6),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
                      decoration: BoxDecoration(
                        color: const Color(0xFFF44336).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        statistic,
                        style: const TextStyle(
                          fontSize: 11,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFFF44336),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildScientificProofWithLogos(BuildContext context) {

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFE3F2FD),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF4A90E2).withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          const Row(
            children: [
              Icon(
                Icons.science,
                color: Color(0xFF4A90E2),
                size: 20,
              ),
              SizedBox(width: 8),
              Text(
                'Scientific Evidence',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF333333),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'Peer-reviewed studies confirm: excessive passive screen time causes brain changes, language delays, and attention problems in young children.',
            style: TextStyle(
              fontSize: 13,
              color: Colors.grey.shade700,
              height: 1.4,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          // Source logos row with actual logos
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildSourceLogo('assets/logos/aap-logo.png', 'American Academy of Pediatrics'),
              _buildSourceLogo('assets/logos/jama-logo.png', 'JAMA Pediatrics'),
              _buildSourceLogo('assets/logos/mdpi-logo.png', 'Brain Sciences Journal'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSourceLogo(String logoPath, String organizationName) {
    return Column(
      children: [
        Container(
          width: 60,
          height: 40,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Colors.grey.shade300,
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.05),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(7),
            child: Image.asset(
              logoPath,
              width: 58,
              height: 38,
              fit: BoxFit.contain,
              errorBuilder: (context, error, stackTrace) {
                // Fallback if logo fails to load
                return Center(
                  child: Icon(
                    Icons.image_not_supported,
                    size: 16,
                    color: Colors.grey.shade400,
                  ),
                );
              },
            ),
          ),
        ),
        const SizedBox(height: 4),
        SizedBox(
          width: 60,
          child: Text(
            organizationName,
            style: TextStyle(
              fontSize: 8,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}

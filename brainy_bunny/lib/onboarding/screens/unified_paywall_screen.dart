// lib/onboarding/screens/unified_paywall_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:brainy_bunny/constants/subscription_constants.dart';
import 'package:brainy_bunny/services/subscription_manager.dart';
import 'package:brainy_bunny/services/notification_manager.dart';
import 'package:brainy_bunny/ui/widgets/parent_gate_dialog.dart';

enum SubscriptionPlan { yearly, monthly, weekly }

/// Unified Paywall Screen
/// Single paywall component used across the app
class UnifiedPaywallScreen extends StatefulWidget {
  final VoidCallback? onSubscribed;
  final VoidCallback? onSkip;
  final VoidCallback? onBack;

  const UnifiedPaywallScreen({
    super.key,
    this.onSubscribed,
    this.onSkip,
    this.onBack,
  });

  @override
  State<UnifiedPaywallScreen> createState() => _UnifiedPaywallScreenState();
}

class _UnifiedPaywallScreenState extends State<UnifiedPaywallScreen> {
  final _subscriptionManager = SubscriptionManager();
  SubscriptionPlan _selectedPlan = SubscriptionPlan.yearly;
  bool _isLoading = false;
  String? _errorMessage;

  Future<void> _handleSubscribe() async {
    // Show parent gate first
    final verified = await showParentGate(context);
    if (!verified) {
      if (kDebugMode) {
        print('❌ Parent gate verification failed or cancelled');
      }
      return;
    }

    if (kDebugMode) {
      print('✅ Parent gate verified, proceeding with purchase');
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final productId = _getProductId();
      if (kDebugMode) {
        print('🛒 Attempting to purchase: $productId');
      }
      final success = await _subscriptionManager.purchaseSubscription(productId);

      if (success && mounted) {
        if (kDebugMode) {
          print('✅ Purchase successful!');
        }

        // Schedule trial notifications if yearly plan (with trial)
        if (_selectedPlan == SubscriptionPlan.yearly) {
          await NotificationManager.instance.scheduleTrialNotifications(
            yearlyPrice: _getYearlyPrice(),
          );
        }

        widget.onSubscribed?.call();
      } else if (mounted) {
        // Purchase failed but didn't throw an exception
        if (kDebugMode) {
          print('❌ Purchase failed (returned false)');
        }
        setState(() {
          _errorMessage = 'Purchase was cancelled or could not be completed. Please try again.';
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Purchase error: $e');
      }
      if (mounted) {
        setState(() {
          // Make error messages more user-friendly
          final errorText = e.toString().toLowerCase();
          if (errorText.contains('network') || errorText.contains('internet') || errorText.contains('connection')) {
            _errorMessage = 'No internet connection. Please check your connection and try again.';
          } else if (errorText.contains('product') || errorText.contains('not available') || errorText.contains('not found')) {
            _errorMessage = 'Subscriptions are temporarily unavailable. Please try again in a few moments.';
          } else if (errorText.contains('cancelled') || errorText.contains('canceled')) {
            _errorMessage = 'Purchase was cancelled.';
          } else if (errorText.contains('billing unavailable') || errorText.contains('billing service')) {
            _errorMessage = 'Billing service unavailable. Please check your device settings and try again.';
          } else {
            _errorMessage = 'Unable to complete purchase. Please try again or contact support.';
          }
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _handleRestorePurchases() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      await _subscriptionManager.restorePurchases();

      if (_subscriptionManager.hasActiveSubscription && mounted) {
        widget.onSubscribed?.call();
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('No previous purchases found'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          final errorText = e.toString().toLowerCase();
          if (errorText.contains('network') || errorText.contains('internet') || errorText.contains('connection')) {
            _errorMessage = 'No internet connection. Please check your connection and try again.';
          } else {
            _errorMessage = 'Failed to restore purchases. Please try again or contact support.';
          }
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  String _getProductId() {
    switch (_selectedPlan) {
      case SubscriptionPlan.yearly:
        return SubscriptionConstants.SUBSCRIPTION_YEARLY;
      case SubscriptionPlan.monthly:
        return SubscriptionConstants.SUBSCRIPTION_MONTHLY;
      case SubscriptionPlan.weekly:
        return SubscriptionConstants.SUBSCRIPTION_WEEKLY;
    }
  }

  String _getYearlyPrice() {
    return _subscriptionManager.yearlyProduct?.price ??
        SubscriptionConstants.FALLBACK_YEARLY_PRICE;
  }

  String _getMonthlyPrice() {
    return _subscriptionManager.monthlyProduct?.price ??
        SubscriptionConstants.FALLBACK_MONTHLY_PRICE;
  }

  String _getWeeklyPrice() {
    return _subscriptionManager.weeklyProduct?.price ??
        SubscriptionConstants.FALLBACK_WEEKLY_PRICE;
  }

  String _getMonthlyEquivalent() {
    final yearlyPrice = _getYearlyPrice();
    final numericValue = yearlyPrice.replaceAll(RegExp(r'[^\d.]'), '');
    final yearlyAmount = double.tryParse(numericValue) ?? 39.99;
    final monthlyEquivalent = (yearlyAmount / 12).toStringAsFixed(2);
    final currencySymbol = yearlyPrice.replaceAll(RegExp(r'[\d.]'), '').trim();
    return '$currencySymbol$monthlyEquivalent';
  }

  String _getWeeklyEquivalent() {
    final monthlyPrice = _getMonthlyPrice();
    final numericValue = monthlyPrice.replaceAll(RegExp(r'[^\d.]'), '');
    final monthlyAmount = double.tryParse(numericValue) ?? 8.99;
    final weeklyEquivalent = (monthlyAmount / 4.33).toStringAsFixed(2);
    final currencySymbol = monthlyPrice.replaceAll(RegExp(r'[\d.]'), '').trim();
    return '$currencySymbol$weeklyEquivalent';
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Stack(
          children: [
            // Main content - NO SCROLLING
            Padding(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Header row with back and skip
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      if (widget.onBack != null)
                        IconButton(
                          onPressed: widget.onBack,
                          icon: const Icon(Icons.arrow_back),
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(),
                        )
                      else
                        const SizedBox(width: 40),
                      if (widget.onSkip != null)
                        TextButton(
                          onPressed: widget.onSkip,
                          child: Text(
                            l10n.skip_button,
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Headline
                  Text(
                    l10n.unified_paywall_headline,
                    style: const TextStyle(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF2C3E50),
                    ),
                    textAlign: TextAlign.center,
                  ),

                  const SizedBox(height: 8),

                  // Subheadline
                  Text(
                    l10n.unified_paywall_subheadline,
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey.shade600,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  const SizedBox(height: 20),

                  // Plan cards
                  Expanded(
                    child: ListView(
                      physics: const NeverScrollableScrollPhysics(),
                      children: [
                        _PlanCard(
                          isSelected: _selectedPlan == SubscriptionPlan.yearly,
                          onTap: () => setState(() => _selectedPlan = SubscriptionPlan.yearly),
                          title: l10n.unified_paywall_yearly_title,
                          price: _getYearlyPrice(),
                          priceSubtext: l10n.unified_paywall_yearly_price_breakdown(
                            _getMonthlyEquivalent(),
                          ),
                          savingsBadge: l10n.unified_paywall_yearly_badge_save,
                          trialBadge: l10n.unified_paywall_yearly_badge_trial,
                          savings: l10n.unified_paywall_yearly_savings,
                          features: [
                            l10n.unified_paywall_yearly_feature_1,
                            l10n.unified_paywall_yearly_feature_2,
                            l10n.unified_paywall_yearly_feature_3,
                          ],
                        ),
                        const SizedBox(height: 12),
                        _PlanCard(
                          isSelected: _selectedPlan == SubscriptionPlan.monthly,
                          onTap: () => setState(() => _selectedPlan = SubscriptionPlan.monthly),
                          title: l10n.unified_paywall_monthly_title,
                          price: _getMonthlyPrice(),
                          priceSubtext: l10n.unified_paywall_monthly_per_week(
                            _getWeeklyEquivalent(),
                          ),
                          savings: l10n.unified_paywall_monthly_savings,
                          features: [
                            l10n.unified_paywall_monthly_feature_1,
                            l10n.unified_paywall_monthly_feature_2,
                          ],
                        ),
                        const SizedBox(height: 12),
                        _PlanCard(
                          isSelected: _selectedPlan == SubscriptionPlan.weekly,
                          onTap: () => setState(() => _selectedPlan = SubscriptionPlan.weekly),
                          title: l10n.unified_paywall_weekly_title,
                          price: _getWeeklyPrice(),
                          priceSubtext: null,
                          savings: l10n.unified_paywall_weekly_savings,
                          features: [
                            l10n.unified_paywall_weekly_feature_1,
                            l10n.unified_paywall_weekly_feature_2,
                          ],
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 16),

                  // CTA Button - changes based on selected plan
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _handleSubscribe,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: _selectedPlan == SubscriptionPlan.yearly
                            ? const Color(0xFF27AE60)
                            : const Color(0xFF4A90E2),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 18),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 0,
                      ),
                      child: Text(
                        _getButtonText(l10n),
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(height: 12),

                  // Error message (below button)
                  if (_errorMessage != null) ...[
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.red.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.red.shade200, width: 1.5),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.error_outline,
                            color: Colors.red.shade700,
                            size: 20,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              _errorMessage!,
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.red.shade900,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 12),
                  ],

                  // Trust elements
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      _TrustIcon(),
                      const SizedBox(width: 8),
                      Flexible(
                        child: Text(
                          l10n.unified_paywall_trust_1,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade600,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 8),

                  // Restore purchases link
                  Center(
                    child: TextButton(
                      onPressed: _isLoading ? null : _handleRestorePurchases,
                      child: Text(
                        l10n.unified_paywall_restore,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.blue.shade700,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(height: 4),

                  // Terms
                  Text(
                    l10n.unified_paywall_terms,
                    style: TextStyle(
                      fontSize: 10,
                      color: Colors.grey.shade600,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  const SizedBox(height: 8),
                ],
              ),
            ),

            // Loading overlay
            if (_isLoading)
              Container(
                color: Colors.black54,
                child: const Center(
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  String _getButtonText(AppLocalizations l10n) {
    switch (_selectedPlan) {
      case SubscriptionPlan.yearly:
        return l10n.unified_paywall_yearly_button;
      case SubscriptionPlan.monthly:
        return l10n.unified_paywall_monthly_button;
      case SubscriptionPlan.weekly:
        return l10n.unified_paywall_weekly_button;
    }
  }
}

class _PlanCard extends StatelessWidget {
  final bool isSelected;
  final VoidCallback onTap;
  final String title;
  final String price;
  final String? priceSubtext;
  final String? savingsBadge;
  final String? trialBadge;
  final String savings;
  final List<String> features;

  const _PlanCard({
    required this.isSelected,
    required this.onTap,
    required this.title,
    required this.price,
    this.priceSubtext,
    this.savingsBadge,
    this.trialBadge,
    required this.savings,
    required this.features,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected
              ? const Color(0xFF4A90E2).withOpacity(0.1)
              : Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected
                ? const Color(0xFF4A90E2)
                : Colors.grey.shade300,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Badges row
            if (savingsBadge != null || trialBadge != null) ...[
              Row(
                children: [
                  if (savingsBadge != null) ...[
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: const Color(0xFFF39C12),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            savingsBadge!,
                            style: const TextStyle(
                              fontSize: 11,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(width: 4),
                          const Text(
                            '🎉',
                            style: TextStyle(fontSize: 11),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 8),
                  ],
                  if (trialBadge != null)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: const Color(0xFF27AE60),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        trialBadge!,
                        style: const TextStyle(
                          fontSize: 11,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 12),
            ],

            // Title and price row
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade900,
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      price,
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: isSelected
                            ? const Color(0xFF4A90E2)
                            : Colors.grey.shade900,
                      ),
                    ),
                    if (priceSubtext != null)
                      Text(
                        priceSubtext!,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                  ],
                ),
              ],
            ),

            const SizedBox(height: 8),

            // Savings text
            Text(
              savings,
              style: TextStyle(
                fontSize: 13,
                color: Colors.grey.shade700,
                fontWeight: FontWeight.w500,
              ),
            ),

            // Features (only show when selected)
            if (isSelected) ...[
              const SizedBox(height: 12),
              ...features.take(3).map((feature) => Padding(
                padding: const EdgeInsets.only(bottom: 6),
                child: Row(
                  children: [
                    const Icon(
                      Icons.check_circle,
                      color: Color(0xFF27AE60),
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        feature,
                        style: TextStyle(
                          fontSize: 13,
                          color: Colors.grey.shade700,
                        ),
                      ),
                    ),
                  ],
                ),
              )),
            ],

            // Radio button
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isSelected
                          ? const Color(0xFF4A90E2)
                          : Colors.grey.shade400,
                      width: 2,
                    ),
                  ),
                  child: isSelected
                      ? Center(
                          child: Container(
                            width: 10,
                            height: 10,
                            decoration: const BoxDecoration(
                              shape: BoxShape.circle,
                              color: Color(0xFF4A90E2),
                            ),
                          ),
                        )
                      : null,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class _TrustIcon extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Icon(
      Icons.check_circle_outline,
      color: const Color(0xFF27AE60),
      size: 16,
    );
  }
}

// lib/main.dart
import 'package:firebase_core/firebase_core.dart';
import 'package:flame/flame.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:brainy_bunny/app.dart';
import 'package:brainy_bunny/firebase_options.dart';
import 'package:brainy_bunny/services/auth_service.dart';
import 'package:brainy_bunny/services/purchase_manager.dart';
import 'package:brainy_bunny/constants/app_constants.dart';
import 'package:brainy_bunny/models/onboarding_progress.dart';
import 'package:brainy_bunny/models/onboarding_state.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    if (kDebugMode) {
      print('🚀 Starting Brainy Bunny app...');
    }

    // Reset onboarding if configured (for testing)
    if (AppConstants.RESET_ONBOARDING_ON_RESTART) {
      if (kDebugMode) {
        print('🔄 Resetting onboarding progress (RESET_ONBOARDING_ON_RESTART = true)');
      }
      await OnboardingProgress.clear();
      await OnboardingState.clear();
    }

    // Only do essential setup in main() - move heavy initialization to splash screen
    // Configure device orientation - start with portrait for splash/onboarding
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
    ]);

    // Set up Flame for fullscreen but allow orientation changes
    Flame.device.fullScreen();

    if (kDebugMode) {
      print('✅ Essential setup complete, launching app...');
    }

    // Launch app immediately - initialization will happen in splash screen
    runApp(const BrainyBunnyApp());

  } catch (e, stackTrace) {
    if (kDebugMode) {
      print('❌ Critical error during app initialization: $e');
      print('Stack trace: $stackTrace');
    }

    // Try to run app anyway with minimal setup
    runApp(
      MaterialApp(
        home: Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error, size: 64, color: Colors.red),
                const SizedBox(height: 16),
                const Text(
                  'App initialization failed',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                Text(
                  'Error: $e',
                  style: const TextStyle(fontSize: 14),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    // Restart the app
                    main();
                  },
                  child: const Text('Retry'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
// lib/onboarding/screens/educational_philosophy_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:brainy_bunny/constants/onboarding_constants.dart';

/// Educational value screen - shows what problem the app solves for parents
class EducationalPhilosophyScreen extends StatefulWidget {
  final VoidCallback onContinue;

  const EducationalPhilosophyScreen({
    super.key,
    required this.onContinue,
  });

  @override
  State<EducationalPhilosophyScreen> createState() =>
      _EducationalPhilosophyScreenState();
}

class _EducationalPhilosophyScreenState
    extends State<EducationalPhilosophyScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onManualContinue() {
    widget.onContinue();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final size = MediaQuery.of(context).size;

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(height: size.height * 0.04),

              // Icon
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: const Color(0xFFE8F4F8),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.lightbulb,
                  size: 40,
                  color: const Color(0xFFF39C12),
                ),
              ),

              const SizedBox(height: 24),

              // Headline
              Text(
                l10n.educational_value_headline,
                style: const TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2C3E50),
                  height: 1.3,
                ),
                textAlign: TextAlign.center,
              ),

              SizedBox(height: size.height * 0.04),

              // 4 Educational value points with icons (per publisher guidelines)
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _AnimatedBulletPoint(
                        animation: _animationController,
                        delay: 0.0,
                        icon: Icons.psychology, // 🧠 Brain
                        iconColor: const Color(0xFF4A90E2),
                        title: l10n.educational_value_point_1_title,
                        description: l10n.educational_value_point_1_description,
                      ),
                      const SizedBox(height: 20),
                      _AnimatedBulletPoint(
                        animation: _animationController,
                        delay: 0.2,
                        icon: Icons.visibility, // 👁️ Eye
                        iconColor: const Color(0xFF27AE60),
                        title: l10n.educational_value_point_2_title,
                        description: l10n.educational_value_point_2_description,
                      ),
                      const SizedBox(height: 20),
                      _AnimatedBulletPoint(
                        animation: _animationController,
                        delay: 0.4,
                        icon: Icons.center_focus_strong, // 🎯 Target
                        iconColor: const Color(0xFFF39C12),
                        title: l10n.educational_value_point_3_title,
                        description: l10n.educational_value_point_3_description,
                      ),
                      const SizedBox(height: 20),
                      _AnimatedBulletPoint(
                        animation: _animationController,
                        delay: 0.6,
                        icon: Icons.access_time, // ⏱️ Clock
                        iconColor: const Color(0xFF9B59B6),
                        title: l10n.educational_value_point_4_title,
                        description: l10n.educational_value_point_4_description,
                      ),
                    ],
                  ),
                ),
              ),

              SizedBox(height: size.height * 0.03),

              // Research backing
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      const Color(0xFF4A90E2).withOpacity(0.1),
                      const Color(0xFF4A90E2).withOpacity(0.05),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: const Color(0xFF4A90E2).withOpacity(0.3),
                    width: 1.5,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.science,
                      color: const Color(0xFF4A90E2),
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            l10n.educational_value_research,
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey.shade800,
                              fontWeight: FontWeight.w500,
                              height: 1.4,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            l10n.educational_value_research_source,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade600,
                              fontStyle: FontStyle.italic,
                              height: 1.3,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 24),

              // Continue button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _onManualContinue,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF4A90E2),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 18),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(16),
                    ),
                    elevation: 2,
                  ),
                  child: Text(
                    l10n.continue_button,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _AnimatedBulletPoint extends StatelessWidget {
  final Animation<double> animation;
  final double delay;
  final IconData icon;
  final Color iconColor;
  final String title;
  final String description;

  const _AnimatedBulletPoint({
    required this.animation,
    required this.delay,
    required this.icon,
    required this.iconColor,
    required this.title,
    required this.description,
  });

  @override
  Widget build(BuildContext context) {
    final delayedAnimation = CurvedAnimation(
      parent: animation,
      curve: Interval(
        delay,
        1.0,
        curve: Curves.easeOut,
      ),
    );

    return FadeTransition(
      opacity: delayedAnimation,
      child: SlideTransition(
        position: Tween<Offset>(
          begin: const Offset(0, 0.3),
          end: Offset.zero,
        ).animate(delayedAnimation),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: iconColor.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                size: 24,
                color: iconColor,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade900,
                      height: 1.3,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade700,
                      height: 1.4,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

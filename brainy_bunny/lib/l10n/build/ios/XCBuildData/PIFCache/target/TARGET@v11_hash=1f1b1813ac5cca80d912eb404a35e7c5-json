{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980aec93d2c77fb06b70f1d3b03bc9b349", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988739fe12e2029ec33474d51e1c1de486", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d1228bff0948e0574787579a60041bad", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e7e194b3c7a0dcadb37a08a8959714bb", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d1228bff0948e0574787579a60041bad", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ad06093d4c8fed0ad01765d765863350", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984df139248ef99479e353b1b47cb48071", "guid": "bfdfe7dc352907fc980b868725387e982f7568700678161c9dd366d7739bf413", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3fbb1ef5b193d4348dc4b4394d21214", "guid": "bfdfe7dc352907fc980b868725387e98537c0b86fe75d06dc03ef2ed1f351580", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b51ace17c91241001705e25d988c9e70", "guid": "bfdfe7dc352907fc980b868725387e981a0138dd142905152035ab5fa9d5fb8d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805135be366efe0f24797d5cb57dc3ce6", "guid": "bfdfe7dc352907fc980b868725387e98ba2f6057e36305f1538b68115d0ff177", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ae14cd87c927d988416e644f710329e", "guid": "bfdfe7dc352907fc980b868725387e9890ea7615c078ae10c474ea8d1fc8f4f4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804364bc230e3611747f2e68033454387", "guid": "bfdfe7dc352907fc980b868725387e9815e750895d256c42c89eeeb7e7b855e3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836f0028d633c0e2058a0c32ce0588a39", "guid": "bfdfe7dc352907fc980b868725387e9899ef9f45809e0eabf011c142b7ef1593", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856bc870f9ebceedf53c01173ed1266c0", "guid": "bfdfe7dc352907fc980b868725387e98f0fb28e9c9e73625ef2e357cc9fe7f79", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810dcbf97e2b40915d05a0ac34e7384d6", "guid": "bfdfe7dc352907fc980b868725387e98d4bf1e54e6fb09fb80b9e807a95b9e98", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865ad8c5c6ff0ef8f86d3c7d6784eeeab", "guid": "bfdfe7dc352907fc980b868725387e9828fc94f2fe5915a50c96e6bbce3eaa86", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982444ece3a090046a8f75f19f81594032", "guid": "bfdfe7dc352907fc980b868725387e986f51ab3881a00b5da2cc65494d3218f8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2c2a72a28184f7f21d78c7e846bed3d", "guid": "bfdfe7dc352907fc980b868725387e9815108d278cb7fa7b708e5b7eb87ace94", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987277f856b4380c634107288b46b9f509", "guid": "bfdfe7dc352907fc980b868725387e98315ab11579f30593c475e4116714f775", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98616481da8a4032dfc8a86cde1f331695", "guid": "bfdfe7dc352907fc980b868725387e98c7e130d936f8a20ed293c66c9f066f75", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98527db3c40639cc6759f0e8015001fafc", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ef782e44f7ee87e3e2439b4ec1aa1a74", "guid": "bfdfe7dc352907fc980b868725387e98475fc8e8b3ec4b7dd01534402dce2e25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ee085e74d0180e882e99dfbf7898307", "guid": "bfdfe7dc352907fc980b868725387e98fdfd773427ab9120894b907e96283c20"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e3ddb8ee72bd58ad6291a47d2be75ca", "guid": "bfdfe7dc352907fc980b868725387e98dc4a83d2d8f97d0a0adf8478760dfd28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bdc144c9500506972acc354e9453b6c0", "guid": "bfdfe7dc352907fc980b868725387e98c30ec1114984a469f080b6bf83fdb330"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98505d3b239a27de5fc355e6fdb30d7d11", "guid": "bfdfe7dc352907fc980b868725387e987e124e98910c8d36b65f3ad3665033c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cbe69dd13e7697a93f203b85ea06dae", "guid": "bfdfe7dc352907fc980b868725387e985da08f13617ab266e6b0f64f913b7c13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98798ef44b0a5530979dc437012bf82ac4", "guid": "bfdfe7dc352907fc980b868725387e98655524df099c35646567c4784e8484b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98818e94f90caf8e2b5e595f0a6c3e9e2b", "guid": "bfdfe7dc352907fc980b868725387e98bd1e41377380ca92c1e9ae4dd63e291b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984073415d391dbb9d12d846d950ddeddd", "guid": "bfdfe7dc352907fc980b868725387e98866f64ec983282d8d5f688994eebb856"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825fe0e68494aed309de28c15499484a4", "guid": "bfdfe7dc352907fc980b868725387e983c5eef7b41d2ad394ab4ab70534c5577"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a254ad91ca4d4758f1fb36cb51bca5a", "guid": "bfdfe7dc352907fc980b868725387e984a69b6f8aaf850cb1c08b2f82a0e8e30"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986afe178b64e768df9101f032d9d69812", "guid": "bfdfe7dc352907fc980b868725387e982244307c3fc949e9b475f0ad63b78a8a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b2cd2e8f96e3cd2f57e4d0614009ec8", "guid": "bfdfe7dc352907fc980b868725387e981ad1be39793d142a35e7bbe3dce444e8"}], "guid": "bfdfe7dc352907fc980b868725387e98445c13b134f6717c552fb845dd07cf6c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986d33a47f1e1031e7e6a671aab8d386e4", "guid": "bfdfe7dc352907fc980b868725387e987208fa6207d938fe537d0e8359bf5699"}], "guid": "bfdfe7dc352907fc980b868725387e98ee8cf58f2eaf1185d54859fdaf1a4231", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98e1b65cad46427b34676adbcb934284e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d57b8bce60a0f11113f4cff532db68d3", "name": "Firebase"}, {"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987f74324bfc5c78140e34d510e26e00c1", "name": "firebase_core"}], "guid": "bfdfe7dc352907fc980b868725387e989840e8244cb75f43b3efe8cd6dec5ec5", "name": "cloud_firestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98321793542cff8793cba84baa893d5044", "name": "cloud_firestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
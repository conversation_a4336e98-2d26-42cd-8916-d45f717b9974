{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fc3bde7eda64f4ed2e5c4e746ea13aee", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f585bdbc9f766f5440da92d33d240f16", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a0ab7c98efbf4dc7ed7126a81d0b1692", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988acb5fa924c86b456f5ae9f1fbc1f4f2", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a0ab7c98efbf4dc7ed7126a81d0b1692", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9814578839185d82f5b7666e5d1b13abe2", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986e1f2a4f4fb174347ecd9ebe45a6b500", "guid": "bfdfe7dc352907fc980b868725387e98d4b306143639dfb0e13a81dec383ca6f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aaea902c5a9226c934eb373137bcc071", "guid": "bfdfe7dc352907fc980b868725387e9849a15f83ae0f3e4c169b82c53261de6a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986535622cbed69537c6a7777558a73049", "guid": "bfdfe7dc352907fc980b868725387e98075f3e60278f84e1dbfcbae23cd83f58", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b39b89d4009c7aa5873f006daa981546", "guid": "bfdfe7dc352907fc980b868725387e98792546cbcef4cb12e95cf14ec3b5ff09", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98577dbf06fa2e09186861336331f44f88", "guid": "bfdfe7dc352907fc980b868725387e984a3b0f2994df4d53b23771925b69675a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823785e7098fa60b8834ed76964cf976a", "guid": "bfdfe7dc352907fc980b868725387e986c8a26c5a99d31463d4e23a8817b3054", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c8e4672530a0fc8b15d85fa773e4c90", "guid": "bfdfe7dc352907fc980b868725387e982dfa2ea5cddfae05cf137daf5e4b77a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981539af90558830cf41abd6cccb5ab7be", "guid": "bfdfe7dc352907fc980b868725387e98e70182935c38cdd5446c85399fbeb30f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3815b7ae4b191137c83d87d57455eda", "guid": "bfdfe7dc352907fc980b868725387e98fd8b4edb222b8fb9f18148e2ecb3c9f9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802f2ef03bac7721a4358c5973fef487e", "guid": "bfdfe7dc352907fc980b868725387e981ac7f32ba53f527edc85a1a74bd90027", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a4e3863bfa51cdafbe9ddd4cace8ba8", "guid": "bfdfe7dc352907fc980b868725387e98d6f65ddf6edb9327f87190697cf6b0d6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871ebc80457c14d988502a976860dbcae", "guid": "bfdfe7dc352907fc980b868725387e9842223644b45e4e8988ffd4e180d66af4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800013ebea7e2df8cbdbf1cef2e230539", "guid": "bfdfe7dc352907fc980b868725387e9879d614baa9a8f26c93980d86f75696b9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98142ab0881059f7c0448a3570fb5ab2f5", "guid": "bfdfe7dc352907fc980b868725387e9868ab9865aa3af4896f945132905340d2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98965d11bd1ebb2cb2a275bbd7568be30a", "guid": "bfdfe7dc352907fc980b868725387e98dead5c5af0a4f31f12b77dee993eab09", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3e108be75cb698a6a8aedc0a613917f", "guid": "bfdfe7dc352907fc980b868725387e9826f03fec754872f9a44f2b5da3fa16c2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cacd777b1d7a756f3e1f7c3ff62f4e8d", "guid": "bfdfe7dc352907fc980b868725387e9842095effcd36103f23ff984731d9b81d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f35430cc610e3056727910f2a650e053", "guid": "bfdfe7dc352907fc980b868725387e982b849c2fcbdc92bb54cea9bb3a0e6a34", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98241c2c8272fac8ba084e5917aa8a0612", "guid": "bfdfe7dc352907fc980b868725387e9858c7bd362508ac1f4dd184a2dd073c94", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afc7111d2b794859889522e034694ff9", "guid": "bfdfe7dc352907fc980b868725387e9868c9d7f644912c0c460756312bea337c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865c51351100e3257a1f779870284b3bc", "guid": "bfdfe7dc352907fc980b868725387e98c93c82c3e562c8b9ce880b5314a7b4e7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982fac0ce8763e20ace3d4e3eafd95fcea", "guid": "bfdfe7dc352907fc980b868725387e98f877f92098d713542376d8ef78e3d0b6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f49d288c4f299322050821ecc37b17a", "guid": "bfdfe7dc352907fc980b868725387e985c225dea43d33b82874895b56f59e339", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986621f6c6be02ceaf6f188258de0dc84a", "guid": "bfdfe7dc352907fc980b868725387e9887d10e6c3bc256c6940a41369b0d334c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825c2d30308bc1cefe1e9e2497cc58699", "guid": "bfdfe7dc352907fc980b868725387e9854c4a98f25135923eb2b09862c7ac77f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862543fa311fb64a71b62197b873f9dd4", "guid": "bfdfe7dc352907fc980b868725387e98c443b9dde9e00156bf550ca89435ecee", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98caaa1cef9bee4c887098757ee6f98ba3", "guid": "bfdfe7dc352907fc980b868725387e986526005c1c5ef0471f6989424f5bcffd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a454506ec41edfdbf22429ad10151fc", "guid": "bfdfe7dc352907fc980b868725387e9892f5ebc2e2f66b040d10d7382a61129c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984054a0f9ff2ed9ef375b084fbb4d4b12", "guid": "bfdfe7dc352907fc980b868725387e9822b427fe0b280e6987e91dd10e34a9b0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3ceef4925ec00231d06f36c48e4f168", "guid": "bfdfe7dc352907fc980b868725387e98d39239b3fd9459a62c955c8063327d72", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e985a8f9b8f5621df0779e1dcad888c397b", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980cdcc276c71dba410153cb56f884aa70", "guid": "bfdfe7dc352907fc980b868725387e983819ca7489b022071720abd385c4f5b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987451dfa49d352e23defc541cf5b0244b", "guid": "bfdfe7dc352907fc980b868725387e98329ba8654f997831e7e6618516e4b05d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a8cbdc289aadd073fb70f6e6bd61979", "guid": "bfdfe7dc352907fc980b868725387e986f903dfa469d854a9d94fe4eeedf7576"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816422f89d23ab719979b7345c5bbe0df", "guid": "bfdfe7dc352907fc980b868725387e98758f14c66421fe3daf20607d71b6e520"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867aa4eb7e5728b20bbd50921cd5b194d", "guid": "bfdfe7dc352907fc980b868725387e983f5e260be518e79de0804a1371cde159"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888b9a5a2c3b6f0c8a98ddd9d04bd82fc", "guid": "bfdfe7dc352907fc980b868725387e988141ae01f610b730338c09c1e954345b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4d7fb967638a260f1712227fd09b376", "guid": "bfdfe7dc352907fc980b868725387e9804a206027f1262ed9a105e914d4ca296"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cfd0713b681e48f613694f03ce1a2b2", "guid": "bfdfe7dc352907fc980b868725387e98351303600b2409177750e0682aa061d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840714280e89c49520e32c61762b2dcf8", "guid": "bfdfe7dc352907fc980b868725387e98943730fc3bff37ea1ceaf7ab23c71871"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984bd497e6eec545655427ada66e416b25", "guid": "bfdfe7dc352907fc980b868725387e9831274f8b6acc5205090bd8a075ad098f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98467ad1f90320303f113474b48d4b0356", "guid": "bfdfe7dc352907fc980b868725387e98857bf1638471abca7124d94c86a51315"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894f7fb3423447bed883cfc58d79ba98d", "guid": "bfdfe7dc352907fc980b868725387e98e966707e566cfa7e7582ef82d7ed9938"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982550862b2314a06ce5ce952a9560ca37", "guid": "bfdfe7dc352907fc980b868725387e98cf4fd5cce00530622e5215724e500a81"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c6a21f113d043069c4b48b0b07a7499", "guid": "bfdfe7dc352907fc980b868725387e982b87804c64728da69cde58a2e497f040"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d70242801eee80ef66cb9d46dced75a", "guid": "bfdfe7dc352907fc980b868725387e9840c156ba7b5e865cee97f05b7b95c39a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e275642cd51b4ae21b5c3bf655c2e9d0", "guid": "bfdfe7dc352907fc980b868725387e98b67e705194b95faa6d4d8611f6dfdfd3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e84c0df968102bb6882ad62a5f8df0b1", "guid": "bfdfe7dc352907fc980b868725387e986d0491cbde19b08b7dc0254dc23bd79c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98247eb332dcf0cb5ed6a31665f41a25c2", "guid": "bfdfe7dc352907fc980b868725387e983462bb12c123c7fc1830758b568a1a16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6ace72644824ea04385e5a8c636a624", "guid": "bfdfe7dc352907fc980b868725387e9879362f381cb3ed53502813e10d7cf2f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98166c33707a8e346eb3e211db70f76cc0", "guid": "bfdfe7dc352907fc980b868725387e980bc0bd2eacb20fa8efcf64b5315f4d44"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e0431c6ec06cb92bde4f974e650fa05", "guid": "bfdfe7dc352907fc980b868725387e981734c295a44bbd1ba04dd0a9c057e137"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfd46dfa936214846c06e1fd508d7e6e", "guid": "bfdfe7dc352907fc980b868725387e9889633739f9b425a8d9ca551761d43c5e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa99f83b71bd509b1c7625f837e09c06", "guid": "bfdfe7dc352907fc980b868725387e9854b3a96f1f0bddce06d6dbc8e9deaf1a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9807d5991fdd853128d0a53c952c1162a6", "guid": "bfdfe7dc352907fc980b868725387e9816c0c3699969accc011a8fb3297358e8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cf8926ee955c05518a0c17616de40c3", "guid": "bfdfe7dc352907fc980b868725387e989d38b1bef4bdc3f1f64dd9f82c7f9d7f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5dcf0b1dbeef2110b0819e2e05d1399", "guid": "bfdfe7dc352907fc980b868725387e98b364af5e453a81cbc08961acb2237bea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989723143e531dfb8e2d29b078a4cc407c", "guid": "bfdfe7dc352907fc980b868725387e98e5788b007364278100b0ad54c9a1d1fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f01e03a9d5f3354da68c213d4029a73", "guid": "bfdfe7dc352907fc980b868725387e9891576b4c248155fdf7fb79f0eeca0320"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849fad5b8ae4603a51b51effcec041132", "guid": "bfdfe7dc352907fc980b868725387e98415a7a7859ceb6df8bbcb5d932297c57"}], "guid": "bfdfe7dc352907fc980b868725387e98eb427bf47bfed1296ac9263fdc072dfc", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986d33a47f1e1031e7e6a671aab8d386e4", "guid": "bfdfe7dc352907fc980b868725387e98aae7f90b45d94c7fbb470b8d59be7eab"}], "guid": "bfdfe7dc352907fc980b868725387e98d6308ae85427f9c6765b2c8725e21ac6", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c522dafc5cf08117d2a82b84e1a2ef8e", "targetReference": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340"}], "guid": "bfdfe7dc352907fc980b868725387e9870820a2d73cf9a33065463acb9373ee1", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340", "name": "FirebaseFirestore-FirebaseFirestore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98919212c22943df12241906dd601cdff4", "name": "FirebaseFirestoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift"}], "guid": "bfdfe7dc352907fc980b868725387e98c075cc473fa5680b867d51f1363214ff", "name": "FirebaseFirestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9809dfd848e2259e061e90089e1647f5b7", "name": "FirebaseFirestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
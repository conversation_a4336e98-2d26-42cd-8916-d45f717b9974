{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b10100d0ab1d8542940414bbe2687d28", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PurchasesHybridCommon/PurchasesHybridCommon-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PurchasesHybridCommon/PurchasesHybridCommon-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PurchasesHybridCommon/PurchasesHybridCommon.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "PurchasesHybridCommon", "PRODUCT_NAME": "PurchasesHybridCommon", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.7", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98513962c357e16a82e10b0a6ee61deea9", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d50964076750a8aa53fb0b3de3e1324f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PurchasesHybridCommon/PurchasesHybridCommon-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PurchasesHybridCommon/PurchasesHybridCommon-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PurchasesHybridCommon/PurchasesHybridCommon.modulemap", "PRODUCT_MODULE_NAME": "PurchasesHybridCommon", "PRODUCT_NAME": "PurchasesHybridCommon", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.7", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98fbcd672d041b729f79fe121be7c87421", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d50964076750a8aa53fb0b3de3e1324f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PurchasesHybridCommon/PurchasesHybridCommon-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PurchasesHybridCommon/PurchasesHybridCommon-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PurchasesHybridCommon/PurchasesHybridCommon.modulemap", "PRODUCT_MODULE_NAME": "PurchasesHybridCommon", "PRODUCT_NAME": "PurchasesHybridCommon", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.7", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984c88e64c9c082fe3b5de63fbb3479152", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981f401947af7ea65131a0efbf991bb4cd", "guid": "bfdfe7dc352907fc980b868725387e98ffd4573f063b5854a1de73c29bf00046", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f2fe6f51b6d3c8f041bc399989175e5", "guid": "bfdfe7dc352907fc980b868725387e98eaa4912fbeed008e78bd695704638e61", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9861bcb052e813f9db48d197baf38c7136", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e86f7931959d954cdb9818c94a300a51", "guid": "bfdfe7dc352907fc980b868725387e98d4e8d2474a182b247af1ffe06426d4d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9b5ebb7a9c42b7bc131d1d939d67241", "guid": "bfdfe7dc352907fc980b868725387e9809c938754d3bfcf3b16fe1717a4ca441"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98902abbc6ba0067193c470fb9a41896a5", "guid": "bfdfe7dc352907fc980b868725387e984cdba8c82efb4485d7ad725238f31379"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a0d9d04771250cc54fd6a201e179b64", "guid": "bfdfe7dc352907fc980b868725387e98930dba74da5f31951364603e33533e1e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810a9e6c6e508c54081d465a251eca352", "guid": "bfdfe7dc352907fc980b868725387e9867ef49f8d6b2d0a0440b6d64ad09c7b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7c51db430348bad036389a408312708", "guid": "bfdfe7dc352907fc980b868725387e98a677576b579cb37ecc0b9bbd1a537e93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842045e80e34ff383b98e0c13da0eeea9", "guid": "bfdfe7dc352907fc980b868725387e982aaefe9c4f66e54ca15e2b7ddc5c080a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800032c7a472336b014cc963220a894dc", "guid": "bfdfe7dc352907fc980b868725387e986f6b5fe267ae6d3e539b1403553b61c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d0fe64729858d778bdaf2c7ea4b46e2", "guid": "bfdfe7dc352907fc980b868725387e98ccffe4eab86860b4a3478ca27dc2729b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873850bb9754d39ea37accab0a9e11e51", "guid": "bfdfe7dc352907fc980b868725387e98a570e78c279e57b5e74450e68db2240b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cbc5f1385104d2787ed5c2cfac248205", "guid": "bfdfe7dc352907fc980b868725387e984e32f1f265283e7063de9b8c141d7d65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef5efdb5be174270f7e974fc40920428", "guid": "bfdfe7dc352907fc980b868725387e9861b95e83480edb912d31e1f525b0398f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98017e874a96ae6b7e700a8072e81001c7", "guid": "bfdfe7dc352907fc980b868725387e9805b8e03968fd950d94a5196001e19ec2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c8bbff97d3334a43dbffb0cae025359e", "guid": "bfdfe7dc352907fc980b868725387e9839a85be6425f3912d3801ef0acd4a126"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982974d5e853c25c265256435b89101807", "guid": "bfdfe7dc352907fc980b868725387e988afd107e6f393c6c35fe7e15088acee5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a2fbe1c6e76406b45379246d5ff4890", "guid": "bfdfe7dc352907fc980b868725387e9882ccc93baf891f76610144a32de8cfb6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98acc9ad717be79cc1cc4f8921b09f7a17", "guid": "bfdfe7dc352907fc980b868725387e9829f4029e0598e2f92a7bc4864b5eeae1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce598e221bc6fc640964dbbc87952dc6", "guid": "bfdfe7dc352907fc980b868725387e985ea01f19cc4b85a8cd505d59a4de780e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885297a9d15586f0163ccbbaeea1fb487", "guid": "bfdfe7dc352907fc980b868725387e98b141088a875faaf5c298e5b6c87db3ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b92be674dd7d07f7acd9ca290db09bd4", "guid": "bfdfe7dc352907fc980b868725387e987fcd33c01f4b205e56f066389113ce9f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa5670cd23301d932ecd16b786123044", "guid": "bfdfe7dc352907fc980b868725387e988fb58a907ef503f4c888abde1e507934"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e74ef4b0dd1cc93cee84b56e5467449f", "guid": "bfdfe7dc352907fc980b868725387e987072cc07c1e44a627a719e28613e92f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e9d05102a98b7e456ae4d45b0ed30cc", "guid": "bfdfe7dc352907fc980b868725387e9835312f576f37edf9f33e3fbe405bdb2b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9487a12b3ac0f78848ecf7450903018", "guid": "bfdfe7dc352907fc980b868725387e98aaf4af257f7ab2d532a468468e2b2d9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9850deb6864fbcc188d91acb21038d38ec", "guid": "bfdfe7dc352907fc980b868725387e9815a78b13229eea3567fffaa048d83ece"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866a620f9772bf2db8b69f370fb7b1131", "guid": "bfdfe7dc352907fc980b868725387e9824a5e0f42bdb47024b8223babefd321b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc06fd5bdaaf61ef7a3c01138f6c9155", "guid": "bfdfe7dc352907fc980b868725387e9827b7f4673c91f0e63994ff948a9b0e29"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985da79353cf94d90bbf16ee8e403346e3", "guid": "bfdfe7dc352907fc980b868725387e988fb4612a47396d069c1416791fc25eb5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986cda90dbfc1ad5f34bc00cb86dc643a5", "guid": "bfdfe7dc352907fc980b868725387e98171c79bd43d22b0b01f1a9f6ed8a36df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981644d2c7cf0268e42ead290109ebfdbe", "guid": "bfdfe7dc352907fc980b868725387e9893dd4b927734817bbc2b4e74653f510a"}], "guid": "bfdfe7dc352907fc980b868725387e98018de7881a121675a628208368eb21d6", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986d33a47f1e1031e7e6a671aab8d386e4", "guid": "bfdfe7dc352907fc980b868725387e984a0869429b8b98aae14845a2f403544f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c931eefef975be5ab365c20d363472d", "guid": "bfdfe7dc352907fc980b868725387e98729b82c528ec380a84728982cdeacf27"}], "guid": "bfdfe7dc352907fc980b868725387e9824be19a8fc96e866601df09916d7424e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9803acf001720b1ac7cb78eabbeb397d1e", "targetReference": "bfdfe7dc352907fc980b868725387e9821a12af8510f5a5a4842f587aedc172a"}], "guid": "bfdfe7dc352907fc980b868725387e987ef82eeb8d1fffcd891a8a2a4afc81fd", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9821a12af8510f5a5a4842f587aedc172a", "name": "PurchasesHybridCommon-PurchasesHybridCommon"}, {"guid": "bfdfe7dc352907fc980b868725387e9833260f06b6ffe5cdf6831b2907a4b8de", "name": "RevenueCat"}], "guid": "bfdfe7dc352907fc980b868725387e98b0f638c99ac593829a518d6a6a45d8d0", "name": "PurchasesHybridCommon", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988760891fc732fcd8db57a04e95d983d2", "name": "PurchasesHybridCommon.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
// lib/ui/widgets/settings_button_overlay.dart
import 'package:flutter/material.dart';
import '../screens/subscription_management_screen.dart';

/// Floating settings button overlay that appears on the home screen
class SettingsButtonOverlay extends StatelessWidget {
  const SettingsButtonOverlay({super.key});

  void _openSettings(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const SubscriptionManagementScreen(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: 16,
      right: 16,
      child: SafeArea(
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: () => _openSettings(context),
            borderRadius: BorderRadius.circular(24),
            child: Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.9),
                borderRadius: BorderRadius.circular(24),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.2),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: const Icon(
                Icons.settings,
                color: Color(0xFF4A90E2),
                size: 28,
              ),
            ),
          ),
        ),
      ),
    );
  }
}

// lib/onboarding/games/demo_drag_game.dart
import 'dart:math';
import 'package:flame/components.dart';
import 'package:flame/effects.dart';
import 'package:flame/game.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:brainy_bunny/game/components/confetti_component.dart';
import 'package:brainy_bunny/game/components/drag_target_item.dart';
import 'package:brainy_bunny/game/components/draggable_item.dart';
import 'package:brainy_bunny/game/games/drag_game.dart';
import 'package:brainy_bunny/services/audio_service.dart';

/// Simplified drag game for onboarding demo
/// Shows only 3 matches instead of full rounds
class DemoDragGame extends DragGame {
  final int matchLimit;
  final VoidCallback? onMatchCompleted;
  final VoidCallback? onGameCompleted;

  int _matchedCount = 0;
  SpriteComponent? _background;
  bool _isGameComplete = false;
  bool _isMounted = false;
  Sprite? _backgroundSprite;

  DemoDragGame({
    required String gameName,
    this.matchLimit = 5,
    this.onMatchCompleted,
    this.onGameCompleted,
  }) : super(gameName: gameName);

  @override
  Future<void> onLoad() async {
    await super.onLoad();

    _matchedCount = 0;
    _isGameComplete = false;
    _isMounted = false;

    if (kDebugMode) {
      print("Loading demo game for $gameName with $matchLimit matches");
    }

    try {
      _backgroundSprite = await loadSprite('$gameName/background.jpg');
    } catch (e) {
      if (kDebugMode) {
        print("Error loading background sprite: $e");
      }
    }

    // Play game music
    AudioService.instance.playGameMusic();
    _isMounted = true;
  }

  @override
  void onGameResize(Vector2 gameSize) {
    super.onGameResize(gameSize);

    if (_isMounted && gameSize.x > 0 && gameSize.y > 0) {
      if (_background == null && _backgroundSprite != null) {
        _background = SpriteComponent(
          sprite: _backgroundSprite!,
          size: gameSize,
        );
        add(_background!);

        if (kDebugMode) {
          print("Demo game background added with size: ${gameSize.x} x ${gameSize.y}");
        }

        // Load the single round with match limit
        Future.microtask(() => _loadDemoRound());
      }
    }
  }

  void _applyPopInEffect(PositionComponent component) {
    component.scale = Vector2.all(0.1);

    component.add(
      SequenceEffect([
        ScaleEffect.to(
          Vector2.all(1.2),
          EffectController(
            duration: 0.4,
            curve: Curves.easeOutBack,
          ),
        ),
        ScaleEffect.to(
          Vector2.all(1.0),
          EffectController(
            duration: 0.2,
            curve: Curves.easeInOut,
          ),
        ),
      ]),
    );

    component.add(
      OpacityEffect.fadeIn(
        EffectController(
          duration: 0.3,
          curve: Curves.easeIn,
        ),
      ),
    );
  }

  Future<void> _loadDemoRound() async {
    if (!_isMounted || _isGameComplete) {
      return;
    }

    if (kDebugMode) {
      print("Loading demo round with $matchLimit matches");
    }

    _matchedCount = 0;

    // Clear all components except background
    final componentsToRemove = children
        .where((component) => component != _background)
        .toList();
    for (final component in componentsToRemove) {
      remove(component);
    }

    // Load sprite assets
    final draggableSprites = await Future.wait(
      List.generate(5, (i) => loadSprite('$gameName/draggable_image_$i.png')),
    );

    final dragTargetSprites = await Future.wait(
      List.generate(5, (i) => loadSprite('$gameName/drag_target_image_$i.png')),
    );

    final acceptedTargetSprites = await Future.wait(
      List.generate(5, (i) => loadSprite('$gameName/drag_target_image_accepted_$i.png')),
    );

    if (!_isMounted || _isGameComplete) {
      return;
    }

    final currentSize = size;
    if (currentSize.x <= 0 || currentSize.y <= 0) {
      if (kDebugMode) {
        print("Invalid game size: $currentSize - deferring demo round loading");
      }
      Future.microtask(() => _loadDemoRound());
      return;
    }

    // Randomly select which pairs to show (limited to matchLimit)
    final random = Random();
    final indices = List<int>.generate(5, (i) => i)..shuffle(random);
    final selectedIndices = indices.take(matchLimit).toList();

    if (kDebugMode) {
      print("Selected indices for demo: $selectedIndices");
    }

    // Calculate component sizes
    final maxWidth = currentSize.x / (matchLimit + 1);
    final maxHeight = currentSize.y / 3;
    final spriteSize = Vector2.all(maxWidth < maxHeight ? maxWidth : maxHeight);

    final List<PositionComponent> newComponents = [];

    // Add draggable items
    for (int i = 0; i < selectedIndices.length; i++) {
      final index = selectedIndices[i];

      final topLeftPosition = Vector2(
        currentSize.x / (matchLimit + 1) * (i + 1) - spriteSize.x / 2,
        currentSize.y / 10,
      );

      final centerPosition = Vector2(
        topLeftPosition.x + spriteSize.x / 2,
        topLeftPosition.y + spriteSize.y / 2,
      );

      if (!_isMounted || _isGameComplete) return;

      final draggable = DraggableItem(
        index: index,
        sprite: draggableSprites[index],
        initialPosition: centerPosition,
        size: spriteSize,
        gameRef: this,
      );

      draggable.anchor = Anchor.center;
      draggable.position = centerPosition;
      draggable.scale = Vector2.all(0.1);
      if (draggable.paint.color != Colors.transparent) {
        draggable.paint.color = draggable.paint.color.withValues(alpha: 0);
      }

      add(draggable);
      newComponents.add(draggable);
    }

    // Add drag target items
    selectedIndices.shuffle(random);
    for (int i = 0; i < selectedIndices.length; i++) {
      final index = selectedIndices[i];

      final topLeftPosition = Vector2(
        currentSize.x / (matchLimit + 1) * (i + 1) - spriteSize.x / 2,
        currentSize.y / 2,
      );

      final centerPosition = Vector2(
        topLeftPosition.x + spriteSize.x / 2,
        topLeftPosition.y + spriteSize.y / 2,
      );

      if (!_isMounted || _isGameComplete) return;

      final target = DragTargetItem(
        index: index,
        sprite: dragTargetSprites[index],
        acceptedSprite: acceptedTargetSprites[index],
        position: topLeftPosition,
        size: spriteSize,
        gameRef: this,
      );

      target.anchor = Anchor.center;
      target.position = centerPosition;
      target.scale = Vector2.all(0.1);
      if (target.paint.color != Colors.transparent) {
        target.paint.color = target.paint.color.withValues(alpha: 0);
      }

      add(target);
      newComponents.add(target);
    }

    // Apply pop-in effect to all new components
    for (var i = 0; i < newComponents.length; i++) {
      final component = newComponents[i];
      Future.delayed(Duration(milliseconds: 50 * i), () {
        if (_isMounted && !_isGameComplete) {
          _applyPopInEffect(component);
        }
      });
    }

    if (kDebugMode) {
      print("Demo round loaded with ${newComponents.length} components");
    }
  }

  void onItemMatched() {
    if (!_isMounted || _isGameComplete) return;

    _matchedCount++;

    if (kDebugMode) {
      print("Demo item matched. Total: $_matchedCount / $matchLimit");
    }

    // Notify wrapper of match completion
    if (onMatchCompleted != null) {
      onMatchCompleted!();
    }

    // Check if all matches are complete
    if (_matchedCount >= matchLimit) {
      if (kDebugMode) {
        print("Demo game complete");
      }
      _demoGameComplete();
    }
  }

  void _demoGameComplete() {
    if (_isGameComplete) return;

    _isGameComplete = true;
    AudioService.instance.playSound('game_complete_sound.wav');

    if (kDebugMode) {
      print("Demo game completed - showing confetti");
    }

    // Add confetti celebration
    Future.delayed(const Duration(milliseconds: 300), () {
      final confetti = FlameConfetti();
      add(confetti);

      Future.delayed(const Duration(milliseconds: 100), () {
        confetti.burst(Vector2(size.x / 2, size.y / 2));
      });
    });

    // Notify wrapper that game is complete immediately
    if (onGameCompleted != null) {
      onGameCompleted!();
    }
  }

  @override
  void onRemove() {
    _isMounted = false;
    _isGameComplete = true;

    // Stop music when demo game is removed
    AudioService.instance.stopMusic();

    // Clear references to help garbage collection
    _background = null;
    _backgroundSprite = null;

    if (kDebugMode) {
      print('🧹 Demo game resources cleared');
    }

    super.onRemove();
  }

  @override
  void removeFromParent() {
    _isMounted = false;
    _isGameComplete = true;

    try {
      super.removeFromParent();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error removing demo game from parent: $e');
      }
    }
  }

  @override
  void onDispose() {
    _isMounted = false;
    _isGameComplete = true;

    // Clear all references
    _background = null;
    _backgroundSprite = null;

    if (kDebugMode) {
      print('🧹 Demo game disposed');
    }

    super.onDispose();
  }
}

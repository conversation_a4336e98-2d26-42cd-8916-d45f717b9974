{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b9a4b052dbaba383fdb8115850af00c2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98447f19d4de9a173a10f24ceb5b1a25ee", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9800b07ee879b3ffd1dad0646569eb5224", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98416c23e2cfa6ec689c8dd80b9cbba378", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9800b07ee879b3ffd1dad0646569eb5224", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e13f79d2f23766dbbad9739e6308da8c", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a433752197178b9cb0cc1b4f72a4da7a", "guid": "bfdfe7dc352907fc980b868725387e98d25cd70975ba96e8a3057a2ad49c6f8c", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e985ddf373b308a4ad4994aec91f86532b0", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9856f76ac855abba3bfcca90fc6f07552c", "guid": "bfdfe7dc352907fc980b868725387e981fa0586b83434cd25504fe9fb1bb8010"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b2ecb397fb833d0148bd23f3a79485d", "guid": "bfdfe7dc352907fc980b868725387e98f698fd974f68423b33d43e9d9d48f10a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824bead27a4baa2ffe617854f8dcb19ad", "guid": "bfdfe7dc352907fc980b868725387e98450f8a964cb7102df4f4be86405fc17a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e84f590251bd4ecdded4853ef5a0d128", "guid": "bfdfe7dc352907fc980b868725387e98f4d245c6997801007b6bec4f7e927885"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888999913f358cf3b400cbd0c41f3f2f9", "guid": "bfdfe7dc352907fc980b868725387e98df18ed140b566c64b0fa6eeea6506394"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98106e1aef1ab4c3f73c787c8e87b9f7dd", "guid": "bfdfe7dc352907fc980b868725387e985ab7da79cb78ba2131522c8cde1d43c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98347a7aac7576a16b177f6b1a6138164f", "guid": "bfdfe7dc352907fc980b868725387e98a5055f15a985fd40a988595886dcce20"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea02bb284d3aa0e943b041e2786f52b0", "guid": "bfdfe7dc352907fc980b868725387e98e335e1a007de475d0c69e9d9953664f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f395732c4e833a8a8c34ae18002d080f", "guid": "bfdfe7dc352907fc980b868725387e984e39fa7742664a95500cd338b0b7726b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839025056dd60135ad1db1b3dbb9dbf65", "guid": "bfdfe7dc352907fc980b868725387e989aca6dd4b1604237c8668dd6582afda5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d43b913f975f197f9dea1402ecfaf020", "guid": "bfdfe7dc352907fc980b868725387e98f8866e8a3473f2befbaebfea62a00e47"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985521d17cf6b93b5747885c0e9f3f8285", "guid": "bfdfe7dc352907fc980b868725387e98921b8d56b018a67b6dba7fa666ae2829"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981806524e493ab8c438a89d0f400b622a", "guid": "bfdfe7dc352907fc980b868725387e9897770e4de5da2b16f02e03e645410da3"}], "guid": "bfdfe7dc352907fc980b868725387e984275417fe22147c1c985e9a089b4f013", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986d33a47f1e1031e7e6a671aab8d386e4", "guid": "bfdfe7dc352907fc980b868725387e98dec2a72bffdb645fdcdf09fb328f34ee"}], "guid": "bfdfe7dc352907fc980b868725387e986cc80d5e13b54457423b127806edb26f", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9876c8a300afa88f5ca68408dd2c42bd77", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e982d5642c37d3781dc249b0b4bbde257bf", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
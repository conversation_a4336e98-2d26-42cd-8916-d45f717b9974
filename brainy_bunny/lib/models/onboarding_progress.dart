// lib/models/onboarding_progress.dart
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

/// Model to track onboarding progress
class OnboardingProgress {
  int currentScreenIndex;
  Set<int> completedScreens;
  Set<int> completedDemoGames;
  List<String> earnedBadges;
  bool isCompleted;
  DateTime? lastUpdated;

  OnboardingProgress({
    this.currentScreenIndex = 0,
    Set<int>? completedScreens,
    Set<int>? completedDemoGames,
    List<String>? earnedBadges,
    this.isCompleted = false,
    this.lastUpdated,
  })  : completedScreens = completedScreens ?? {},
        completedDemoGames = completedDemoGames ?? {},
        earnedBadges = earnedBadges ?? [];

  /// Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'currentScreenIndex': currentScreenIndex,
      'completedScreens': completedScreens.toList(),
      'completedDemoGames': completedDemoGames.toList(),
      'earnedBadges': earnedBadges,
      'isCompleted': isCompleted,
      'lastUpdated': lastUpdated?.toIso8601String(),
    };
  }

  /// Create from JSON
  factory OnboardingProgress.fromJson(Map<String, dynamic> json) {
    return OnboardingProgress(
      currentScreenIndex: json['currentScreenIndex'] as int? ?? 0,
      completedScreens: (json['completedScreens'] as List<dynamic>?)
          ?.map((e) => e as int)
          .toSet() ?? {},
      completedDemoGames: (json['completedDemoGames'] as List<dynamic>?)
          ?.map((e) => e as int)
          .toSet() ?? {},
      earnedBadges: (json['earnedBadges'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ?? [],
      isCompleted: json['isCompleted'] as bool? ?? false,
      lastUpdated: json['lastUpdated'] != null
          ? DateTime.parse(json['lastUpdated'] as String)
          : null,
    );
  }

  /// Save to SharedPreferences
  Future<void> save() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = jsonEncode(toJson());
      await prefs.setString('onboarding_progress', jsonString);
      await prefs.setBool('onboarding_completed', isCompleted);
    } catch (e) {
      print('❌ Failed to save onboarding progress: $e');
    }
  }

  /// Load from SharedPreferences
  static Future<OnboardingProgress> load() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString('onboarding_progress');

      if (jsonString != null) {
        final json = jsonDecode(jsonString) as Map<String, dynamic>;
        return OnboardingProgress.fromJson(json);
      }
    } catch (e) {
      print('❌ Failed to load onboarding progress: $e');
    }

    return OnboardingProgress();
  }

  /// Check if onboarding has been completed (static method for quick check)
  static Future<bool> isOnboardingCompleted() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool('onboarding_completed') ?? false;
    } catch (e) {
      print('❌ Failed to check onboarding completion: $e');
      return false;
    }
  }

  /// Clear saved progress
  static Future<void> clear() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('onboarding_progress');
      await prefs.remove('onboarding_completed');
    } catch (e) {
      print('❌ Failed to clear onboarding progress: $e');
    }
  }

  /// Mark a screen as completed
  void completeScreen(int screenIndex) {
    completedScreens.add(screenIndex);
    if (screenIndex > currentScreenIndex) {
      currentScreenIndex = screenIndex;
    }
    lastUpdated = DateTime.now();
  }

  /// Mark a demo game as completed
  void completeDemoGame(int gameIndex) {
    completedDemoGames.add(gameIndex);
    lastUpdated = DateTime.now();
  }

  /// Add an earned badge
  void addBadge(String badgeName) {
    if (!earnedBadges.contains(badgeName)) {
      earnedBadges.add(badgeName);
      lastUpdated = DateTime.now();
    }
  }

  /// Mark onboarding as completed
  void complete() {
    isCompleted = true;
    lastUpdated = DateTime.now();
  }

  /// Check if a specific screen is completed
  bool isScreenCompleted(int screenIndex) {
    return completedScreens.contains(screenIndex);
  }

  /// Check if a specific demo game is completed
  bool isDemoGameCompleted(int gameIndex) {
    return completedDemoGames.contains(gameIndex);
  }

  /// Get completion percentage (0-100)
  double getCompletionPercentage({int totalScreens = 14}) {
    if (isCompleted) return 100.0;
    return (completedScreens.length / totalScreens * 100).clamp(0.0, 100.0);
  }

  /// Check if all demo games are completed
  bool areAllDemoGamesCompleted({int totalDemoGames = 5}) {
    return completedDemoGames.length >= totalDemoGames;
  }

  /// Create a copy with updated fields
  OnboardingProgress copyWith({
    int? currentScreenIndex,
    Set<int>? completedScreens,
    Set<int>? completedDemoGames,
    List<String>? earnedBadges,
    bool? isCompleted,
    DateTime? lastUpdated,
  }) {
    return OnboardingProgress(
      currentScreenIndex: currentScreenIndex ?? this.currentScreenIndex,
      completedScreens: completedScreens ?? this.completedScreens,
      completedDemoGames: completedDemoGames ?? this.completedDemoGames,
      earnedBadges: earnedBadges ?? this.earnedBadges,
      isCompleted: isCompleted ?? this.isCompleted,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  @override
  String toString() {
    return 'OnboardingProgress(currentScreen: $currentScreenIndex, completed: ${completedScreens.length} screens, ${completedDemoGames.length} games, badges: ${earnedBadges.length}, isCompleted: $isCompleted)';
  }
}

// lib/onboarding/screens/problem_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

/// Problem Screen - Screen 2
/// Establishes empathy, acknowledges parent concerns
class ProblemScreen extends StatefulWidget {
  final VoidCallback onContinue;

  const ProblemScreen({
    super.key,
    required this.onContinue,
  });

  @override
  State<ProblemScreen> createState() => _ProblemScreenState();
}

class _ProblemScreenState extends State<ProblemScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return Scaffold(
      backgroundColor: Colors.white,
      body: Safe<PERSON>rea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(height: size.height * 0.04),

              // Concerned parent icon
              FadeTransition(
                opacity: _animationController,
                child: Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    color: const Color(0xFFF5F5F5),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.phone_android,
                    size: 50,
                    color: Colors.grey.shade600,
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Headline
              FadeTransition(
                opacity: _animationController,
                child: const Text(
                  'Screen time that worries\nparents',
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF333333),
                    height: 1.3,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

              SizedBox(height: size.height * 0.04),

              // Problem points
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      _buildProblemPoint(
                        context,
                        icon: Icons.visibility_off,
                        title: 'Passive video watching',
                        description: 'No learning, just staring',
                        delay: 0.2,
                      ),
                      const SizedBox(height: 16),
                      _buildProblemPoint(
                        context,
                        icon: Icons.refresh,
                        title: 'Addictive mechanics',
                        description: 'Designed to maximize screen time',
                        delay: 0.4,
                      ),
                      const SizedBox(height: 16),
                      _buildProblemPoint(
                        context,
                        icon: Icons.warning_amber_rounded,
                        title: 'Inappropriate content',
                        description: 'Ads and unsafe material',
                        delay: 0.6,
                      ),
                      const SizedBox(height: 16),
                      _buildProblemPoint(
                        context,
                        icon: Icons.school_outlined,
                        title: 'Zero learning value',
                        description: 'Entertainment that doesn\'t build skills',
                        delay: 0.8,
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Subtext
              Text(
                'Sound familiar? You\'re not alone.',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 24),

              // Continue button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: widget.onContinue,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF4A90E2),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 18),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(28),
                    ),
                    elevation: 2,
                  ),
                  child: const Text(
                    'There\'s a better way',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProblemPoint(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String description,
    required double delay,
  }) {
    final delayedAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Interval(
        delay,
        1.0,
        curve: Curves.easeOut,
      ),
    );

    return FadeTransition(
      opacity: delayedAnimation,
      child: SlideTransition(
        position: Tween<Offset>(
          begin: const Offset(0, 0.3),
          end: Offset.zero,
        ).animate(delayedAnimation),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(
                Icons.close,
                color: const Color(0xFFF44336),
                size: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF333333),
                        height: 1.3,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade700,
                        height: 1.4,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

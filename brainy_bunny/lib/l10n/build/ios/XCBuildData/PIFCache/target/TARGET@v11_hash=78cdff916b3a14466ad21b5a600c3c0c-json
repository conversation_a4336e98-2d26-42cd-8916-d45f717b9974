{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98618e4c91cc160750f1d1d8f6d43579f1", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98670e1ec08df9357af0c751bab7adc3c1", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d9f591f81e10c8e565e54564b81270a5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98237e3a8d1ad24dd44cc9c237deb7617e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d9f591f81e10c8e565e54564b81270a5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a7239967de5dc8d5ecc1c8631678a513", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f8ff69d70334d81670a0514e5cf4bdca", "guid": "bfdfe7dc352907fc980b868725387e98c29c9d50e515a4b37f1e33af80b3fd61", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859e5835141eabe631a2c275563d0a18a", "guid": "bfdfe7dc352907fc980b868725387e9819348dfd581e750588798867e05aea11", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b7932ed5ed227775065a57fc699ba09", "guid": "bfdfe7dc352907fc980b868725387e982617f755a760a7fd5c399515ade3ea52"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bab1c33ef4c6fb4d184366f5eaee28d8", "guid": "bfdfe7dc352907fc980b868725387e9836026071b0cfad1e426fa1a63a8ec37d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9dab4c6ad80805761ba23ba44740b55", "guid": "bfdfe7dc352907fc980b868725387e98379b22bc09a15e950bf8824f0924655c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98114ee90039588fea99a40f3bd91ece53", "guid": "bfdfe7dc352907fc980b868725387e981f2c050a67e24aacfdfe65d2fff582a8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98703a51e8b3cf3845989d74d76db54b88", "guid": "bfdfe7dc352907fc980b868725387e98414b1d2fef7f492e8ec6d907800ad345", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f03f6c6941902941f7cec7404e0008fd", "guid": "bfdfe7dc352907fc980b868725387e98e08ddd3cd7538d0e82dead0c1633870a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7f87aacfd0c27f7af6bb7822d8504e8", "guid": "bfdfe7dc352907fc980b868725387e985c3e20fc8c2b00b312a197c196e8ad9c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988410bdd6d429db1303702e9044427723", "guid": "bfdfe7dc352907fc980b868725387e9883d83402e3c393eba2e7636738b2b66c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987dbcdcd4a0ee5dff7cd88db761dedb90", "guid": "bfdfe7dc352907fc980b868725387e9834d044c5d0eb2ffbb9d4196321c5ca42", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986da97adee693d748bebaa4e2b74fcf5f", "guid": "bfdfe7dc352907fc980b868725387e986fe7f2d4d6e7e55841ae358ff397eab1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800ae168b80672830d9a9fbcfeb96929f", "guid": "bfdfe7dc352907fc980b868725387e98d5da31a9b0f77935fb417b5c04471505", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98172958a99cd4074eeb13f621e964aac9", "guid": "bfdfe7dc352907fc980b868725387e98c05bde50ae0b7b1ca2581348cebc051f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e12e0b6f888ad70662a99ab18f3c4fe5", "guid": "bfdfe7dc352907fc980b868725387e98fe1935f1616ae2f2d0c5064bd404ee67"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1a0137dd07ca68c085f89ca33393e7e", "guid": "bfdfe7dc352907fc980b868725387e988d05732a6ca44cf44b52a07665a32160", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c839df4b5e8c36a86a710ebe401c1317", "guid": "bfdfe7dc352907fc980b868725387e987bddfeee736b273518a39ee31dbcdad5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bf5b6bf4528d626d1f0c81f70cb5f8d", "guid": "bfdfe7dc352907fc980b868725387e98a26a58afea3cf5104abf5bb28f075b84", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98066f667b68ea03a7033f538534ce7240", "guid": "bfdfe7dc352907fc980b868725387e9898173dfe159bfbb1235f80e8b322187e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887dba45e317c9b780005c34663690257", "guid": "bfdfe7dc352907fc980b868725387e98663372f85fe3395685d6f26e6d123d3c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a47a6a2dc49585f785537dd645542fbb", "guid": "bfdfe7dc352907fc980b868725387e9843a20c27a5e888d2d632e8f633ede75d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e5e171ea8da82e7efe2441fb98bef9e", "guid": "bfdfe7dc352907fc980b868725387e98397d357772b8b151620507ae9b51ea50"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c8a2f48f1dac7724ef327f4634dea01", "guid": "bfdfe7dc352907fc980b868725387e986dd40ebd51e8ac7fe7c9b1032a846386", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1529a786911d78999d6e551a11c18f8", "guid": "bfdfe7dc352907fc980b868725387e9899bb7b40c6db28e06e6da1be6e74e976"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a19a15333a3faddf89c92c16c72519b", "guid": "bfdfe7dc352907fc980b868725387e980c44caa000b8993645ca732bdfc2ca6b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986898110a34ff6b59cfd1b84bb0d70824", "guid": "bfdfe7dc352907fc980b868725387e981c1941626b77da38fec56f26777b92e8"}], "guid": "bfdfe7dc352907fc980b868725387e98b477404740601938439d64b96bb16cc5", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988cbd09c437760eea2d3bb0c4fa090682", "guid": "bfdfe7dc352907fc980b868725387e9872900db9595664bdacc85a65fc9f9f50"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d20c490e5a9a4c3da1cae4b862ea477c", "guid": "bfdfe7dc352907fc980b868725387e98bf004e028c7e44d50f4c009fd6480b0a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b956b0483d80fc331893698ca5ee9762", "guid": "bfdfe7dc352907fc980b868725387e982a1089252c4eaa8132c75b58aa8f47fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a8195f1498129e433bcebc83e9f47804", "guid": "bfdfe7dc352907fc980b868725387e98b3122d7814bf35f4b0237fb9c13508df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98345cd66a7d5bb0dd50fa8fc6f4716a13", "guid": "bfdfe7dc352907fc980b868725387e9807ce57854197860de23a1d7ac992a4fd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986611491c8ac8be4758fbeb5076497879", "guid": "bfdfe7dc352907fc980b868725387e98778a94a2bf34a5ad672e2155b8db5176"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5c6c88b8203653b1d38adf252872e4a", "guid": "bfdfe7dc352907fc980b868725387e98e4538216f92add6361b058c66a30827b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863d3a722c6bdf62e41fadfdb3623ef17", "guid": "bfdfe7dc352907fc980b868725387e98036c9370b945f0c286087d7c63677ca9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e247a250b0e9e22de5c4ab8fd4356153", "guid": "bfdfe7dc352907fc980b868725387e98256213dbcac0a50652a9aec6221a3739"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984d4a415a23651591551d41c279037a4e", "guid": "bfdfe7dc352907fc980b868725387e98c2b15cba6df93d4cdebc08d25015f9e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98658cfa3bb14b8b24e4303253639d193d", "guid": "bfdfe7dc352907fc980b868725387e98d564e2ad94da81aee6a3c7e30db1b24b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e672511db7ffce57faf1d42b3add5260", "guid": "bfdfe7dc352907fc980b868725387e981f68adbf6c0af6c5f983147cf6067810"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f43659b5b975b26d64307090fd6c101", "guid": "bfdfe7dc352907fc980b868725387e98259e9332f45fab6e7bec62b1dac70124"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98001c749f86369ce33e5c68ee3a1f6963", "guid": "bfdfe7dc352907fc980b868725387e98a1382401bf93d73b08ec3f2ee162128a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805d8b115110821ee8a4f7b7634d757a3", "guid": "bfdfe7dc352907fc980b868725387e986ec3d2f3941fc931ad09571d76ebddcf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bd5775c967c5653739e38bb7381dc50", "guid": "bfdfe7dc352907fc980b868725387e98466a37e44ea69c6c6c7cd0d324f146d9"}], "guid": "bfdfe7dc352907fc980b868725387e98cc2c96ae2c07d1c921dd6727ab6bb942", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986d33a47f1e1031e7e6a671aab8d386e4", "guid": "bfdfe7dc352907fc980b868725387e984b4f7b95467db36996e52c3f7b450bc1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825fcdd28269a098362a526741c54f856", "guid": "bfdfe7dc352907fc980b868725387e9838a167f97c40b32b8a90b5a6b68d7a46"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b3eecf116c5aea51d48318b3aa1954f", "guid": "bfdfe7dc352907fc980b868725387e9884e39f14dfc41644daf796022ddb49cc"}], "guid": "bfdfe7dc352907fc980b868725387e9868fca52ca9e4ab552c8dc3b20b5d8297", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e986ebdcdfd4ae455b1e933b73d6a5bee1f", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e982e92cee56c4453f93447903b6a54b62e", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
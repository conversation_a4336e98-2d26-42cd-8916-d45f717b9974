// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Russian (`ru`).
class AppLocalizationsRu extends AppLocalizations {
  AppLocalizationsRu([String locale = 'ru']) : super(locale);

  @override
  String get onboarding_welcome_headline =>
      'Помогите вашему ребенку учиться и расти';

  @override
  String get onboarding_welcome_subheading =>
      'Обучающие игры для детей от 2 до 5 лет';

  @override
  String get onboarding_welcome_cta => 'Начать';

  @override
  String get onboarding_name_headline => 'Персонализируем ваш опыт';

  @override
  String get onboarding_name_hint => 'Ваше имя';

  @override
  String get onboarding_name_mom => 'Мама';

  @override
  String get onboarding_name_dad => 'Папа';

  @override
  String get onboarding_name_parent => 'Родитель';

  @override
  String onboarding_name_greeting(String name) {
    return 'Отлично, $name!';
  }

  @override
  String get onboarding_child_age_headline => 'Сколько лет вашему ребенку?';

  @override
  String get onboarding_child_age_subtext =>
      'All games work for ages 2-5. This helps us provide age-appropriate tips.';

  @override
  String get onboarding_age_2 => '2 года';

  @override
  String get onboarding_age_3 => '3 года';

  @override
  String get onboarding_age_4 => '4 года';

  @override
  String get onboarding_age_5_plus => '5+ лет';

  @override
  String get onboarding_philosophy_headline =>
      'Превратите экранное время в время обучения';

  @override
  String get onboarding_philosophy_aap =>
      'Соответствует рекомендациям AAP по экранному времени';

  @override
  String get onboarding_philosophy_learning =>
      'Превратите экранное время в осмысленный опыт обучения';

  @override
  String get onboarding_philosophy_skills =>
      'Развивайте когнитивные навыки через обучение на основе игры';

  @override
  String get onboarding_transition_to_games =>
      'Давайте играть! Поверните устройство →';

  @override
  String get onboarding_transition_from_games =>
      'Отличное обучение! Завершим →';

  @override
  String get onboarding_solution_headline => 'Learning through play';

  @override
  String get onboarding_solution_description =>
      'While they match shapes and colors, they\'re building real cognitive skills.';

  @override
  String get onboarding_solution_benefit_engagement => 'Active engagement';

  @override
  String get onboarding_solution_benefit_pattern_recognition =>
      'Pattern recognition';

  @override
  String get onboarding_solution_benefit_cause_effect =>
      'Cause-and-effect thinking';

  @override
  String get onboarding_solution_benefit_screen_time =>
      'Aligned with 1-hour screen time guidelines';

  @override
  String get onboarding_solution_research_text =>
      'Research shows: Matching activities improve spatial reasoning...';

  @override
  String get onboarding_solution_research_source =>
      'Based on developmental psychology studies';

  @override
  String get onboarding_solution_cta => 'See how it works';

  @override
  String get onboarding_problem_headline =>
      'We understand your\nscreen time concerns';

  @override
  String get onboarding_problem_point_1_title => 'Passive video consumption';

  @override
  String get onboarding_problem_point_1_description =>
      'Hours of mindless watching with zero interaction or learning';

  @override
  String get onboarding_problem_point_1_statistic =>
      '6x higher risk of language delays';

  @override
  String get onboarding_problem_point_2_title => 'Brain development damage';

  @override
  String get onboarding_problem_point_2_description =>
      'Excessive screen time alters white matter structure in developing brains';

  @override
  String get onboarding_problem_point_2_statistic =>
      'Reduced cognitive abilities';

  @override
  String get onboarding_problem_point_3_title =>
      'Inappropriate content exposure';

  @override
  String get onboarding_problem_point_3_description =>
      'Ads, violence, and age-inappropriate material in \'kids\' content';

  @override
  String get onboarding_problem_point_3_statistic =>
      '85% of kids\' apps contain ads';

  @override
  String get onboarding_problem_point_4_title => 'Attention & focus problems';

  @override
  String get onboarding_problem_point_4_description =>
      'Fast-paced content destroys ability to concentrate and learn';

  @override
  String get onboarding_problem_point_4_statistic =>
      '40% increase in ADHD symptoms';

  @override
  String get onboarding_problem_research_title => 'Scientific Evidence';

  @override
  String get onboarding_problem_research_text =>
      'Peer-reviewed studies confirm: excessive passive screen time causes brain changes, language delays, and attention problems in young children.';

  @override
  String get onboarding_problem_subtext =>
      'You\'re not alone. 89% of parents share these concerns.';

  @override
  String get onboarding_problem_cta => 'There\'s a better way';

  @override
  String get demo_game_1_skill =>
      'Визуальное сопоставление и распознавание форм';

  @override
  String get demo_game_1_science =>
      'Сопоставление животных с их силуэтами развивает визуальную дискриминацию — способность замечать различия между похожими формами. Этот навык является основой для распознавания букв и чтения.';

  @override
  String get demo_game_1_citation =>
      'Борнштейн (1985) - Визуальная обработка и категориальное мышление';

  @override
  String get demo_game_1_badge => 'Детектив форм';

  @override
  String get demo_game_2_skill => 'Визуальная память и внимание';

  @override
  String get demo_game_2_science =>
      'Поиск пар укрепляет рабочую память — способность вашего ребенка удерживать и обрабатывать информацию. Это напрямую поддерживает решение математических задач и следование многоступенчатым инструкциям.';

  @override
  String get demo_game_2_citation =>
      'Гопник и Мельцофф (1987) - Категоризация и когнитивная гибкость';

  @override
  String get demo_game_2_badge => 'Мастер памяти';

  @override
  String get demo_game_3_skill => 'Логические ассоциации и категоризация';

  @override
  String get demo_game_3_science =>
      'Связывание объектов с их пользователями учит категоризации и логическому мышлению. Ваш ребенок узнает, что вещи связаны по причинам — ключевой шаг в понимании причины и следствия.';

  @override
  String get demo_game_3_citation =>
      'Пиаже (1952) - Дооперациональное когнитивное развитие';

  @override
  String get demo_game_3_badge => 'Звезда логики';

  @override
  String get demo_game_4_skill => 'Распознавание паттернов и сопоставление';

  @override
  String get demo_game_4_science =>
      'Сопоставление паттернов развивает распознавание паттернов — способность видеть связи между вещами. Этот навык сильно предсказывает успех в математике и помогает детям понимать \'одинаковое\' и \'разное\'.';

  @override
  String get demo_game_4_citation =>
      'Риттл-Джонсон и др. (2019) - Навыки паттернов и математика';

  @override
  String get demo_game_4_badge => 'Профессионал паттернов';

  @override
  String get demo_game_5_skill =>
      'Символическое мышление и связи с реальным миром';

  @override
  String get demo_game_5_science =>
      'Связывание инструментов с профессиями развивает символическое мышление — понимание того, что одна вещь может представлять другую. Это абстрактное мышление необходимо для языка, математики и воображения.';

  @override
  String get demo_game_5_citation =>
      'Выготский (1978) - Символическое представление в когнитивном развитии';

  @override
  String get demo_game_5_badge => 'Исследователь мира';

  @override
  String get demo_game_next => 'Следующая игра';

  @override
  String onboarding_summary_headline(String name) {
    return 'Ваш прогресс, $name';
  }

  @override
  String onboarding_summary_age(int age) {
    return 'Идеально для $age-летних';
  }

  @override
  String get onboarding_summary_skills => '5 навыков отработаны';

  @override
  String get onboarding_summary_screen_time =>
      'Здоровый подход к экранному времени в соответствии с рекомендациями AAP';

  @override
  String get onboarding_summary_cta => 'Посмотреть персональный план';

  @override
  String get trust_headline => 'Нам доверяют родители и педагоги';

  @override
  String get trust_approved => 'Одобрено учителями';

  @override
  String get trust_cta => 'Получить полный доступ к обучению';

  @override
  String get paywall_headline => 'Откройте доступ ко всем 15 обучающим играм';

  @override
  String get paywall_subheadline => 'Продолжите путь обучения вашего ребенка';

  @override
  String get paywall_feature_games =>
      '15 обучающих игр для развития ключевых навыков';

  @override
  String get paywall_feature_progress =>
      'Отслеживание прогресса вашего ребенка';

  @override
  String get paywall_feature_ad_free => 'Безопасная среда без рекламы';

  @override
  String get paywall_feature_new_games => 'Новые игры добавляются ежемесячно';

  @override
  String get paywall_feature_ages => 'Разработано для детей 2-5 лет';

  @override
  String get paywall_trial_headline =>
      'Попробуйте все функции БЕСПЛАТНО 7 дней';

  @override
  String get paywall_trial_price => 'Затем всего 0,87 €/неделю';

  @override
  String get paywall_trial_today => 'Сегодня: Полный доступ открыт';

  @override
  String get paywall_trial_day5 => 'День 5: Мы отправим напоминание';

  @override
  String get paywall_trial_day7 =>
      'День 7: Начало оплаты (можно отменить в любой момент)';

  @override
  String get paywall_trial_no_payment => '✓ Оплата не требуется сейчас';

  @override
  String get paywall_trial_cta => 'Начать бесплатный пробный период →';

  @override
  String get paywall_weekly => 'Еженедельно';

  @override
  String get paywall_monthly => 'Ежемесячно';

  @override
  String get paywall_yearly => 'Ежегодно';

  @override
  String get paywall_save_60 => 'Экономия 60%';

  @override
  String get paywall_most_popular => 'Самый популярный выбор';

  @override
  String get paywall_cancel_anytime =>
      'Отмените в любое время в настройках устройства';

  @override
  String get paywall_restore => 'Восстановить покупки';

  @override
  String get subscription_premium => 'Премиум';

  @override
  String get subscription_trial => 'Пробный период 7 дней';

  @override
  String get subscription_lifetime => 'Доступ навсегда';

  @override
  String get subscription_expired => 'Подписка завершена';

  @override
  String get subscription_manage => 'Управление подпиской';

  @override
  String get subscription_cancel => 'Отменить подписку';

  @override
  String get subscription_change_plan => 'Изменить план';

  @override
  String get continue_button => 'Продолжить';

  @override
  String get skip_button => 'Пропустить';

  @override
  String get close_button => 'Закрыть';

  @override
  String get loading => 'Загрузка...';

  @override
  String get error_purchase_failed => 'Покупка не удалась';

  @override
  String get error_purchase_failed_message =>
      'Не удалось завершить покупку. Пожалуйста, попробуйте снова.';

  @override
  String get error_restore_failed => 'Покупки не найдены';

  @override
  String get error_restore_failed_message =>
      'Не удалось найти предыдущие покупки. Если вы считаете, что это ошибка, обратитесь в службу поддержки.';

  @override
  String get error_network => 'Ошибка сети';

  @override
  String get error_network_message =>
      'Пожалуйста, проверьте подключение к интернету и попробуйте снова.';

  @override
  String get summary_headline => 'Отличный прогресс!';

  @override
  String summary_message_age_2(Object name) {
    return 'Отлично, $name! Ваш малыш развивает важные навыки через игру. В 2 года каждое совпадение укрепляет визуальное распознавание и способности к решению проблем.';
  }

  @override
  String summary_message_age_3(Object name) {
    return 'Замечательно, $name! Ваш ребенок развивает важнейшие когнитивные навыки. В 3 года эти занятия улучшают память, концентрацию и логическое мышление.';
  }

  @override
  String summary_message_age_4(Object name) {
    return 'Превосходно, $name! Ваш 4-летний ребенок осваивает продвинутое решение задач. Эти игры готовят к успеху в детском саду.';
  }

  @override
  String summary_message_age_5_plus(Object name) {
    return 'Фантастика, $name! Ваш ребенок превосходно справляется со сложным мышлением. Эти навыки дадут прочную основу для школы.';
  }

  @override
  String get summary_badges_earned => 'Значки получены';

  @override
  String get summary_badges => 'Значки';

  @override
  String get summary_games => 'Игры';

  @override
  String get summary_skills => 'Навыки';

  @override
  String get trust_aap_description =>
      'Наш образовательный подход соответствует рекомендациям AAP по здоровому экранному времени и раннему детскому развитию.';

  @override
  String get trust_research_title => 'Программа на основе исследований';

  @override
  String get trust_research_description =>
      'Каждая игра разработана на основе рецензируемых исследований когнитивного развития с методами, доказавшими эффективность для детей 2-5 лет.';

  @override
  String get trust_testimonial_1_name => 'Сара М., мама 3-летнего ребенка';

  @override
  String get trust_testimonial_1_quote =>
      '\"Моя дочь перешла от трудностей с формами к уверенному распознаванию их везде. Прогресс за 2 недели меня поразил!\"';

  @override
  String get trust_testimonial_2_name => 'Михаил Т., папа 4-летнего ребенка';

  @override
  String get trust_testimonial_2_quote =>
      '\"Наконец-то экранное время, которое не вызывает беспокойства! Мой сын учится, играя, и я вижу реальные когнитивные улучшения.\"';

  @override
  String get trust_downloads_title =>
      'Присоединяйтесь к более чем 100 000 семей';

  @override
  String get trust_downloads_description =>
      'Нам доверяют родители из более чем 50 стран, чтобы дать своим детям преимущество в обучении.';

  @override
  String get trust_cta_headline =>
      'Готовы раскрыть полный потенциал вашего ребенка?';

  @override
  String get trust_cta_button => 'Начать бесплатный пробный период';

  @override
  String get paywall_premium_badge => 'Премиум доступ';

  @override
  String get paywall_step1_headline =>
      'Раскройте полный потенциал вашего ребенка';

  @override
  String get paywall_value_1_title => '15 премиум-игр';

  @override
  String get paywall_value_1_description =>
      'Доступ ко всем обучающим играм для детей 2-5 лет: формы, цвета, числа, логика и многое другое';

  @override
  String get paywall_value_2_title => 'Отслеживание прогресса';

  @override
  String get paywall_value_2_description =>
      'Подробная аналитика, показывающая развитие вашего ребенка и улучшение навыков со временем';

  @override
  String get paywall_value_3_title => 'Персонализированное обучение';

  @override
  String get paywall_value_3_description =>
      'Игры адаптируются к возрасту и уровню навыков вашего ребенка для оптимального обучения';

  @override
  String get paywall_value_4_title => 'Новый контент ежемесячно';

  @override
  String get paywall_value_4_description =>
      'Регулярные обновления с новыми играми и активностями для свежего и увлекательного обучения';

  @override
  String get paywall_step1_cta => 'Посмотреть планы';

  @override
  String get paywall_secure_payment => 'Безопасная обработка платежей';

  @override
  String get paywall_trial_badge => '7-дневный бесплатный пробный период';

  @override
  String get paywall_step2_headline => 'Выберите ваш план';

  @override
  String get paywall_step2_subheadline =>
      'Начните 7-дневный бесплатный пробный период. Отмените в любое время.';

  @override
  String get paywall_plan_best_value => 'Лучшая цена';

  @override
  String get paywall_plan_yearly_title => 'Ежегодно';

  @override
  String get paywall_plan_yearly_period => '/год';

  @override
  String get paywall_plan_yearly_per_month => 'Всего 3,33 €/месяц';

  @override
  String get paywall_plan_yearly_savings =>
      'Экономия 63% по сравнению с ежемесячным';

  @override
  String get paywall_plan_monthly_title => 'Ежемесячно';

  @override
  String get paywall_plan_monthly_period => '/месяц';

  @override
  String get paywall_plan_weekly_title => 'Еженедельно';

  @override
  String get paywall_plan_weekly_period => '/неделя';

  @override
  String get paywall_plan_weekly_note => 'Для краткосрочного доступа';

  @override
  String get paywall_trial_reminder =>
      'Ваш бесплатный пробный период начинается сегодня. Оплата начнется на 8-й день. Отмените в любое время до этого без оплаты.';

  @override
  String get paywall_step2_cta => 'Продолжить';

  @override
  String get paywall_terms =>
      'Продолжая, вы соглашаетесь с нашими Условиями использования и Политикой конфиденциальности';

  @override
  String get paywall_urgency_text => 'Ограниченное предложение';

  @override
  String get paywall_step3_headline => 'Вы в одном шаге!';

  @override
  String get paywall_step3_included_title => 'Все, что вы получите:';

  @override
  String get paywall_included_1 => 'Все 15 премиум обучающих игр';

  @override
  String get paywall_included_2 =>
      'Персонализированные траектории обучения для вашего ребенка';

  @override
  String get paywall_included_3 =>
      'Подробное отслеживание прогресса и аналитика';

  @override
  String get paywall_included_4 => 'Новый контент и активности каждый месяц';

  @override
  String get paywall_included_5 =>
      'Опыт без рекламы для сосредоточенного обучения';

  @override
  String get paywall_included_6 =>
      'Офлайн-режим - учитесь где угодно, когда угодно';

  @override
  String get paywall_guarantee_title => '100% гарантия без риска';

  @override
  String get paywall_guarantee_text =>
      'Попробуйте бесплатно 7 дней. Если не удовлетворены, отмените до окончания пробного периода и не платите ничего. Без вопросов.';

  @override
  String get paywall_step3_cta => 'Начать мой бесплатный пробный период';

  @override
  String get pre_paywall_headline => 'Your Learning Journey is Ready!';

  @override
  String pre_paywall_subheadline_personalized(String name) {
    return 'Here\'s what we\'ve prepared for $name:';
  }

  @override
  String pre_paywall_subheadline_age(String age) {
    return 'Here\'s what we\'ve prepared for your $age-year-old:';
  }

  @override
  String get pre_paywall_subheadline_generic =>
      'Here\'s what we\'ve prepared for your child:';

  @override
  String get pre_paywall_card_1_title => 'Age-Appropriate Content';

  @override
  String pre_paywall_card_1_subtitle_age(String age) {
    return 'Perfect for $age years old';
  }

  @override
  String get pre_paywall_card_1_subtitle_generic =>
      'All 15 games work great for ages 2-5';

  @override
  String get pre_paywall_card_2_title => 'Learning Focus';

  @override
  String get pre_paywall_card_2_subtitle => 'Building skills through play';

  @override
  String get pre_paywall_card_3_title => '15 Educational Games';

  @override
  String get pre_paywall_card_3_subtitle =>
      'Shapes, colors, patterns, animals & more';

  @override
  String get pre_paywall_key_benefit =>
      'Transform screen time from guilt into growth—educational content you can feel good about.';

  @override
  String get pre_paywall_trust_1 => 'Teacher Approved';

  @override
  String get pre_paywall_trust_2 => 'Ad-Free';

  @override
  String get pre_paywall_trust_3 => 'Expert-Backed';

  @override
  String get pre_paywall_cta_primary => 'Start 7-Day FREE Trial';

  @override
  String get pre_paywall_cta_primary_subtext =>
      'Unlock all 15 games • No charge today';

  @override
  String get pre_paywall_cta_secondary => 'Continue with 5 free games';

  @override
  String get pre_paywall_important_note =>
      'Trial includes all 15 games. After trial: Keep 5 free games or subscribe for full access.';

  @override
  String get onboarding_rotate_to_landscape =>
      'Поверните устройство в горизонтальное положение';

  @override
  String get onboarding_rotate_to_portrait =>
      'Поверните устройство в вертикальное положение';

  @override
  String get demo_game_1_title => 'Формы животных';

  @override
  String get demo_game_1_context =>
      'Помогите ребенку сопоставлять животных с их формами! Это развивает навыки визуального распознавания, необходимые для чтения.';

  @override
  String get demo_game_2_title => 'Игра на память';

  @override
  String get demo_game_2_context =>
      'Находите пары для укрепления рабочей памяти — важно для следования инструкциям и решения задач.';

  @override
  String get demo_game_3_title => 'Логические головоломки';

  @override
  String get demo_game_3_context =>
      'Решайте головоломки для развития логического мышления и распознавания паттернов.';

  @override
  String get demo_game_4_title => 'Весёлые паттерны';

  @override
  String get demo_game_4_context =>
      'Распознавайте паттерны для подготовки к математике и навыков последовательности.';

  @override
  String get demo_game_5_title => 'Исследователь мира';

  @override
  String get demo_game_5_context =>
      'Исследуйте мир для расширения словарного запаса и культурного кругозора.';

  @override
  String get demo_game_congratulations => 'Потрясающе! Вы получили значок!';

  @override
  String paywall_benefit_all_games(int gameCount) {
    return 'Доступ ко всем $gameCount играм';
  }

  @override
  String get paywall_benefit_age_appropriate =>
      'Контент, соответствующий возрасту';

  @override
  String get paywall_benefit_progress_tracking => 'Отслеживание прогресса';

  @override
  String get paywall_benefit_offline_play => 'Поддержка офлайн-режима';

  @override
  String get paywall_benefit_no_ads => 'Без рекламы, безопасно для детей';

  @override
  String get paywall_benefit_regular_updates => 'Регулярные обновления';

  @override
  String get paywall_start_trial => 'Начать бесплатный пробный период';

  @override
  String get paywall_step3_benefit_1 => 'Полный доступ ко всем обучающим играм';

  @override
  String get paywall_step3_benefit_2 => 'Безопасная среда обучения без рекламы';

  @override
  String get paywall_step3_benefit_3 => 'Идеально для всей семьи';

  @override
  String get paywall_subscribe_button => 'Оформить подписку';

  @override
  String get trial_explanation_headline => 'Try All 15 Games Free for 7 Days';

  @override
  String get trial_explanation_feature_1 =>
      'Full access to all 15 educational games';

  @override
  String get trial_explanation_feature_2 => 'No charge for 7 days';

  @override
  String get trial_explanation_feature_3 =>
      'We\'ll remind you 2 days before trial ends';

  @override
  String get trial_explanation_feature_4 =>
      'Cancel anytime during trial - no cost';

  @override
  String get trial_explanation_subtext =>
      'You won\'t be charged until day 8 of your trial';

  @override
  String get trial_explanation_cta => 'See Plans';

  @override
  String get unified_paywall_headline => 'Choose Your Plan';

  @override
  String get unified_paywall_subheadline =>
      'All plans unlock 15 educational games';

  @override
  String get unified_paywall_yearly_badge_save => 'Save 60%';

  @override
  String get unified_paywall_yearly_badge_trial => '7-Day Free Trial';

  @override
  String get unified_paywall_yearly_title => 'Yearly';

  @override
  String unified_paywall_yearly_price_breakdown(String monthlyEquivalent) {
    return 'Just $monthlyEquivalent/month';
  }

  @override
  String get unified_paywall_yearly_savings => 'Save 63% • Try free for 7 days';

  @override
  String get unified_paywall_yearly_feature_1 => 'No charge for 7 days';

  @override
  String get unified_paywall_yearly_feature_2 => 'Cancel anytime during trial';

  @override
  String get unified_paywall_yearly_feature_3 => 'Full access to all 15 games';

  @override
  String get unified_paywall_yearly_button => 'Start Free Trial';

  @override
  String get unified_paywall_monthly_title => 'Monthly';

  @override
  String unified_paywall_monthly_per_week(String weeklyEquivalent) {
    return '$weeklyEquivalent/week';
  }

  @override
  String get unified_paywall_monthly_savings => 'Flexible monthly plan';

  @override
  String get unified_paywall_monthly_feature_1 => 'Cancel anytime';

  @override
  String get unified_paywall_monthly_feature_2 => 'Full access to all 15 games';

  @override
  String get unified_paywall_monthly_button => 'Subscribe Monthly';

  @override
  String get unified_paywall_weekly_title => 'Weekly';

  @override
  String get unified_paywall_weekly_savings => 'Try for just one week';

  @override
  String get unified_paywall_weekly_feature_1 => 'Cancel anytime';

  @override
  String get unified_paywall_weekly_feature_2 => 'Full access to all 15 games';

  @override
  String get unified_paywall_weekly_button => 'Subscribe Weekly';

  @override
  String get unified_paywall_trust_1 => 'Secure payment processing';

  @override
  String get unified_paywall_trust_2 => 'Manage in App/Play Store';

  @override
  String get unified_paywall_trust_3 => 'All plans include full access';

  @override
  String get unified_paywall_restore => 'Restore Purchases';

  @override
  String get unified_paywall_terms =>
      'By continuing, you agree to our Terms of Service and Privacy Policy';

  @override
  String get educational_value_headline =>
      'Качественное экранное время, которое действительно помогает вашему ребенку';

  @override
  String get educational_value_point_1_title =>
      'Develops pattern recognition & logical thinking';

  @override
  String get educational_value_point_1_description =>
      'Essential skills for math readiness and problem-solving';

  @override
  String get educational_value_point_2_title =>
      'Strengthens visual discrimination skills';

  @override
  String get educational_value_point_2_description =>
      'Helps children identify differences and similarities';

  @override
  String get educational_value_point_3_title =>
      'Builds problem-solving abilities';

  @override
  String get educational_value_point_3_description =>
      'Active engagement develops critical thinking';

  @override
  String get educational_value_point_4_title =>
      'Designed for healthy screen time';

  @override
  String get educational_value_point_4_description =>
      'Aligned with pediatric recommendation of 1 hour daily';

  @override
  String get educational_value_research =>
      'Основано на исследованиях раннего детского развития';

  @override
  String get educational_value_research_source =>
      'Based on developmental psychology studies';

  @override
  String get value_carousel_1_headline =>
      'Развивайте когнитивные навыки через игру';

  @override
  String get value_carousel_1_description =>
      'Наши игры разработаны для укрепления памяти, решения задач и навыков критического мышления в идеальном темпе для возраста вашего ребенка.';

  @override
  String get value_carousel_1_subtext =>
      'Not just entertainment—actual learning in every match.';

  @override
  String get value_carousel_1_benefit_1_title => 'Развитие памяти';

  @override
  String get value_carousel_1_benefit_1_description =>
      'Игры на сопоставление, укрепляющие запоминание и распознавание';

  @override
  String get value_carousel_1_benefit_2_title => 'Решение задач';

  @override
  String get value_carousel_1_benefit_2_description =>
      'Головоломки, поощряющие логическое мышление и стратегию';

  @override
  String get value_carousel_1_benefit_3_title => 'Прогрессивное обучение';

  @override
  String get value_carousel_1_benefit_3_description =>
      'Сложность адаптируется по мере освоения новых навыков';

  @override
  String get value_carousel_2_headline =>
      'Экранное время, о котором можно не беспокоиться';

  @override
  String get value_carousel_2_description =>
      'В отличие от пассивного развлечения, Brainy Bunny активно вовлекает ум вашего ребенка в безопасной среде без рекламы.';

  @override
  String get value_carousel_2_category_1 => 'Shapes & Geometry';

  @override
  String get value_carousel_2_category_2 => 'Colors & Patterns';

  @override
  String get value_carousel_2_category_3 => 'Animals & Nature';

  @override
  String get value_carousel_2_category_4 => 'Professions & Roles';

  @override
  String get value_carousel_2_category_5 => 'Cause & Effect';

  @override
  String get value_carousel_2_category_6 => 'And more...';

  @override
  String get value_carousel_2_feature_1_title => '100% без рекламы';

  @override
  String get value_carousel_2_feature_1_description =>
      'Без рекламы, без отвлечений, без неподходящего контента';

  @override
  String get value_carousel_2_feature_2_title => 'Соответствует возрасту';

  @override
  String get value_carousel_2_feature_2_description =>
      'Контент специально разработан для детей 2-5 лет';

  @override
  String get value_carousel_2_feature_3_title => 'Активное обучение';

  @override
  String get value_carousel_2_feature_3_description =>
      'Увлекательные занятия, а не пассивный просмотр';

  @override
  String get value_carousel_3_headline =>
      'Отслеживайте прогресс и поддерживайте интерес';

  @override
  String get value_carousel_3_trust_element_1 => 'Ad-free learning environment';

  @override
  String get value_carousel_3_trust_element_2 => 'No data collection';

  @override
  String get value_carousel_3_trust_element_3 =>
      'Based on child development expert recommendations';

  @override
  String get value_carousel_3_trust_element_4 => 'Safe for young children';

  @override
  String get value_carousel_3_feature_1_title => 'Отслеживание прогресса';

  @override
  String get value_carousel_3_feature_1_description =>
      'Смотрите, какие навыки развивает ваш ребенок';

  @override
  String get value_carousel_3_feature_2_title => 'Награды за достижения';

  @override
  String get value_carousel_3_feature_2_description =>
      'Получайте звёзды и значки, которые мотивируют продолжать обучение';

  @override
  String get value_carousel_3_feature_3_title => 'Персонализированный опыт';

  @override
  String get value_carousel_3_feature_3_description =>
      'Игры адаптируются к уровню навыков вашего ребенка';

  @override
  String get summary_headline_new =>
      'Отлично! Вот ваша персональная траектория обучения';

  @override
  String get summary_learning_path_title => 'Что будет изучать ваш ребенок:';

  @override
  String get summary_skill_cognitive => 'Когнитивное развитие';

  @override
  String summary_skill_cognitive_desc(int age) {
    return 'Игры на память и активности для решения задач, идеальные для $age лет';
  }

  @override
  String get summary_skill_visual => 'Визуальное восприятие';

  @override
  String summary_skill_visual_desc(int age) {
    return 'Игры на распознавание форм и пространственное восприятие для $age-летних';
  }

  @override
  String get summary_skill_exploration => 'Исследование и открытия';

  @override
  String summary_skill_exploration_desc(int age) {
    return 'Интерактивные игры, поощряющие любопытство в $age лет';
  }

  @override
  String get summary_next_step =>
      'Далее: Попробуйте Премиум бесплатно 7 дней, чтобы открыть все игры!';

  @override
  String get trial_badge => '7-дневный бесплатный пробный период';

  @override
  String get trial_headline => 'Попробуйте Премиум бесплатно 7 дней';

  @override
  String get trial_description =>
      'Получите полный доступ ко всем премиум-играм и функциям. Отмените в любое время во время пробного периода — без оплаты при отмене до его окончания.';

  @override
  String get trial_feature_1_title => 'Все премиум-игры';

  @override
  String get trial_feature_1_description =>
      'Доступ ко всем обучающим играм в нашей библиотеке';

  @override
  String get trial_feature_2_title => 'Опыт без рекламы';

  @override
  String get trial_feature_2_description =>
      'Безопасная среда обучения без рекламы';

  @override
  String get trial_feature_3_title => 'Отслеживание прогресса';

  @override
  String get trial_feature_3_description =>
      'Отслеживайте развитие и достижения вашего ребенка';

  @override
  String get trial_feature_4_title => 'Регулярные обновления';

  @override
  String get trial_feature_4_description =>
      'Новые игры и функции добавляются регулярно';

  @override
  String get trial_how_it_works_title => 'Как это работает:';

  @override
  String get trial_step_1 =>
      'Начните бесплатный 7-дневный пробный период сегодня';

  @override
  String get trial_step_2 =>
      'Наслаждайтесь полным доступом ко всем премиум-функциям';

  @override
  String get trial_step_3 =>
      'Отмените в любое время — без оплаты до окончания пробного периода';

  @override
  String get trial_cta => 'Начать мой бесплатный пробный период';

  @override
  String get trial_disclaimer =>
      'Бесплатно 7 дней, затем тариф выбранного плана. Отмените в любое время.';

  @override
  String get notification_permission_headline =>
      'Оставайтесь на связи с обучением вашего ребенка';

  @override
  String get notification_permission_description =>
      'Получайте полезные напоминания и отмечайте вехи вместе с ребенком. Мы будем отправлять своевременные уведомления о достижениях и возможностях обучения.';

  @override
  String get notification_benefit_1_title => 'Напоминания о пробном периоде';

  @override
  String get notification_benefit_1_description =>
      'Получайте уведомления до окончания пробного периода, чтобы не потерять доступ';

  @override
  String get notification_benefit_2_title => 'Вехи обучения';

  @override
  String get notification_benefit_2_description =>
      'Отмечайте, когда ваш ребенок достигает новых достижений';

  @override
  String get notification_benefit_3_title => 'Советы по вовлечению';

  @override
  String get notification_benefit_3_description =>
      'Получайте предложения по поддержанию обучения весёлым и увлекательным';

  @override
  String get notification_privacy_note =>
      'Мы уважаем вашу конфиденциальность. Вы можете отключить уведомления в любое время в настройках.';

  @override
  String get notification_enable_button => 'Включить уведомления';

  @override
  String get notification_maybe_later => 'Возможно, позже';

  @override
  String get subscription_management_title => 'Управление подпиской';

  @override
  String get subscription_status_active => 'Премиум активен';

  @override
  String get subscription_status_active_description =>
      'У вас есть полный доступ ко всем премиум-функциям';

  @override
  String get subscription_status_inactive => 'Бесплатная версия';

  @override
  String get subscription_status_inactive_description =>
      'Обновитесь до премиума для полного доступа ко всем играм';

  @override
  String get subscription_actions_title => 'Действия';

  @override
  String get subscription_restore_title => 'Восстановить покупки';

  @override
  String get subscription_restore_description =>
      'Уже подписались на другом устройстве? Восстановите покупки здесь.';

  @override
  String get subscription_restore_button => 'Восстановить';

  @override
  String get subscription_manage_title => 'Управление подпиской';

  @override
  String get subscription_manage_description =>
      'Просмотр, изменение или отмена подписки через учётную запись магазина приложений.';

  @override
  String get subscription_manage_button => 'Открыть настройки подписки';

  @override
  String get subscription_help_title => 'Справка и информация';

  @override
  String get subscription_cancel_title => 'Как отменить';

  @override
  String get subscription_cancel_description =>
      'Вы можете отменить подписку в любое время через настройки учётной записи App Store или Google Play. Доступ сохранится до конца расчётного периода.';

  @override
  String get subscription_payment_failure_title => 'Проблемы с оплатой';

  @override
  String get subscription_payment_failure_description =>
      'Если платёж не прошёл, обновите способ оплаты в учётной записи магазина приложений. Мы автоматически повторим попытку оплаты.';

  @override
  String get next_button => 'Далее';

  @override
  String get back_button => 'Назад';

  @override
  String get onboarding_priority_question => 'What\'s most important to you?';

  @override
  String get onboarding_priority_1 => 'Educational screen time';

  @override
  String get onboarding_priority_1_sub => 'Learning while playing';

  @override
  String get onboarding_priority_2 => 'School readiness';

  @override
  String get onboarding_priority_2_sub =>
      'Preparing for preschool/kindergarten';

  @override
  String get onboarding_priority_3 => 'Keeping them engaged';

  @override
  String get onboarding_priority_3_sub => 'Productive, happy screen time';

  @override
  String get onboarding_priority_4 => 'Learning through play';

  @override
  String get onboarding_priority_4_sub => 'Fun that builds skills';

  @override
  String get onboarding_transition_message => 'Thank you for sharing!';

  @override
  String onboarding_transition_submessage(int age) {
    return 'We\'re creating the perfect experience for your $age-year-old...';
  }

  @override
  String summary_result_headline(int age) {
    return 'Perfect for your $age-year old!';
  }

  @override
  String get summary_card_1_title => 'What They\'ll Learn:';

  @override
  String get summary_card_1_point_1 =>
      'Pattern recognition through 15 different games';

  @override
  String get summary_card_1_point_2 => 'Visual discrimination skills';

  @override
  String get summary_card_1_point_3 => 'Logical thinking and problem-solving';

  @override
  String get summary_card_1_point_4 =>
      'Real-world connections (animals, professions, nature)';

  @override
  String get summary_card_2_title => 'Why It Works:';

  @override
  String get summary_card_2_point_1 => 'Active learning beats passive watching';

  @override
  String get summary_card_2_point_2 => 'Immediate feedback keeps them engaged';

  @override
  String get summary_card_2_point_3 => 'Variety prevents boredom';

  @override
  String get summary_card_2_point_4 => 'Aligned with screen time guidelines';

  @override
  String get summary_cta => 'See what\'s included';

  @override
  String get free_trial_headline => 'Try 5 games free, unlock 10 more';

  @override
  String get free_trial_free_section => 'Start with 5 free matching games';

  @override
  String get free_trial_free_point_1 => 'Shapes, colors, and more';

  @override
  String get free_trial_free_point_2 => 'No time limit';

  @override
  String get free_trial_free_point_3 => 'No credit card needed';

  @override
  String get free_trial_premium_section =>
      'Unlock all 15 games with a subscription';

  @override
  String get free_trial_premium_point_1 => 'Full game library';

  @override
  String get free_trial_premium_point_2 => 'New learning categories';

  @override
  String get free_trial_premium_point_3 => 'Cancel anytime';

  @override
  String get free_trial_bottom_message =>
      'Try the free games first—unlock more anytime!';

  @override
  String get free_trial_cta_primary => 'Start with free games';

  @override
  String get free_trial_cta_secondary => 'See subscription options';

  @override
  String get paywall_section1_headline =>
      'Ready to unlock all 15 learning games?';

  @override
  String get paywall_section1_feature_1 => '15 educational matching games';

  @override
  String get paywall_section1_feature_2 =>
      'Shape, color, pattern & logic games';

  @override
  String get paywall_section1_feature_3 =>
      'Animals, professions, nature themes';

  @override
  String get paywall_section1_feature_4 => 'Cause-and-effect learning';

  @override
  String get paywall_section1_feature_5 => 'No ads, no distractions';

  @override
  String get paywall_section1_feature_6 => 'Teacher Approved';

  @override
  String get paywall_section2_badge => 'Teacher Approved';

  @override
  String get paywall_section2_text =>
      'Educators recognize Brainy Bunny for its developmental approach to early learning.';

  @override
  String get paywall_section3_weekly_title => 'Weekly';

  @override
  String get paywall_section3_weekly_subtext => 'Try it out';

  @override
  String get paywall_section3_weekly_feature => 'Cancel anytime';

  @override
  String get paywall_section3_weekly_button => 'Subscribe';

  @override
  String get paywall_section3_yearly_title => 'Yearly';

  @override
  String get paywall_section3_yearly_badge => 'BEST VALUE - Save 60%';

  @override
  String get paywall_section3_yearly_highlight => '7-day FREE trial';

  @override
  String paywall_section3_yearly_breakdown(String monthlyEquivalent) {
    return 'Just $monthlyEquivalent/month';
  }

  @override
  String paywall_section3_yearly_fine_print(String yearlyPrice) {
    return 'Then $yearlyPrice annually';
  }

  @override
  String get paywall_section3_yearly_feature =>
      'Cancel during trial - no charge';

  @override
  String get paywall_section3_yearly_button => 'Start FREE trial';

  @override
  String get paywall_section3_monthly_title => 'Monthly';

  @override
  String get paywall_section3_monthly_subtext => 'Flexible option';

  @override
  String get paywall_section3_monthly_feature => 'Cancel anytime';

  @override
  String get paywall_section3_monthly_button => 'Subscribe';

  @override
  String get paywall_trust_element_1 => 'Secure payment';

  @override
  String get paywall_trust_element_2 => 'Cancel anytime during trial';

  @override
  String get paywall_trust_element_3 =>
      'Manage subscription in App Store/Play Store';

  @override
  String get paywall_trust_element_4 =>
      'Charged only after trial ends (for yearly)';

  @override
  String get paywall_disclaimer =>
      'You won\'t be charged during your 7-day trial. Cancel anytime in your device settings.';

  @override
  String get paywall_continue_free_link => 'Continue with free games';

  @override
  String get parent_gate_title => 'Parent Verification Required';

  @override
  String get parent_gate_instruction =>
      'This purchase requires an adult. Please solve this problem:';

  @override
  String get parent_gate_input_placeholder => 'Enter answer';

  @override
  String get parent_gate_cancel => 'Cancel';

  @override
  String get parent_gate_verify => 'Verify';

  @override
  String get parent_gate_error => 'Incorrect answer. Please try again.';

  @override
  String get notification_type_1 => 'New game unlocks';

  @override
  String get notification_type_2 => 'Learning streak milestones';

  @override
  String get notification_type_3 => 'Trial ending reminder (if applicable)';

  @override
  String get notification_type_4 => 'Daily learning encouragement';

  @override
  String get notification_trial_callout =>
      'We\'ll remind you 2 days before your trial ends, so you\'re never surprised.';

  @override
  String get notification_benefit_1 => 'Stay consistent with learning';

  @override
  String get notification_benefit_2 => 'Never miss trial deadline';

  @override
  String get notification_benefit_3 => 'Celebrate progress together';

  @override
  String get notification_cta_enable => 'Enable notifications';

  @override
  String get notification_cta_skip => 'Not now';

  @override
  String get subscription_error_loading_title => 'Loading Subscriptions';

  @override
  String get subscription_error_loading_description =>
      'Please wait while we load subscription options...';

  @override
  String get subscription_error_offline_title => 'No Internet Connection';

  @override
  String get subscription_error_offline_description =>
      'Please check your internet connection and try again. You need to be online to subscribe.';

  @override
  String get subscription_error_not_available_title =>
      'Subscriptions Not Available';

  @override
  String get subscription_error_not_available_description =>
      'In-app purchases are not available on this device. Please try again later or contact support.';

  @override
  String get subscription_error_products_not_found_title =>
      'Products Not Available';

  @override
  String get subscription_error_products_not_found_description =>
      'We couldn\'t load subscription products from the store. Please try again later.';

  @override
  String get subscription_error_unknown_title => 'Something Went Wrong';

  @override
  String get subscription_error_unknown_description =>
      'An unexpected error occurred. Please try again.';

  @override
  String get subscription_error_retry => 'Try Again';

  @override
  String get subscription_error_continue_free => 'Continue with Free Games';

  @override
  String get subscription_loading => 'Loading...';

  @override
  String get goal_preschool_title => 'Prepare for preschool/kindergarten';

  @override
  String get goal_preschool_description => 'Building readiness skills';

  @override
  String get goal_cognitive_title => 'Develop cognitive abilities';

  @override
  String get goal_cognitive_description =>
      'Pattern recognition & problem-solving';

  @override
  String get goal_replace_screen_time_title => 'Replace passive screen time';

  @override
  String get goal_replace_screen_time_description =>
      'Active learning instead of videos';

  @override
  String get goal_keep_engaged_title => 'Keep them engaged & learning';

  @override
  String get goal_keep_engaged_description => 'Fun that actually builds skills';

  @override
  String get summary_age2_headline => 'Perfect for your 2-year-old explorer';

  @override
  String get summary_age2_card1_title => 'What they\'ll learn';

  @override
  String get summary_age2_card1_point1 =>
      'Basic shape recognition (circles, squares, triangles)';

  @override
  String get summary_age2_card1_point2 => 'Simple color matching';

  @override
  String get summary_age2_card1_point3 =>
      'Hand-eye coordination through drag-and-drop';

  @override
  String get summary_age2_card1_point4 => 'Cause and effect understanding';

  @override
  String get summary_age2_card2_title => 'Why it works for age 2';

  @override
  String get summary_age2_card2_point1 =>
      'Extra-large pieces perfect for tiny fingers';

  @override
  String get summary_age2_card2_point2 => 'Simple 1-2 pair matching to start';

  @override
  String get summary_age2_card2_point3 =>
      'Instant positive feedback builds confidence';

  @override
  String get summary_age2_card2_point4 =>
      'Sessions designed for 5-10 minute attention spans';

  @override
  String get summary_age3_headline => 'Designed for your curious 3-year-old';

  @override
  String get summary_age3_card1_title => 'What they\'ll learn';

  @override
  String get summary_age3_card1_point1 =>
      'Advanced shape recognition and sorting';

  @override
  String get summary_age3_card1_point2 => 'Pattern identification';

  @override
  String get summary_age3_card1_point3 => 'Color mixing and matching concepts';

  @override
  String get summary_age3_card1_point4 => 'Early problem-solving skills';

  @override
  String get summary_age3_card2_title => 'Why it works for age 3';

  @override
  String get summary_age3_card2_point1 =>
      'Progressive difficulty grows with their skills';

  @override
  String get summary_age3_card2_point2 =>
      'Builds on preschool learning concepts';

  @override
  String get summary_age3_card2_point3 =>
      'Celebrates small wins to boost motivation';

  @override
  String get summary_age3_card2_point4 => 'Perfect for emerging independence';

  @override
  String get summary_age4_headline => 'Tailored for your smart 4-year-old';

  @override
  String get summary_age4_card1_title => 'What they\'ll learn';

  @override
  String get summary_age4_card1_point1 => 'Complex pattern recognition';

  @override
  String get summary_age4_card1_point2 =>
      'Categorical thinking (animals, professions, objects)';

  @override
  String get summary_age4_card1_point3 => 'Spatial reasoning and relationships';

  @override
  String get summary_age4_card1_point4 =>
      'Pre-reading visual discrimination skills';

  @override
  String get summary_age4_card2_title => 'Why it works for age 4';

  @override
  String get summary_age4_card2_point1 =>
      'Challenges that match pre-K curriculum';

  @override
  String get summary_age4_card2_point2 =>
      'Multiple rounds build sustained focus';

  @override
  String get summary_age4_card2_point3 =>
      'Vocabulary expansion through themed games';

  @override
  String get summary_age4_card2_point4 => 'Prepares for kindergarten readiness';

  @override
  String get summary_age5_headline => 'Engaging games for your 5+ year-old';

  @override
  String get summary_age5_card1_title => 'What they\'ll learn';

  @override
  String get summary_age5_card1_point1 => 'Advanced categorization and sorting';

  @override
  String get summary_age5_card1_point2 => 'Abstract pattern completion';

  @override
  String get summary_age5_card1_point3 => 'Critical thinking and strategy';

  @override
  String get summary_age5_card1_point4 => 'Visual memory enhancement';

  @override
  String get summary_age5_card2_title => 'Why it works for age 5+';

  @override
  String get summary_age5_card2_point1 =>
      'Kindergarten-level cognitive challenges';

  @override
  String get summary_age5_card2_point2 =>
      'Builds confidence for school success';

  @override
  String get summary_age5_card2_point3 =>
      'Reinforces classroom learning at home';

  @override
  String get summary_age5_card2_point4 => 'Keeps advanced learners engaged';

  @override
  String get parental_gate_overlay_title => 'Parental Verification';

  @override
  String get parental_gate_overlay_instruction =>
      'Please solve this problem to continue:';

  @override
  String get error_purchase_verification_failed =>
      'Purchase verification failed. Please restart the app.';

  @override
  String get paywall_step2_badge_save => 'Save 60%';

  @override
  String get paywall_step2_badge_trial => '7-Day Trial';

  @override
  String get paywall_step2_yearly_title => 'Yearly';

  @override
  String paywall_step2_yearly_per_month(String price) {
    return 'Just $price/month';
  }

  @override
  String get paywall_step2_yearly_savings => 'Save 63% • Try free for 7 days';

  @override
  String get paywall_step2_yearly_feature1 => 'No charge for 7 days';

  @override
  String get paywall_step2_yearly_feature2 => 'Cancel anytime during trial';

  @override
  String get paywall_step2_yearly_feature3 =>
      'We\'ll remind you 2 days before trial ends';

  @override
  String get paywall_step2_yearly_feature4 => 'Full access to all 15 games';

  @override
  String get paywall_step2_yearly_button => 'Start FREE Trial';

  @override
  String get paywall_step2_monthly_title => 'Monthly';

  @override
  String paywall_step2_monthly_per_week(String price) {
    return '$price/week';
  }

  @override
  String get paywall_step2_monthly_savings => 'Flexible monthly plan';

  @override
  String get paywall_step2_monthly_feature1 => 'Cancel anytime';

  @override
  String get paywall_step2_monthly_feature2 => 'Full access to all 15 games';

  @override
  String get paywall_step2_monthly_button => 'Subscribe Monthly';

  @override
  String get paywall_step2_weekly_title => 'Weekly';

  @override
  String get paywall_step2_weekly_savings => 'Try for just one week';

  @override
  String get paywall_step2_weekly_feature1 => 'Cancel anytime';

  @override
  String get paywall_step2_weekly_feature2 => 'Full access to all 15 games';

  @override
  String get paywall_step2_weekly_button => 'Subscribe Weekly';

  @override
  String locked_game_headline_personalized(String childName) {
    return 'Unlock All Games for $childName!';
  }

  @override
  String get locked_game_headline_generic => 'Unlock All 15 Educational Games!';

  @override
  String locked_game_card1_title_age(int age) {
    return 'Perfect for Your $age-Year-Old';
  }

  @override
  String get locked_game_card1_title_generic => 'Age-Appropriate Learning';

  @override
  String get locked_game_card2_title => 'Building Cognitive Abilities';

  @override
  String locked_game_card3_title(int count) {
    return '$count More Games to Explore';
  }

  @override
  String get locked_game_card3_subtitle =>
      'Unlock the full collection of educational games';

  @override
  String get locked_game_age_skill_1_generic => 'Shape and color recognition';

  @override
  String get locked_game_age_skill_2_generic => 'Problem-solving abilities';

  @override
  String get locked_game_age_skill_3_generic => 'Hand-eye coordination';

  @override
  String get locked_game_age_skill_4_generic => 'Memory and focus';

  @override
  String get locked_game_age_skill_1_age2 => 'Basic shape recognition';

  @override
  String get locked_game_age_skill_2_age2 => 'Simple color matching';

  @override
  String get locked_game_age_skill_3_age2 => 'Hand-eye coordination';

  @override
  String get locked_game_age_skill_4_age2 => 'Cause and effect understanding';

  @override
  String get locked_game_age_skill_1_age3 => 'Advanced shape sorting';

  @override
  String get locked_game_age_skill_2_age3 => 'Pattern identification';

  @override
  String get locked_game_age_skill_3_age3 => 'Color mixing concepts';

  @override
  String get locked_game_age_skill_4_age3 => 'Early problem-solving';

  @override
  String get locked_game_age_skill_1_age4 => 'Complex pattern recognition';

  @override
  String get locked_game_age_skill_2_age4 => 'Categorical thinking';

  @override
  String get locked_game_age_skill_3_age4 => 'Spatial reasoning';

  @override
  String get locked_game_age_skill_4_age4 => 'Pre-reading visual skills';

  @override
  String get locked_game_age_skill_1_age5 => 'Advanced categorization';

  @override
  String get locked_game_age_skill_2_age5 => 'Abstract pattern completion';

  @override
  String get locked_game_age_skill_3_age5 => 'Critical thinking';

  @override
  String get locked_game_age_skill_4_age5 => 'Visual memory enhancement';

  @override
  String get locked_game_card2_content_default =>
      'Develop essential cognitive skills through play';

  @override
  String get locked_game_card2_content_school =>
      'Build skills for preschool and kindergarten success';

  @override
  String get locked_game_card2_content_cognitive =>
      'Enhance memory, focus, and problem-solving abilities';

  @override
  String get locked_game_card2_content_screentime =>
      'Quality educational content that parents can feel good about';

  @override
  String get locked_game_card2_content_engagement =>
      'Keep your child engaged with fun, educational activities';
}

// lib/onboarding/screens/pre_paywall_screen.dart
import 'package:flutter/material.dart';
import '../../flutter_gen/gen_l10n/app_localizations.dart';
import 'package:brainy_bunny/models/onboarding_state.dart';
import 'package:brainy_bunny/constants/app_constants.dart';

/// Pre-paywall screen shown before the free trial explanation
/// Purpose: Recap value, create momentum toward trial
class PrePaywallScreen extends StatefulWidget {
  final VoidCallback onStartTrial;
  final VoidCallback onTryFreeGames;
  final String? childName;
  final int? childAge;

  const PrePaywallScreen({
    super.key,
    required this.onStartTrial,
    required this.onTryFreeGames,
    this.childName,
    this.childAge,
  });

  @override
  State<PrePaywallScreen> createState() => _PrePaywallScreenState();
}

class _PrePaywallScreenState extends State<PrePaywallScreen> {
  int? _loadedChildAge;
  String? _loadedLearningGoal;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadOnboardingData();
  }

  Future<void> _loadOnboardingData() async {
    // Try to load onboarding state to get child's age and learning goal if not provided
    if (widget.childAge == null) {
      final onboardingState = await OnboardingState.load();
      setState(() {
        _loadedChildAge = onboardingState.childAge;
        _loadedLearningGoal = onboardingState.learningGoal;
        _isLoading = false;
      });
    } else {
      setState(() {
        _isLoading = false;
      });
    }
  }

  int? get effectiveChildAge => widget.childAge ?? _loadedChildAge;
  String? get effectiveLearningGoal => _loadedLearningGoal;

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final size = MediaQuery.of(context).size;

    if (_isLoading) {
      return const Scaffold(
        backgroundColor: Colors.white,
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 32),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // App icon centered
                Center(
                  child: Image.asset(
                    'assets/icon/icon.png',
                    width: 100,
                    height: 100,
                    fit: BoxFit.contain,
                  ),
                ),

                const SizedBox(height: 24),

                // Personalized Headline
                Text(
                  _getHeadline(),
                  style: const TextStyle(
                    fontSize: 26,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2C3E50),
                    height: 1.3,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 32),

                // Card 1: Perfect for Age X (Blue background)
                _buildBenefitCard(
                  icon: '',
                  title: _getCard1Title(),
                  items: _getCard1Items(),
                  backgroundColor: const Color(0xFFE3F2FD),
                ),

                const SizedBox(height: 16),

                // Card 2: Building Cognitive Abilities (Green background)
                _buildBenefitCard(
                  icon: '🎯',
                  title: l10n.locked_game_card2_title,
                  subtitle: _getCard2Content(effectiveLearningGoal),
                  items: [],
                  backgroundColor: const Color(0xFFE8F5E9),
                ),

                const SizedBox(height: 16),

                // Card 3: 10 More Games (Yellow background)
                _buildBenefitCard(
                  icon: '✨',
                  title: l10n.locked_game_card3_title(AppConstants.TOTAL_GAMES_COUNT - AppConstants.FREE_GAMES_COUNT),
                  subtitle: l10n.locked_game_card3_subtitle,
                  items: [],
                  backgroundColor: const Color(0xFFFFF9E6),
                ),

                const SizedBox(height: 24),

                // Trust badges
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Flexible(
                      child: _buildTrustBadge(l10n.pre_paywall_trust_1, Icons.verified, const Color(0xFF27AE60)),
                    ),
                    const SizedBox(width: 6),
                    Flexible(
                      child: _buildTrustBadge(l10n.pre_paywall_trust_2, Icons.block, const Color(0xFFE91E63)),
                    ),
                  ],
                ),

                const SizedBox(height: 32),

                // Primary CTA - Unlock All Games
                SizedBox(
                  width: size.width * 0.8,
                  child: ElevatedButton(
                    onPressed: widget.onStartTrial,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF4A90E2),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 18),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(28),
                      ),
                      elevation: 2,
                    ),
                    child: Text(
                      l10n.pre_paywall_cta_primary,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Secondary CTA - Continue with free games
                Center(
                  child: TextButton(
                    onPressed: widget.onTryFreeGames,
                    child: Text(
                      l10n.pre_paywall_cta_secondary,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade700,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ),
                ),

                const SizedBox(height: 16),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _getHeadline() {
    final l10n = AppLocalizations.of(context)!;
    // Use child's name if available
    if (widget.childName != null && widget.childName!.isNotEmpty) {
      // Use localized string with child's name
      return l10n.locked_game_headline_personalized(widget.childName!);
    } else {
      return l10n.locked_game_headline_generic;
    }
  }

  String _getCard1Title() {
    final l10n = AppLocalizations.of(context)!;
    final age = effectiveChildAge;
    if (age != null) {
      return l10n.locked_game_card1_title_age(age);
    } else {
      return l10n.locked_game_card1_title_generic;
    }
  }

  List<String> _getCard1Items() {
    final l10n = AppLocalizations.of(context)!;
    final age = effectiveChildAge;

    if (age == null) {
      return [
        l10n.locked_game_age_skill_1_generic,
        l10n.locked_game_age_skill_2_generic,
        l10n.locked_game_age_skill_3_generic,
        l10n.locked_game_age_skill_4_generic,
      ];
    }

    // Age-specific content
    if (age == 2) {
      return [
        l10n.locked_game_age_skill_1_age2,
        l10n.locked_game_age_skill_2_age2,
        l10n.locked_game_age_skill_3_age2,
        l10n.locked_game_age_skill_4_age2,
      ];
    } else if (age == 3) {
      return [
        l10n.locked_game_age_skill_1_age3,
        l10n.locked_game_age_skill_2_age3,
        l10n.locked_game_age_skill_3_age3,
        l10n.locked_game_age_skill_4_age3,
      ];
    } else if (age == 4) {
      return [
        l10n.locked_game_age_skill_1_age4,
        l10n.locked_game_age_skill_2_age4,
        l10n.locked_game_age_skill_3_age4,
        l10n.locked_game_age_skill_4_age4,
      ];
    } else {
      // 5+ years
      return [
        l10n.locked_game_age_skill_1_age5,
        l10n.locked_game_age_skill_2_age5,
        l10n.locked_game_age_skill_3_age5,
        l10n.locked_game_age_skill_4_age5,
      ];
    }
  }

  String _getCard2Content(String? learningGoal) {
    final l10n = AppLocalizations.of(context)!;

    // Use learning goal from onboarding if available
    if (learningGoal == null) {
      return l10n.locked_game_card2_content_default;
    }

    switch (learningGoal.toLowerCase()) {
      case 'school_readiness':
      case 'school readiness':
        return l10n.locked_game_card2_content_school;

      case 'cognitive':
      case 'cognitive development':
        return l10n.locked_game_card2_content_cognitive;

      case 'screen_time':
      case 'replace screen time':
        return l10n.locked_game_card2_content_screentime;

      case 'engagement':
      case 'keep engaged':
        return l10n.locked_game_card2_content_engagement;

      default:
        return l10n.locked_game_card2_content_default;
    }
  }

  Widget _buildBenefitCard({
    required String icon,
    required String title,
    String? subtitle,
    required List<String> items,
    required Color backgroundColor,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Icon and title
          Row(
            children: [
              if (icon.isNotEmpty) ...[
                Text(
                  icon,
                  style: const TextStyle(fontSize: 24),
                ),
                const SizedBox(width: 12),
              ],
              Expanded(
                child: Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2C3E50),
                  ),
                ),
              ),
            ],
          ),

          // Subtitle if provided
          if (subtitle != null) ...[
            const SizedBox(height: 12),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 15,
                color: Colors.grey.shade800,
                height: 1.4,
              ),
            ),
          ],

          // Items list with checkmarks
          if (items.isNotEmpty) ...[
            const SizedBox(height: 12),
            ...items.map((item) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '✓ ',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF2C3E50),
                    ),
                  ),
                  Expanded(
                    child: Text(
                      item,
                      style: TextStyle(
                        fontSize: 15,
                        color: Colors.grey.shade800,
                      ),
                    ),
                  ),
                ],
              ),
            )).toList(),
          ],
        ],
      ),
    );
  }

  Widget _buildTrustBadge(String text, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1.5,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14,
            color: color,
          ),
          const SizedBox(width: 4),
          Flexible(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 11,
                fontWeight: FontWeight.w600,
                color: color,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
        ],
      ),
    );
  }
}

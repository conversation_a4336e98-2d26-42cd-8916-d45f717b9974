// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for German (`de`).
class AppLocalizationsDe extends AppLocalizations {
  AppLocalizationsDe([String locale = 'de']) : super(locale);

  @override
  String get onboarding_welcome_headline => 'Willkommen bei Brainy Bunny';

  @override
  String get onboarding_welcome_subheading => 'Lernspiele für Kleinkinder';

  @override
  String get onboarding_welcome_cta => 'Beginnen';

  @override
  String get onboarding_name_headline => 'Personalisieren Sie Ihre Erfahrung';

  @override
  String get onboarding_name_hint => 'Ihr Name';

  @override
  String get onboarding_name_mom => 'Mama';

  @override
  String get onboarding_name_dad => 'Papa';

  @override
  String get onboarding_name_parent => 'Elternteil';

  @override
  String onboarding_name_greeting(String name) {
    return 'G<PERSON><PERSON><PERSON>ig, $name!';
  }

  @override
  String get onboarding_child_age_headline => 'Wie alt ist Ihr Kind?';

  @override
  String get onboarding_child_age_subtext =>
      'Alle Spiele funktionieren für Kinder von 2-5 Jahren. Dies hilft uns, altersgerechte Tipps zu geben.';

  @override
  String get onboarding_age_2 => '2 Jahre alt';

  @override
  String get onboarding_age_3 => '3 Jahre alt';

  @override
  String get onboarding_age_4 => '4 Jahre alt';

  @override
  String get onboarding_age_5_plus => '5+ Jahre alt';

  @override
  String get onboarding_philosophy_headline =>
      'Bildschirmzeit in Lernzeit verwandeln';

  @override
  String get onboarding_philosophy_aap =>
      'Abgestimmt mit AAP-Empfehlungen zur Bildschirmzeit';

  @override
  String get onboarding_philosophy_learning =>
      'Verwandeln Sie Bildschirmzeit in bedeutungsvolle Lernerfahrungen';

  @override
  String get onboarding_philosophy_skills =>
      'Entwickeln Sie kognitive Fähigkeiten durch spielbasiertes Lernen';

  @override
  String get onboarding_transition_to_games =>
      'Lass uns spielen! Gerät drehen →';

  @override
  String get onboarding_transition_from_games =>
      'Großartiges Lernen! Abschließen →';

  @override
  String get onboarding_solution_headline => 'Learning through play';

  @override
  String get onboarding_solution_description =>
      'While they match shapes and colors, they\'re building real cognitive skills.';

  @override
  String get onboarding_solution_benefit_engagement => 'Active engagement';

  @override
  String get onboarding_solution_benefit_pattern_recognition =>
      'Pattern recognition';

  @override
  String get onboarding_solution_benefit_cause_effect =>
      'Cause-and-effect thinking';

  @override
  String get onboarding_solution_benefit_screen_time =>
      'Aligned with 1-hour screen time guidelines';

  @override
  String get onboarding_solution_research_text =>
      'Research shows: Matching activities improve spatial reasoning...';

  @override
  String get onboarding_solution_research_source =>
      'Based on developmental psychology studies';

  @override
  String get onboarding_solution_cta => 'See how it works';

  @override
  String get onboarding_problem_headline =>
      'We understand your\nscreen time concerns';

  @override
  String get onboarding_problem_point_1_title => 'Passive video consumption';

  @override
  String get onboarding_problem_point_1_description =>
      'Hours of mindless watching with zero interaction or learning';

  @override
  String get onboarding_problem_point_1_statistic =>
      '6x higher risk of language delays';

  @override
  String get onboarding_problem_point_2_title => 'Addictive mechanics';

  @override
  String get onboarding_problem_point_2_description =>
      'Apps use dopamine-driven design patterns to create dependency and compulsive usage';

  @override
  String get onboarding_problem_point_2_statistic =>
      '89% of apps use addictive features';

  @override
  String get onboarding_problem_point_3_title =>
      'Inappropriate content exposure';

  @override
  String get onboarding_problem_point_3_description =>
      'Ads, violence, and age-inappropriate material in \'kids\' content';

  @override
  String get onboarding_problem_point_3_statistic =>
      '85% of kids\' apps contain ads';

  @override
  String get onboarding_problem_point_4_title => 'Attention & focus problems';

  @override
  String get onboarding_problem_point_4_description =>
      'Fast-paced content destroys ability to concentrate and learn';

  @override
  String get onboarding_problem_point_4_statistic =>
      '40% increase in ADHD symptoms';

  @override
  String get onboarding_problem_research_title => 'Scientific Evidence';

  @override
  String get onboarding_problem_research_text =>
      'Peer-reviewed studies confirm: excessive passive screen time causes brain changes, language delays, and attention problems in young children.';

  @override
  String get onboarding_problem_subtext =>
      'You\'re not alone. 89% of parents share these concerns.';

  @override
  String get onboarding_problem_cta => 'There\'s a better way';

  @override
  String get demo_game_1_skill => 'Visuelle Zuordnung und Formerkennung';

  @override
  String get demo_game_1_science =>
      'Das Zuordnen von Tieren zu ihren Silhouetten entwickelt visuelle Diskriminierung - die Fähigkeit, Unterschiede zwischen ähnlichen Formen zu erkennen. Diese Fähigkeit ist grundlegend für Buchstabenerkennung und Lesen.';

  @override
  String get demo_game_1_citation =>
      'Bornstein (1985) - Visuelle Verarbeitung und kategorisches Denken';

  @override
  String get demo_game_1_badge => 'Formen-Detektiv';

  @override
  String get demo_game_2_skill => 'Visuelles Gedächtnis und Aufmerksamkeit';

  @override
  String get demo_game_2_science =>
      'Das Finden passender Paare stärkt das Arbeitsgedächtnis - die Fähigkeit Ihres Kindes, Informationen zu behalten und zu manipulieren. Dies unterstützt direkt mathematische Problemlösung und das Befolgen mehrstufiger Anweisungen.';

  @override
  String get demo_game_2_citation =>
      'Gopnik & Meltzoff (1987) - Kategorisierung und kognitive Flexibilität';

  @override
  String get demo_game_2_badge => 'Gedächtnis-Meister';

  @override
  String get demo_game_3_skill => 'Logische Zuordnung und Kategorisierung';

  @override
  String get demo_game_3_science =>
      'Das Verbinden von Objekten mit ihren Benutzern lehrt Kategorisierung und logisches Denken. Ihr Kind lernt, dass Dinge aus Gründen zusammengehören - ein Schlüsselschritt zum Verständnis von Ursache und Wirkung.';

  @override
  String get demo_game_3_citation =>
      'Piaget (1952) - Präoperationale kognitive Entwicklung';

  @override
  String get demo_game_3_badge => 'Logik-Stern';

  @override
  String get demo_game_4_skill => 'Mustererkennung und Zuordnung';

  @override
  String get demo_game_4_science =>
      'Das Zuordnen von Mustern baut Mustererkennung auf - die Fähigkeit, Beziehungen zwischen Dingen zu sehen. Diese Fähigkeit sagt mathematischen Erfolg stark voraus und hilft Kindern, \'gleich\' vs. \'unterschiedlich\' zu verstehen.';

  @override
  String get demo_game_4_citation =>
      'Rittle-Johnson et al. (2019) - Musterfähigkeiten und Mathematik';

  @override
  String get demo_game_4_badge => 'Muster-Profi';

  @override
  String get demo_game_5_skill => 'Symbolisches Denken und reale Verbindungen';

  @override
  String get demo_game_5_science =>
      'Das Verbinden von Werkzeugen mit Berufen baut symbolisches Denken auf - das Verständnis, dass eine Sache eine andere repräsentieren kann. Dieses abstrakte Denken ist wesentlich für Sprache, Mathematik und Vorstellungskraft.';

  @override
  String get demo_game_5_citation =>
      'Vygotsky (1978) - Symbolische Repräsentation in der kognitiven Entwicklung';

  @override
  String get demo_game_5_badge => 'Welt-Entdecker';

  @override
  String get demo_game_next => 'Nächstes Spiel';

  @override
  String onboarding_summary_headline(String name) {
    return 'Ihr bisheriger Fortschritt, $name';
  }

  @override
  String onboarding_summary_age(int age) {
    return 'Perfekt für $age-Jährige';
  }

  @override
  String get onboarding_summary_skills => '5 Fähigkeiten geübt';

  @override
  String get onboarding_summary_screen_time =>
      'Gesunder Bildschirmzeit-Ansatz abgestimmt mit AAP-Richtlinien';

  @override
  String get onboarding_summary_cta => 'Ihren personalisierten Plan ansehen';

  @override
  String get trust_headline => 'Vertraut von Eltern und Pädagogen';

  @override
  String get trust_approved => 'Von Lehrern genehmigt';

  @override
  String get trust_cta => 'Vollständige Lernerfahrung freischalten';

  @override
  String get paywall_headline => 'Alle 15 Lernspiele freischalten';

  @override
  String get paywall_subheadline =>
      'Setzen Sie die Lernreise Ihres Kindes fort';

  @override
  String get paywall_feature_games =>
      '15 Lernspiele, die auf Schlüsselfertigkeiten abzielen';

  @override
  String get paywall_feature_progress => 'Fortschrittsverfolgung für Ihr Kind';

  @override
  String get paywall_feature_ad_free => 'Werbefreie, sichere Umgebung';

  @override
  String get paywall_feature_new_games => 'Monatlich neue Spiele hinzugefügt';

  @override
  String get paywall_feature_ages => 'Entwickelt für 2-5 Jahre';

  @override
  String get paywall_trial_headline =>
      'Alle Funktionen 7 Tage KOSTENLOS testen';

  @override
  String get paywall_trial_price => 'Dann nur 0,87 €/Woche';

  @override
  String get paywall_trial_today => 'Heute: Vollzugriff freigeschaltet';

  @override
  String get paywall_trial_day5 => 'Tag 5: Wir senden Ihnen eine Erinnerung';

  @override
  String get paywall_trial_day7 =>
      'Tag 7: Abrechnung beginnt (vorher jederzeit kündbar)';

  @override
  String get paywall_trial_no_payment => '✓ Keine Zahlung jetzt fällig';

  @override
  String get paywall_trial_cta => 'Kostenlose Testversion starten →';

  @override
  String get paywall_weekly => 'Wöchentlich';

  @override
  String get paywall_monthly => 'Monatlich';

  @override
  String get paywall_yearly => 'Jährlich';

  @override
  String get paywall_save_60 => '60% sparen';

  @override
  String get paywall_most_popular => 'Beliebteste Wahl';

  @override
  String get paywall_cancel_anytime =>
      'Jederzeit in Ihren Geräteeinstellungen kündbar';

  @override
  String get paywall_restore => 'Käufe wiederherstellen';

  @override
  String get subscription_premium => 'Premium';

  @override
  String get subscription_trial => '7-Tage-Test';

  @override
  String get subscription_lifetime => 'Lebenslanger Zugriff';

  @override
  String get subscription_expired => 'Abonnement beendet';

  @override
  String get subscription_manage => 'Abonnement verwalten';

  @override
  String get subscription_cancel => 'Abonnement kündigen';

  @override
  String get subscription_change_plan => 'Plan ändern';

  @override
  String get continue_button => 'Fortsetzen';

  @override
  String get skip_button => 'Überspringen';

  @override
  String get close_button => 'Schließen';

  @override
  String get loading => 'Laden...';

  @override
  String get error_purchase_failed => 'Kauf fehlgeschlagen';

  @override
  String get error_purchase_failed_message =>
      'Wir konnten Ihren Kauf nicht abschließen. Bitte versuchen Sie es erneut.';

  @override
  String get error_restore_failed => 'Keine Käufe gefunden';

  @override
  String get error_restore_failed_message =>
      'Wir konnten keine früheren Käufe finden. Wenn Sie glauben, dies ist ein Fehler, wenden Sie sich bitte an den Support.';

  @override
  String get error_network => 'Netzwerkfehler';

  @override
  String get error_network_message =>
      'Bitte überprüfen Sie Ihre Internetverbindung und versuchen Sie es erneut.';

  @override
  String get summary_headline => 'Erstaunlicher Fortschritt!';

  @override
  String summary_message_age_2(Object name) {
    return 'Großartige Arbeit, $name! Ihr Kleines baut wichtige Fähigkeiten durch Spielen auf. Mit 2 Jahren stärkt jede Zuordnung ihre visuelle Erkennung und Problemlösungsfähigkeiten.';
  }

  @override
  String summary_message_age_3(Object name) {
    return 'Wunderbar, $name! Ihr Kind entwickelt entscheidende kognitive Fähigkeiten. Mit 3 Jahren verbessern diese Aktivitäten ihr Gedächtnis, ihre Konzentration und ihr logisches Denken.';
  }

  @override
  String summary_message_age_4(Object name) {
    return 'Ausgezeichnete Arbeit, $name! Ihr 4-Jähriger meistert fortgeschrittene Problemlösung. Diese Spiele bereiten ihn auf Kindergartenerfolg vor.';
  }

  @override
  String summary_message_age_5_plus(Object name) {
    return 'Fantastisch, $name! Ihr Kind zeichnet sich durch komplexes Denken aus. Diese Fähigkeiten werden ihnen eine starke Grundlage für die Schule geben.';
  }

  @override
  String get summary_badges_earned => 'Verdiente Abzeichen';

  @override
  String get summary_badges => 'Abzeichen';

  @override
  String get summary_games => 'Spiele';

  @override
  String get summary_skills => 'Fähigkeiten';

  @override
  String get trust_aap_description =>
      'Unser pädagogischer Ansatz entspricht den AAP-Richtlinien für gesunde Bildschirmzeit und frühkindliche Entwicklung.';

  @override
  String get trust_research_title => 'Forschungsbasierter Lehrplan';

  @override
  String get trust_research_description =>
      'Jedes Spiel ist basierend auf begutachteten Studien zur kognitiven Entwicklung konzipiert, mit bewährten Methoden zur Verbesserung des Lernens bei Kindern im Alter von 2-5 Jahren.';

  @override
  String get trust_testimonial_1_name => 'Sarah M., Mutter eines 3-Jährigen';

  @override
  String get trust_testimonial_1_quote =>
      '\"Meine Tochter hat sich von Schwierigkeiten mit Formen zu selbstbewusstem Erkennen überall entwickelt. Der Fortschritt in nur 2 Wochen hat mich verblüfft!\"';

  @override
  String get trust_testimonial_2_name => 'Michael T., Vater eines 4-Jährigen';

  @override
  String get trust_testimonial_2_quote =>
      '\"Endlich Bildschirmzeit, bei der ich mich gut fühle! Mein Sohn lernt beim Spaß haben, und ich kann echte kognitive Verbesserungen sehen.\"';

  @override
  String get trust_downloads_title =>
      'Schließen Sie sich über 100.000 Familien an';

  @override
  String get trust_downloads_description =>
      'Vertraut von Eltern in über 50 Ländern, um ihren Kindern einen Vorsprung beim Lernen zu geben.';

  @override
  String get trust_cta_headline =>
      'Bereit, das volle Potenzial Ihres Kindes freizusetzen?';

  @override
  String get trust_cta_button => 'Kostenlose Testversion starten';

  @override
  String get paywall_premium_badge => 'Premium-Zugang';

  @override
  String get paywall_step1_headline =>
      'Setzen Sie das volle Potenzial Ihres Kindes frei';

  @override
  String get paywall_value_1_title => '15 Premium-Spiele';

  @override
  String get paywall_value_1_description =>
      'Zugang zu allen Lernspielen für 2-5 Jahre, die Formen, Farben, Zahlen, Logik und mehr abdecken';

  @override
  String get paywall_value_2_title => 'Fortschrittsverfolgung';

  @override
  String get paywall_value_2_description =>
      'Detaillierte Analysen zeigen die Entwicklung Ihres Kindes und Fähigkeitsverbesserung im Laufe der Zeit';

  @override
  String get paywall_value_3_title => 'Personalisiertes Lernen';

  @override
  String get paywall_value_3_description =>
      'Spiele passen sich dem Alter und Fähigkeitsniveau Ihres Kindes für optimales Lernen an';

  @override
  String get paywall_value_4_title => 'Monatlich neuer Inhalt';

  @override
  String get paywall_value_4_description =>
      'Regelmäßige Updates mit neuen Spielen und Aktivitäten, um das Lernen frisch und ansprechend zu halten';

  @override
  String get paywall_step1_cta => 'Pläne ansehen';

  @override
  String get paywall_secure_payment => 'Sichere Zahlungsabwicklung';

  @override
  String get paywall_trial_badge => '7 Tage kostenlose Testversion';

  @override
  String get paywall_step2_headline => 'Wählen Sie Ihren Plan';

  @override
  String get paywall_step2_subheadline =>
      'Starten Sie Ihre 7-tägige kostenlose Testversion. Jederzeit kündbar.';

  @override
  String get paywall_plan_best_value => 'Bester Wert';

  @override
  String get paywall_plan_yearly_title => 'Jährlich';

  @override
  String get paywall_plan_yearly_period => '/Jahr';

  @override
  String get paywall_plan_yearly_per_month => 'Nur 3,33 €/Monat';

  @override
  String get paywall_plan_yearly_savings =>
      '63% Ersparnis im Vergleich zu monatlich';

  @override
  String get paywall_plan_monthly_title => 'Monatlich';

  @override
  String get paywall_plan_monthly_period => '/Monat';

  @override
  String get paywall_plan_weekly_title => 'Wöchentlich';

  @override
  String get paywall_plan_weekly_period => '/Woche';

  @override
  String get paywall_plan_weekly_note => 'Für kurzfristigen Zugang';

  @override
  String get paywall_trial_reminder =>
      'Ihre kostenlose Testversion beginnt heute. Sie werden erst am Tag 8 belastet. Vorher jederzeit kostenlos kündbar.';

  @override
  String get paywall_step2_cta => 'Fortsetzen';

  @override
  String get paywall_terms =>
      'Durch Fortfahren stimmen Sie unseren Nutzungsbedingungen und Datenschutzrichtlinien zu';

  @override
  String get paywall_urgency_text => 'Zeitlich begrenztes Angebot';

  @override
  String get paywall_step3_headline => 'Sie sind nur einen Schritt entfernt!';

  @override
  String get paywall_step3_included_title => 'Alles, was Sie bekommen:';

  @override
  String get paywall_included_1 => 'Alle 15 Premium-Lernspiele';

  @override
  String get paywall_included_2 => 'Personalisierte Lernpfade für Ihr Kind';

  @override
  String get paywall_included_3 =>
      'Detaillierte Fortschrittsverfolgung und Einblicke';

  @override
  String get paywall_included_4 => 'Monatlich neue Inhalte und Aktivitäten';

  @override
  String get paywall_included_5 =>
      'Werbefreie Erfahrung für fokussiertes Lernen';

  @override
  String get paywall_included_6 =>
      'Offline-Modus - lernen Sie überall, jederzeit';

  @override
  String get paywall_guarantee_title => '100% risikofreie Garantie';

  @override
  String get paywall_guarantee_text =>
      'Testen Sie es 7 Tage kostenlos. Wenn Sie nicht vollständig zufrieden sind, kündigen Sie vor Testende und zahlen nichts. Keine Fragen gestellt.';

  @override
  String get paywall_step3_cta => 'Meine kostenlose Testversion starten';

  @override
  String get pre_paywall_headline => 'Your Learning Journey is Ready!';

  @override
  String pre_paywall_subheadline_personalized(String name) {
    return 'Here\'s what we\'ve prepared for $name:';
  }

  @override
  String pre_paywall_subheadline_age(String age) {
    return 'Here\'s what we\'ve prepared for your $age-year-old:';
  }

  @override
  String get pre_paywall_subheadline_generic =>
      'Here\'s what we\'ve prepared for your child:';

  @override
  String get pre_paywall_card_1_title => 'Age-Appropriate Content';

  @override
  String pre_paywall_card_1_subtitle_age(String age) {
    return 'Perfect for $age years old';
  }

  @override
  String get pre_paywall_card_1_subtitle_generic =>
      'All 15 games work great for ages 2-5';

  @override
  String get pre_paywall_card_2_title => 'Learning Focus';

  @override
  String get pre_paywall_card_2_subtitle => 'Building skills through play';

  @override
  String get pre_paywall_card_3_title => '15 Educational Games';

  @override
  String get pre_paywall_card_3_subtitle =>
      'Shapes, colors, patterns, animals & more';

  @override
  String get pre_paywall_key_benefit =>
      'Transform screen time from guilt into growth—educational content you can feel good about.';

  @override
  String get pre_paywall_trust_1 => 'Teacher Approved';

  @override
  String get pre_paywall_trust_2 => 'Ad-Free';

  @override
  String get pre_paywall_trust_3 => 'Expert-Backed';

  @override
  String get pre_paywall_cta_primary => 'Start 7-Day FREE Trial';

  @override
  String get pre_paywall_cta_primary_subtext =>
      'Unlock all 15 games • No charge today';

  @override
  String get pre_paywall_cta_secondary => 'Continue with 5 free games';

  @override
  String get pre_paywall_important_note =>
      'Trial includes all 15 games. After trial: Keep 5 free games or subscribe for full access.';

  @override
  String get onboarding_rotate_to_landscape =>
      'Drehen Sie Ihr Gerät ins Querformat';

  @override
  String get onboarding_rotate_to_portrait =>
      'Drehen Sie Ihr Gerät ins Hochformat';

  @override
  String get demo_game_1_title => 'Tierformen';

  @override
  String get demo_game_1_context =>
      'Helfen Sie Ihrem Kind, Tiere ihren Formen zuzuordnen! Dies baut visuelle Erkennungsfähigkeiten auf, die für das Lesen wesentlich sind.';

  @override
  String get demo_game_2_title => 'Gedächtnis-Zuordnung';

  @override
  String get demo_game_2_context =>
      'Finden Sie passende Paare, um das Arbeitsgedächtnis zu stärken - entscheidend für das Befolgen von Anweisungen und Problemlösung.';

  @override
  String get demo_game_3_title => 'Logik-Rätsel';

  @override
  String get demo_game_3_context =>
      'Lösen Sie Rätsel, um logisches Denken und Mustererkennung zu entwickeln.';

  @override
  String get demo_game_4_title => 'Musterspaß';

  @override
  String get demo_game_4_context =>
      'Erkennen Sie Muster, um Mathematik-Bereitschaft und Sequenzierungsfähigkeiten aufzubauen.';

  @override
  String get demo_game_5_title => 'Welt-Entdecker';

  @override
  String get demo_game_5_context =>
      'Erkunden Sie die Welt, um Wortschatz und kulturelles Bewusstsein zu erweitern.';

  @override
  String get demo_game_congratulations =>
      'Erstaunlich! Sie haben ein Abzeichen verdient!';

  @override
  String paywall_benefit_all_games(int gameCount) {
    return 'Zugang zu allen $gameCount Spielen';
  }

  @override
  String get paywall_benefit_age_appropriate => 'Altersgerechter Inhalt';

  @override
  String get paywall_benefit_progress_tracking => 'Fortschrittsverfolgung';

  @override
  String get paywall_benefit_offline_play => 'Offline-Spiel unterstützt';

  @override
  String get paywall_benefit_no_ads => 'Keine Werbung, kindersicher';

  @override
  String get paywall_benefit_regular_updates => 'Regelmäßige Updates';

  @override
  String get paywall_start_trial => 'Kostenlose Testversion starten';

  @override
  String get paywall_step3_benefit_1 => 'Vollzugriff auf alle Lernspiele';

  @override
  String get paywall_step3_benefit_2 => 'Werbefreie, sichere Lernumgebung';

  @override
  String get paywall_step3_benefit_3 => 'Perfekt für die ganze Familie';

  @override
  String get paywall_subscribe_button => 'Jetzt abonnieren';

  @override
  String get trial_explanation_headline => 'Try All 15 Games Free for 7 Days';

  @override
  String get trial_explanation_feature_1 =>
      'Full access to all 15 educational games';

  @override
  String get trial_explanation_feature_2 => 'No charge for 7 days';

  @override
  String get trial_explanation_feature_3 =>
      'We\'ll remind you 2 days before trial ends';

  @override
  String get trial_explanation_feature_4 =>
      'Cancel anytime during trial - no cost';

  @override
  String get trial_explanation_subtext =>
      'You won\'t be charged until day 8 of your trial';

  @override
  String get trial_explanation_cta => 'See Plans';

  @override
  String get unified_paywall_headline => 'Choose Your Plan';

  @override
  String get unified_paywall_subheadline =>
      'All plans unlock 15 educational games';

  @override
  String get unified_paywall_yearly_badge_save => 'Save 60%';

  @override
  String get unified_paywall_yearly_badge_trial => '7-Day Free Trial';

  @override
  String get unified_paywall_yearly_title => 'Yearly';

  @override
  String unified_paywall_yearly_price_breakdown(String monthlyEquivalent) {
    return 'Just $monthlyEquivalent/month';
  }

  @override
  String get unified_paywall_yearly_savings => 'Save 63% • Try free for 7 days';

  @override
  String get unified_paywall_yearly_feature_1 => 'No charge for 7 days';

  @override
  String get unified_paywall_yearly_feature_2 => 'Cancel anytime during trial';

  @override
  String get unified_paywall_yearly_feature_3 => 'Full access to all 15 games';

  @override
  String get unified_paywall_yearly_button => 'Start Free Trial';

  @override
  String get unified_paywall_monthly_title => 'Monthly';

  @override
  String unified_paywall_monthly_per_week(String weeklyEquivalent) {
    return '$weeklyEquivalent/week';
  }

  @override
  String get unified_paywall_monthly_savings => 'Flexible monthly plan';

  @override
  String get unified_paywall_monthly_feature_1 => 'Cancel anytime';

  @override
  String get unified_paywall_monthly_feature_2 => 'Full access to all 15 games';

  @override
  String get unified_paywall_monthly_button => 'Subscribe Monthly';

  @override
  String get unified_paywall_weekly_title => 'Weekly';

  @override
  String get unified_paywall_weekly_savings => 'Try for just one week';

  @override
  String get unified_paywall_weekly_feature_1 => 'Cancel anytime';

  @override
  String get unified_paywall_weekly_feature_2 => 'Full access to all 15 games';

  @override
  String get unified_paywall_weekly_button => 'Subscribe Weekly';

  @override
  String get unified_paywall_trust_1 => 'Secure payment processing';

  @override
  String get unified_paywall_trust_2 => 'Manage in App/Play Store';

  @override
  String get unified_paywall_trust_3 => 'All plans include full access';

  @override
  String get unified_paywall_restore => 'Restore Purchases';

  @override
  String get unified_paywall_terms =>
      'By continuing, you agree to our Terms of Service and Privacy Policy';

  @override
  String get educational_value_headline =>
      'Lernen durch Spielen, nicht sinnlose Unterhaltung';

  @override
  String get educational_value_point_1_title =>
      'Entwickelt Mustererkennung und logisches Denken';

  @override
  String get educational_value_point_1_description =>
      'Wesentliche Fähigkeiten für Mathematik-Bereitschaft und Problemlösung';

  @override
  String get educational_value_point_2_title =>
      'Stärkt visuelle Diskriminierungsfähigkeiten';

  @override
  String get educational_value_point_2_description =>
      'Hilft Kindern, Unterschiede und Ähnlichkeiten zu erkennen';

  @override
  String get educational_value_point_3_title =>
      'Baut Problemlösungsfähigkeiten auf';

  @override
  String get educational_value_point_3_description =>
      'Aktives Engagement entwickelt kritisches Denken';

  @override
  String get educational_value_point_4_title =>
      'Entwickelt für gesunde Bildschirmzeit';

  @override
  String get educational_value_point_4_description =>
      'Abgestimmt mit der pädiatrischen Empfehlung von 1 Stunde täglich';

  @override
  String get educational_value_research =>
      'Forschung zeigt: Zuordnungsaktivitäten verbessern räumliches Denken und kognitive Entwicklung bei Kindern von 2-5 Jahren.';

  @override
  String get educational_value_research_source =>
      'Basierend auf entwicklungspsychologischen Studien';

  @override
  String get value_carousel_1_headline =>
      'Bildschirmzeit, die Fähigkeiten aufbaut';

  @override
  String get value_carousel_1_description =>
      '15 verschiedene Zuordnungsspiele, die Formen, Farben, Muster, Tiere, Berufe und Ursache-Wirkungs-Denken lehren.';

  @override
  String get value_carousel_1_subtext =>
      'Nicht nur Unterhaltung – echtes Lernen bei jeder Zuordnung.';

  @override
  String get value_carousel_1_benefit_1_title => 'Gedächtnisentwicklung';

  @override
  String get value_carousel_1_benefit_1_description =>
      'Zuordnungsspiele, die Erinnerung und Wiedererkennung stärken';

  @override
  String get value_carousel_1_benefit_2_title => 'Problemlösung';

  @override
  String get value_carousel_1_benefit_2_description =>
      'Rätsel, die logisches Denken und Strategie fördern';

  @override
  String get value_carousel_1_benefit_3_title => 'Progressives Lernen';

  @override
  String get value_carousel_1_benefit_3_description =>
      'Schwierigkeit passt sich an, während Ihr Kind neue Fähigkeiten meistert';

  @override
  String get value_carousel_2_headline => '15 Wege zum Lernen und Wachsen';

  @override
  String get value_carousel_2_description =>
      'Von einfacher Formzuordnung bis zum Verstehen von Beziehungen zwischen Objekten und Situationen – jedes Spiel zielt auf spezifische Entwicklungsfähigkeiten ab.';

  @override
  String get value_carousel_2_category_1 => 'Formen & Geometrie';

  @override
  String get value_carousel_2_category_2 => 'Farben & Muster';

  @override
  String get value_carousel_2_category_3 => 'Tiere & Natur';

  @override
  String get value_carousel_2_category_4 => 'Berufe & Rollen';

  @override
  String get value_carousel_2_category_5 => 'Ursache & Wirkung';

  @override
  String get value_carousel_2_category_6 => 'Und mehr...';

  @override
  String get value_carousel_2_feature_1_title => '100% werbefrei';

  @override
  String get value_carousel_2_feature_1_description =>
      'Keine Werbung, keine Ablenkungen, keine unangemessenen Inhalte';

  @override
  String get value_carousel_2_feature_2_title => 'Altersgerecht';

  @override
  String get value_carousel_2_feature_2_description =>
      'Inhalte speziell für 2-5 Jahre entwickelt';

  @override
  String get value_carousel_2_feature_3_title => 'Aktives Lernen';

  @override
  String get value_carousel_2_feature_3_description =>
      'Ansprechende Aktivitäten, nicht passives Zuschauen';

  @override
  String get value_carousel_3_headline =>
      'Von Lehrern genehmigt, von Eltern vertraut';

  @override
  String get value_carousel_3_trust_element_1 => 'Werbefreie Lernumgebung';

  @override
  String get value_carousel_3_trust_element_2 => 'Keine Datenerfassung';

  @override
  String get value_carousel_3_trust_element_3 => 'Von Pädagogen entwickelt';

  @override
  String get value_carousel_3_trust_element_4 => 'Sicher für junge Kinder';

  @override
  String get value_carousel_3_feature_1_title => 'Fortschrittsverfolgung';

  @override
  String get value_carousel_3_feature_1_description =>
      'Sehen Sie, welche Fähigkeiten Ihr Kind entwickelt';

  @override
  String get value_carousel_3_feature_2_title => 'Erfolgsbelohnungen';

  @override
  String get value_carousel_3_feature_2_description =>
      'Verdienen Sie Sterne und Abzeichen, die zum weiteren Lernen motivieren';

  @override
  String get value_carousel_3_feature_3_title => 'Personalisierte Erfahrung';

  @override
  String get value_carousel_3_feature_3_description =>
      'Spiele, die sich an das Fähigkeitsniveau Ihres Kindes anpassen';

  @override
  String get summary_headline_new =>
      'Perfekt! Hier ist Ihr personalisierter Lernpfad';

  @override
  String get summary_learning_path_title => 'Was Ihr Kind lernen wird:';

  @override
  String get summary_skill_cognitive => 'Kognitive Entwicklung';

  @override
  String summary_skill_cognitive_desc(int age) {
    return 'Gedächtnisspiele und Problemlösungsaktivitäten perfekt für $age Jahre';
  }

  @override
  String get summary_skill_visual => 'Visuelle Wahrnehmung';

  @override
  String summary_skill_visual_desc(int age) {
    return 'Formerkennung und räumliches Bewusstsein Spiele für $age-Jährige';
  }

  @override
  String get summary_skill_exploration => 'Erkundung und Entdeckung';

  @override
  String summary_skill_exploration_desc(int age) {
    return 'Interaktive Spiele, die Neugier im Alter von $age Jahren fördern';
  }

  @override
  String get summary_next_step =>
      'Weiter: Testen Sie Premium 7 Tage kostenlos, um alle Spiele freizuschalten!';

  @override
  String get trial_badge => '7 Tage kostenlose Testversion';

  @override
  String get trial_headline => 'Testen Sie Premium 7 Tage kostenlos';

  @override
  String get trial_description =>
      'Erhalten Sie vollen Zugriff auf alle Premium-Spiele und Funktionen. Jederzeit während Ihrer Testversion kündbar - keine Gebühren, wenn Sie vor Ende kündigen.';

  @override
  String get trial_feature_1_title => 'Alle Premium-Spiele';

  @override
  String get trial_feature_1_description =>
      'Zugriff auf jedes Lernspiel in unserer Bibliothek';

  @override
  String get trial_feature_2_title => 'Werbefreie Erfahrung';

  @override
  String get trial_feature_2_description => 'Sichere Lernumgebung ohne Werbung';

  @override
  String get trial_feature_3_title => 'Fortschrittsverfolgung';

  @override
  String get trial_feature_3_description =>
      'Sehen Sie die Entwicklung und Erfolge Ihres Kindes';

  @override
  String get trial_feature_4_title => 'Regelmäßige Updates';

  @override
  String get trial_feature_4_description =>
      'Neue Spiele und Funktionen regelmäßig hinzugefügt';

  @override
  String get trial_how_it_works_title => 'Wie es funktioniert:';

  @override
  String get trial_step_1 =>
      'Starten Sie heute Ihre 7-tägige kostenlose Testversion';

  @override
  String get trial_step_2 =>
      'Genießen Sie vollen Zugriff auf alle Premium-Funktionen';

  @override
  String get trial_step_3 => 'Jederzeit kündbar - keine Gebühren vor Testende';

  @override
  String get trial_cta => 'Meine kostenlose Testversion starten';

  @override
  String get trial_disclaimer =>
      '7 Tage kostenlos, dann gewählter Plantarif. Jederzeit kündbar.';

  @override
  String get notification_permission_headline =>
      'Bleiben Sie mit dem Lernen Ihres Kindes verbunden';

  @override
  String get notification_permission_description =>
      'Erhalten Sie hilfreiche Erinnerungen und feiern Sie Meilensteine mit Ihrem Kind. Wir senden Ihnen rechtzeitige Benachrichtigungen über Erfolge und Lernmöglichkeiten.';

  @override
  String get notification_benefit_1_title => 'Test-Erinnerungen';

  @override
  String get notification_benefit_1_description =>
      'Werden Sie benachrichtigt, bevor Ihre Testversion endet, damit Sie nie den Zugriff verlieren';

  @override
  String get notification_benefit_2_title => 'Lernmeilensteine';

  @override
  String get notification_benefit_2_description =>
      'Feiern Sie, wenn Ihr Kind neue Erfolge erreicht';

  @override
  String get notification_benefit_3_title => 'Engagement-Tipps';

  @override
  String get notification_benefit_3_description =>
      'Erhalten Sie Vorschläge, um das Lernen unterhaltsam und ansprechend zu halten';

  @override
  String get notification_privacy_note =>
      'Wir respektieren Ihre Privatsphäre. Sie können Benachrichtigungen jederzeit in den Einstellungen deaktivieren.';

  @override
  String get notification_enable_button => 'Benachrichtigungen aktivieren';

  @override
  String get notification_maybe_later => 'Vielleicht später';

  @override
  String get subscription_management_title => 'Abonnement verwalten';

  @override
  String get subscription_status_active => 'Premium aktiv';

  @override
  String get subscription_status_active_description =>
      'Sie haben vollen Zugriff auf alle Premium-Funktionen';

  @override
  String get subscription_status_inactive => 'Kostenlose Version';

  @override
  String get subscription_status_inactive_description =>
      'Upgraden Sie auf Premium für vollen Zugriff auf alle Spiele';

  @override
  String get subscription_actions_title => 'Aktionen';

  @override
  String get subscription_restore_title => 'Käufe wiederherstellen';

  @override
  String get subscription_restore_description =>
      'Bereits auf einem anderen Gerät abonniert? Stellen Sie Ihre Käufe hier wieder her.';

  @override
  String get subscription_restore_button => 'Wiederherstellen';

  @override
  String get subscription_manage_title => 'Verwalten Sie Ihr Abonnement';

  @override
  String get subscription_manage_description =>
      'Anzeigen, ändern oder kündigen Sie Ihr Abonnement über Ihr App Store-Konto.';

  @override
  String get subscription_manage_button => 'Abonnement-Einstellungen öffnen';

  @override
  String get subscription_help_title => 'Hilfe und Informationen';

  @override
  String get subscription_cancel_title => 'So kündigen Sie';

  @override
  String get subscription_cancel_description =>
      'Sie können Ihr Abonnement jederzeit über Ihre App Store- oder Google Play-Kontoeinstellungen kündigen. Sie behalten den Zugriff bis zum Ende Ihres Abrechnungszeitraums.';

  @override
  String get subscription_payment_failure_title => 'Zahlungsprobleme';

  @override
  String get subscription_payment_failure_description =>
      'Wenn Ihre Zahlung fehlschlägt, aktualisieren Sie Ihre Zahlungsmethode in Ihrem App Store-Konto. Wir versuchen die Zahlung automatisch erneut.';

  @override
  String get next_button => 'Weiter';

  @override
  String get back_button => 'Zurück';

  @override
  String get onboarding_priority_question => 'Was ist Ihnen am wichtigsten?';

  @override
  String get onboarding_priority_1 => 'Lehrreiche Bildschirmzeit';

  @override
  String get onboarding_priority_1_sub => 'Lernen beim Spielen';

  @override
  String get onboarding_priority_2 => 'Schulreife';

  @override
  String get onboarding_priority_2_sub =>
      'Vorbereitung auf Vorschule/Kindergarten';

  @override
  String get onboarding_priority_3 => 'Sie beschäftigt halten';

  @override
  String get onboarding_priority_3_sub =>
      'Produktive, glückliche Bildschirmzeit';

  @override
  String get onboarding_priority_4 => 'Lernen durch Spielen';

  @override
  String get onboarding_priority_4_sub => 'Spaß, der Fähigkeiten aufbaut';

  @override
  String get onboarding_transition_message => 'Vielen Dank fürs Teilen!';

  @override
  String onboarding_transition_submessage(int age) {
    return 'Wir erstellen die perfekte Erfahrung für Ihr $age-jähriges Kind...';
  }

  @override
  String summary_result_headline(int age) {
    return 'Perfekt für Ihr $age-jähriges Kind!';
  }

  @override
  String get summary_card_1_title => 'Was sie lernen werden:';

  @override
  String get summary_card_1_point_1 =>
      'Mustererkennung durch 15 verschiedene Spiele';

  @override
  String get summary_card_1_point_2 => 'Visuelle Diskriminierungsfähigkeiten';

  @override
  String get summary_card_1_point_3 => 'Logisches Denken und Problemlösung';

  @override
  String get summary_card_1_point_4 =>
      'Reale Verbindungen (Tiere, Berufe, Natur)';

  @override
  String get summary_card_2_title => 'Warum es funktioniert:';

  @override
  String get summary_card_2_point_1 =>
      'Aktives Lernen schlägt passives Zuschauen';

  @override
  String get summary_card_2_point_2 => 'Sofortiges Feedback hält sie engagiert';

  @override
  String get summary_card_2_point_3 => 'Vielfalt verhindert Langeweile';

  @override
  String get summary_card_2_point_4 =>
      'Abgestimmt mit Bildschirmzeit-Richtlinien';

  @override
  String get summary_cta => 'Sehen Sie, was enthalten ist';

  @override
  String get free_trial_headline =>
      'Probieren Sie 5 Spiele kostenlos, schalten Sie 10 weitere frei';

  @override
  String get free_trial_free_section =>
      'Beginnen Sie mit 5 kostenlosen Zuordnungsspielen';

  @override
  String get free_trial_free_point_1 => 'Formen, Farben und mehr';

  @override
  String get free_trial_free_point_2 => 'Keine Zeitbegrenzung';

  @override
  String get free_trial_free_point_3 => 'Keine Kreditkarte erforderlich';

  @override
  String get free_trial_premium_section =>
      'Schalten Sie alle 15 Spiele mit einem Abonnement frei';

  @override
  String get free_trial_premium_point_1 => 'Vollständige Spielbibliothek';

  @override
  String get free_trial_premium_point_2 => 'Neue Lernkategorien';

  @override
  String get free_trial_premium_point_3 => 'Jederzeit kündbar';

  @override
  String get free_trial_bottom_message =>
      'Probieren Sie zuerst die kostenlosen Spiele – schalten Sie jederzeit mehr frei!';

  @override
  String get free_trial_cta_primary => 'Mit kostenlosen Spielen beginnen';

  @override
  String get free_trial_cta_secondary => 'Abonnementoptionen ansehen';

  @override
  String get paywall_section1_headline =>
      'Bereit, alle 15 Lernspiele freizuschalten?';

  @override
  String get paywall_section1_feature_1 => '15 lehrreiche Zuordnungsspiele';

  @override
  String get paywall_section1_feature_2 =>
      'Form-, Farb-, Muster- und Logikspiele';

  @override
  String get paywall_section1_feature_3 => 'Themen zu Tieren, Berufen, Natur';

  @override
  String get paywall_section1_feature_4 => 'Ursache-Wirkungs-Lernen';

  @override
  String get paywall_section1_feature_5 => 'Keine Werbung, keine Ablenkungen';

  @override
  String get paywall_section1_feature_6 => 'Von Lehrern genehmigt';

  @override
  String get paywall_section2_badge => 'Von Lehrern genehmigt';

  @override
  String get paywall_section2_text =>
      'Pädagogen erkennen Brainy Bunny für seinen entwicklungsorientierten Ansatz zum frühen Lernen an.';

  @override
  String get paywall_section3_weekly_title => 'Wöchentlich';

  @override
  String get paywall_section3_weekly_subtext => 'Probieren Sie es aus';

  @override
  String get paywall_section3_weekly_feature => 'Jederzeit kündbar';

  @override
  String get paywall_section3_weekly_button => 'Abonnieren';

  @override
  String get paywall_section3_yearly_title => 'Jährlich';

  @override
  String get paywall_section3_yearly_badge => 'BESTER WERT - 60% sparen';

  @override
  String get paywall_section3_yearly_highlight =>
      '7-Tage-KOSTENLOSE Testversion';

  @override
  String paywall_section3_yearly_breakdown(String monthlyEquivalent) {
    return 'Nur $monthlyEquivalent/Monat';
  }

  @override
  String paywall_section3_yearly_fine_print(String yearlyPrice) {
    return 'Dann $yearlyPrice jährlich';
  }

  @override
  String get paywall_section3_yearly_feature =>
      'Während der Testversion kündigen - keine Kosten';

  @override
  String get paywall_section3_yearly_button => 'KOSTENLOSE Testversion starten';

  @override
  String get paywall_section3_monthly_title => 'Monatlich';

  @override
  String get paywall_section3_monthly_subtext => 'Flexible Option';

  @override
  String get paywall_section3_monthly_feature => 'Jederzeit kündbar';

  @override
  String get paywall_section3_monthly_button => 'Abonnieren';

  @override
  String get paywall_trust_element_1 => 'Sichere Zahlung';

  @override
  String get paywall_trust_element_2 =>
      'Jederzeit während der Testversion kündbar';

  @override
  String get paywall_trust_element_3 =>
      'Abonnement im App Store/Play Store verwalten';

  @override
  String get paywall_trust_element_4 =>
      'Wird nur nach Ende der Testversion berechnet (für jährlich)';

  @override
  String get paywall_disclaimer =>
      'Sie werden während Ihrer 7-tägigen Testversion nicht belastet. Jederzeit in Ihren Geräteeinstellungen kündbar.';

  @override
  String get paywall_continue_free_link => 'Mit kostenlosen Spielen fortfahren';

  @override
  String get parent_gate_title => 'Elternverifizierung erforderlich';

  @override
  String get parent_gate_instruction =>
      'Dieser Kauf erfordert einen Erwachsenen. Bitte lösen Sie diese Aufgabe:';

  @override
  String get parent_gate_input_placeholder => 'Antwort eingeben';

  @override
  String get parent_gate_cancel => 'Abbrechen';

  @override
  String get parent_gate_verify => 'Verifizieren';

  @override
  String get parent_gate_error =>
      'Falsche Antwort. Bitte versuchen Sie es erneut.';

  @override
  String get notification_type_1 => 'Neue Spielfreischaltungen';

  @override
  String get notification_type_2 => 'Lernstreak-Meilensteine';

  @override
  String get notification_type_3 => 'Testende-Erinnerung (falls zutreffend)';

  @override
  String get notification_type_4 => 'Tägliche Lernermutig ung';

  @override
  String get notification_trial_callout =>
      'Wir erinnern Sie 2 Tage vor Ende Ihrer Testversion, damit Sie nie überrascht werden.';

  @override
  String get notification_benefit_1 => 'Bleiben Sie konsequent beim Lernen';

  @override
  String get notification_benefit_2 => 'Verpassen Sie nie die Test-Deadline';

  @override
  String get notification_benefit_3 => 'Feiern Sie Fortschritte gemeinsam';

  @override
  String get notification_cta_enable => 'Benachrichtigungen aktivieren';

  @override
  String get notification_cta_skip => 'Nicht jetzt';

  @override
  String get subscription_error_loading_title => 'Abonnements werden geladen';

  @override
  String get subscription_error_loading_description =>
      'Bitte warten Sie, während wir Abonnementoptionen laden...';

  @override
  String get subscription_error_offline_title => 'Keine Internetverbindung';

  @override
  String get subscription_error_offline_description =>
      'Bitte überprüfen Sie Ihre Internetverbindung und versuchen Sie es erneut. Sie müssen online sein, um zu abonnieren.';

  @override
  String get subscription_error_not_available_title =>
      'Abonnements nicht verfügbar';

  @override
  String get subscription_error_not_available_description =>
      'In-App-Käufe sind auf diesem Gerät nicht verfügbar. Bitte versuchen Sie es später erneut oder wenden Sie sich an den Support.';

  @override
  String get subscription_error_products_not_found_title =>
      'Produkte nicht verfügbar';

  @override
  String get subscription_error_products_not_found_description =>
      'Wir konnten Abonnementprodukte nicht aus dem Store laden. Bitte versuchen Sie es später erneut.';

  @override
  String get subscription_error_unknown_title => 'Etwas ist schiefgelaufen';

  @override
  String get subscription_error_unknown_description =>
      'Ein unerwarteter Fehler ist aufgetreten. Bitte versuchen Sie es erneut.';

  @override
  String get subscription_error_retry => 'Erneut versuchen';

  @override
  String get subscription_error_continue_free =>
      'Mit kostenlosen Spielen fortfahren';

  @override
  String get subscription_loading => 'Laden...';

  @override
  String get goal_preschool_title => 'Prepare for preschool/kindergarten';

  @override
  String get goal_preschool_description => 'Building readiness skills';

  @override
  String get goal_cognitive_title => 'Develop cognitive abilities';

  @override
  String get goal_cognitive_description =>
      'Pattern recognition & problem-solving';

  @override
  String get goal_replace_screen_time_title => 'Replace passive screen time';

  @override
  String get goal_replace_screen_time_description =>
      'Active learning instead of videos';

  @override
  String get goal_keep_engaged_title => 'Keep them engaged & learning';

  @override
  String get goal_keep_engaged_description => 'Fun that actually builds skills';

  @override
  String get summary_age2_headline => 'Perfect for your 2-year-old explorer';

  @override
  String get summary_age2_card1_title => 'What they\'ll learn';

  @override
  String get summary_age2_card1_point1 =>
      'Basic shape recognition (circles, squares, triangles)';

  @override
  String get summary_age2_card1_point2 => 'Simple color matching';

  @override
  String get summary_age2_card1_point3 =>
      'Hand-eye coordination through drag-and-drop';

  @override
  String get summary_age2_card1_point4 => 'Cause and effect understanding';

  @override
  String get summary_age2_card2_title => 'Why it works for age 2';

  @override
  String get summary_age2_card2_point1 =>
      'Extra-large pieces perfect for tiny fingers';

  @override
  String get summary_age2_card2_point2 => 'Simple 1-2 pair matching to start';

  @override
  String get summary_age2_card2_point3 =>
      'Instant positive feedback builds confidence';

  @override
  String get summary_age2_card2_point4 =>
      'Sessions designed for 5-10 minute attention spans';

  @override
  String get summary_age3_headline => 'Designed for your curious 3-year-old';

  @override
  String get summary_age3_card1_title => 'What they\'ll learn';

  @override
  String get summary_age3_card1_point1 =>
      'Advanced shape recognition and sorting';

  @override
  String get summary_age3_card1_point2 => 'Pattern identification';

  @override
  String get summary_age3_card1_point3 => 'Color mixing and matching concepts';

  @override
  String get summary_age3_card1_point4 => 'Early problem-solving skills';

  @override
  String get summary_age3_card2_title => 'Why it works for age 3';

  @override
  String get summary_age3_card2_point1 =>
      'Progressive difficulty grows with their skills';

  @override
  String get summary_age3_card2_point2 =>
      'Builds on preschool learning concepts';

  @override
  String get summary_age3_card2_point3 =>
      'Celebrates small wins to boost motivation';

  @override
  String get summary_age3_card2_point4 => 'Perfect for emerging independence';

  @override
  String get summary_age4_headline => 'Tailored for your smart 4-year-old';

  @override
  String get summary_age4_card1_title => 'What they\'ll learn';

  @override
  String get summary_age4_card1_point1 => 'Complex pattern recognition';

  @override
  String get summary_age4_card1_point2 =>
      'Categorical thinking (animals, professions, objects)';

  @override
  String get summary_age4_card1_point3 => 'Spatial reasoning and relationships';

  @override
  String get summary_age4_card1_point4 =>
      'Pre-reading visual discrimination skills';

  @override
  String get summary_age4_card2_title => 'Why it works for age 4';

  @override
  String get summary_age4_card2_point1 =>
      'Challenges that match pre-K curriculum';

  @override
  String get summary_age4_card2_point2 =>
      'Multiple rounds build sustained focus';

  @override
  String get summary_age4_card2_point3 =>
      'Vocabulary expansion through themed games';

  @override
  String get summary_age4_card2_point4 => 'Prepares for kindergarten readiness';

  @override
  String get summary_age5_headline => 'Engaging games for your 5+ year-old';

  @override
  String get summary_age5_card1_title => 'What they\'ll learn';

  @override
  String get summary_age5_card1_point1 => 'Advanced categorization and sorting';

  @override
  String get summary_age5_card1_point2 => 'Abstract pattern completion';

  @override
  String get summary_age5_card1_point3 => 'Critical thinking and strategy';

  @override
  String get summary_age5_card1_point4 => 'Visual memory enhancement';

  @override
  String get summary_age5_card2_title => 'Why it works for age 5+';

  @override
  String get summary_age5_card2_point1 =>
      'Kindergarten-level cognitive challenges';

  @override
  String get summary_age5_card2_point2 =>
      'Builds confidence for school success';

  @override
  String get summary_age5_card2_point3 =>
      'Reinforces classroom learning at home';

  @override
  String get summary_age5_card2_point4 => 'Keeps advanced learners engaged';

  @override
  String get parental_gate_overlay_title => 'Parental Verification';

  @override
  String get parental_gate_overlay_instruction =>
      'Please solve this problem to continue:';

  @override
  String get error_purchase_verification_failed =>
      'Purchase verification failed. Please restart the app.';

  @override
  String get paywall_step2_badge_save => 'Save 60%';

  @override
  String get paywall_step2_badge_trial => '7-Day Trial';

  @override
  String get paywall_step2_yearly_title => 'Yearly';

  @override
  String paywall_step2_yearly_per_month(String price) {
    return 'Just $price/month';
  }

  @override
  String get paywall_step2_yearly_savings => 'Save 63% • Try free for 7 days';

  @override
  String get paywall_step2_yearly_feature1 => 'No charge for 7 days';

  @override
  String get paywall_step2_yearly_feature2 => 'Cancel anytime during trial';

  @override
  String get paywall_step2_yearly_feature3 =>
      'We\'ll remind you 2 days before trial ends';

  @override
  String get paywall_step2_yearly_feature4 => 'Full access to all 15 games';

  @override
  String get paywall_step2_yearly_button => 'Start FREE Trial';

  @override
  String get paywall_step2_monthly_title => 'Monthly';

  @override
  String paywall_step2_monthly_per_week(String price) {
    return '$price/week';
  }

  @override
  String get paywall_step2_monthly_savings => 'Flexible monthly plan';

  @override
  String get paywall_step2_monthly_feature1 => 'Cancel anytime';

  @override
  String get paywall_step2_monthly_feature2 => 'Full access to all 15 games';

  @override
  String get paywall_step2_monthly_button => 'Subscribe Monthly';

  @override
  String get paywall_step2_weekly_title => 'Weekly';

  @override
  String get paywall_step2_weekly_savings => 'Try for just one week';

  @override
  String get paywall_step2_weekly_feature1 => 'Cancel anytime';

  @override
  String get paywall_step2_weekly_feature2 => 'Full access to all 15 games';

  @override
  String get paywall_step2_weekly_button => 'Subscribe Weekly';

  @override
  String locked_game_headline_personalized(String childName) {
    return 'Unlock All Games for $childName!';
  }

  @override
  String get locked_game_headline_generic => 'Unlock All 15 Educational Games!';

  @override
  String locked_game_card1_title_age(int age) {
    return 'Perfect for Your $age-Year-Old';
  }

  @override
  String get locked_game_card1_title_generic => 'Age-Appropriate Learning';

  @override
  String get locked_game_card2_title => 'Building Cognitive Abilities';

  @override
  String locked_game_card3_title(int count) {
    return '$count More Games to Explore';
  }

  @override
  String get locked_game_card3_subtitle =>
      'Unlock the full collection of educational games';

  @override
  String get locked_game_age_skill_1_generic => 'Shape and color recognition';

  @override
  String get locked_game_age_skill_2_generic => 'Problem-solving abilities';

  @override
  String get locked_game_age_skill_3_generic => 'Hand-eye coordination';

  @override
  String get locked_game_age_skill_4_generic => 'Memory and focus';

  @override
  String get locked_game_age_skill_1_age2 => 'Basic shape recognition';

  @override
  String get locked_game_age_skill_2_age2 => 'Simple color matching';

  @override
  String get locked_game_age_skill_3_age2 => 'Hand-eye coordination';

  @override
  String get locked_game_age_skill_4_age2 => 'Cause and effect understanding';

  @override
  String get locked_game_age_skill_1_age3 => 'Advanced shape sorting';

  @override
  String get locked_game_age_skill_2_age3 => 'Pattern identification';

  @override
  String get locked_game_age_skill_3_age3 => 'Color mixing concepts';

  @override
  String get locked_game_age_skill_4_age3 => 'Early problem-solving';

  @override
  String get locked_game_age_skill_1_age4 => 'Complex pattern recognition';

  @override
  String get locked_game_age_skill_2_age4 => 'Categorical thinking';

  @override
  String get locked_game_age_skill_3_age4 => 'Spatial reasoning';

  @override
  String get locked_game_age_skill_4_age4 => 'Pre-reading visual skills';

  @override
  String get locked_game_age_skill_1_age5 => 'Advanced categorization';

  @override
  String get locked_game_age_skill_2_age5 => 'Abstract pattern completion';

  @override
  String get locked_game_age_skill_3_age5 => 'Critical thinking';

  @override
  String get locked_game_age_skill_4_age5 => 'Visual memory enhancement';

  @override
  String get locked_game_card2_content_default =>
      'Develop essential cognitive skills through play';

  @override
  String get locked_game_card2_content_school =>
      'Build skills for preschool and kindergarten success';

  @override
  String get locked_game_card2_content_cognitive =>
      'Enhance memory, focus, and problem-solving abilities';

  @override
  String get locked_game_card2_content_screentime =>
      'Quality educational content that parents can feel good about';

  @override
  String get locked_game_card2_content_engagement =>
      'Keep your child engaged with fun, educational activities';
}

// lib/onboarding/screens/problem_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

/// Problem Screen - Screen 2
/// Establishes empathy, acknowledges parent concerns
class ProblemScreen extends StatefulWidget {
  final VoidCallback onContinue;

  const ProblemScreen({
    super.key,
    required this.onContinue,
  });

  @override
  State<ProblemScreen> createState() => _ProblemScreenState();
}

class _ProblemScreenState extends State<ProblemScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final size = MediaQuery.of(context).size;

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(height: size.height * 0.02),

              // Concerned parent icon - moved higher
              FadeTransition(
                opacity: _animationController,
                child: Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    color: const Color(0xFFFFF3E0),
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: const Color(0xFFFF9800).withOpacity(0.3),
                      width: 2,
                    ),
                  ),
                  child: Icon(
                    Icons.family_restroom,
                    size: 40,
                    color: const Color(0xFFFF9800),
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Headline - more empathetic
              FadeTransition(
                opacity: _animationController,
                child: Text(
                  l10n.onboarding_problem_headline,
                  style: const TextStyle(
                    fontSize: 26,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF333333),
                    height: 1.3,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

              SizedBox(height: size.height * 0.03),

              // Problem points - no scrolling, fit on screen
              Column(
                children: [
                  _buildCompactProblemPoint(
                    context,
                    icon: Icons.tv,
                    title: l10n.onboarding_problem_point_1_title,
                    statistic: l10n.onboarding_problem_point_1_statistic,
                    delay: 0.2,
                  ),
                  const SizedBox(height: 12),
                  _buildCompactProblemPoint(
                    context,
                    icon: Icons.psychology_outlined,
                    title: l10n.onboarding_problem_point_2_title,
                    statistic: l10n.onboarding_problem_point_2_statistic,
                    delay: 0.4,
                  ),
                  const SizedBox(height: 12),
                  _buildCompactProblemPoint(
                    context,
                    icon: Icons.ads_click,
                    title: l10n.onboarding_problem_point_3_title,
                    statistic: l10n.onboarding_problem_point_3_statistic,
                    delay: 0.6,
                  ),
                  const SizedBox(height: 12),
                  _buildCompactProblemPoint(
                    context,
                    icon: Icons.trending_down,
                    title: l10n.onboarding_problem_point_4_title,
                    statistic: l10n.onboarding_problem_point_4_statistic,
                    delay: 0.8,
                  ),
                ],
              ),

              SizedBox(height: size.height * 0.02),

              // Scientific proof badges with logos
              _buildScientificProofWithLogos(context),

              SizedBox(height: size.height * 0.02),

              // Subtext
              Text(
                l10n.onboarding_problem_subtext,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                ),
                textAlign: TextAlign.center,
              ),

              SizedBox(height: size.height * 0.02),

              // Continue button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: widget.onContinue,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF4A90E2),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 18),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(28),
                    ),
                    elevation: 2,
                  ),
                  child: Text(
                    l10n.onboarding_problem_cta,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCompactProblemPoint(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String statistic,
    required double delay,
  }) {
    final delayedAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Interval(
        delay,
        1.0,
        curve: Curves.easeOut,
      ),
    );

    return FadeTransition(
      opacity: delayedAnimation,
      child: SlideTransition(
        position: Tween<Offset>(
          begin: const Offset(0, 0.3),
          end: Offset.zero,
        ).animate(delayedAnimation),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: const Color(0xFFFFF8F5),
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
              color: const Color(0xFFFFE5CC),
              width: 1,
            ),
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: const Color(0xFFFF9800).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Icon(
                  icon,
                  color: const Color(0xFFFF9800),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF333333),
                    height: 1.2,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: const Color(0xFFFF9800).withOpacity(0.15),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Text(
                  statistic,
                  style: const TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFFE65100),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildScientificProofWithLogos(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: const Color(0xFF4A90E2).withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.verified,
                color: const Color(0xFF4A90E2),
                size: 16,
              ),
              const SizedBox(width: 6),
              Text(
                l10n.onboarding_problem_research_title,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF333333),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            l10n.onboarding_problem_research_text,
            style: TextStyle(
              fontSize: 11,
              color: Colors.grey.shade700,
              height: 1.3,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          // Source logos row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildSourceLogo('AAP', const Color(0xFF1976D2)),
              _buildSourceLogo('JAMA', const Color(0xFF2E7D32)),
              _buildSourceLogo('Brain Sci', const Color(0xFF7B1FA2)),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSourceLogo(String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.w600,
          color: color,
        ),
      ),
    );
  }
}

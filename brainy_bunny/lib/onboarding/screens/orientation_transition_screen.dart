// lib/onboarding/screens/orientation_transition_screen.dart
import 'package:flutter/material.dart';
import 'dart:math' as math;

/// Orientation transition screen - shows rotation animation
class OrientationTransitionScreen extends StatefulWidget {
  final String message;
  final bool toLandscape; // true = to landscape, false = to portrait
  final VoidCallback onComplete;

  const OrientationTransitionScreen({
    super.key,
    required this.message,
    required this.toLandscape,
    required this.onComplete,
  });

  @override
  State<OrientationTransitionScreen> createState() =>
      _OrientationTransitionScreenState();
}

class _OrientationTransitionScreenState
    extends State<OrientationTransitionScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _rotationAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: widget.toLandscape ? math.pi / 2 : -math.pi / 2,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.3),
    ));

    _animationController.forward();

    // Auto-complete after animation
    Future.delayed(const Duration(milliseconds: 2200), () {
      if (mounted) {
        widget.onComplete();
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Rotating phone icon
              AnimatedBuilder(
                animation: _animationController,
                builder: (context, child) {
                  return Transform.rotate(
                    angle: _rotationAnimation.value,
                    child: Icon(
                      Icons.phone_android,
                      size: 120,
                      color: Colors.blue.shade600,
                    ),
                  );
                },
              ),

              const SizedBox(height: 48),

              // Message with fade-in
              FadeTransition(
                opacity: _fadeAnimation,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 32),
                  child: Text(
                    widget.message,
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey.shade800,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Rotation arrow
              FadeTransition(
                opacity: _fadeAnimation,
                child: Icon(
                  widget.toLandscape
                      ? Icons.screen_rotation
                      : Icons.screen_rotation_alt,
                  size: 48,
                  color: Colors.blue.shade400,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

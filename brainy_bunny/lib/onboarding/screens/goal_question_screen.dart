// lib/onboarding/screens/goal_question_screen.dart
import 'package:flutter/material.dart';
import 'package:brainy_bunny/models/onboarding_state.dart';
import 'package:flutter/services.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

/// Goal Question Screen - Screen 9
/// Understand parent priorities for messaging
class GoalQuestionScreen extends StatefulWidget {
  final OnboardingState state;
  final VoidCallback onContinue;
  final VoidCallback onSkip;

  const GoalQuestionScreen({
    super.key,
    required this.state,
    required this.onContinue,
    required this.onSkip,
  });

  @override
  State<GoalQuestionScreen> createState() => _GoalQuestionScreenState();
}

class _GoalQuestionScreenState extends State<GoalQuestionScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  String? _selectedGoal;

  // Goal IDs and icons - titles/descriptions come from l10n
  final List<Map<String, dynamic>> _goalData = [
    {
      'id': 'preschool',
      'icon': Icons.school,
    },
    {
      'id': 'cognitive',
      'icon': Icons.psychology,
    },
    {
      'id': 'replace_screen_time',
      'icon': Icons.timer,
    },
    {
      'id': 'keep_engaged',
      'icon': Icons.sentiment_very_satisfied,
    },
  ];

  // Get localized goals list
  List<Map<String, dynamic>> _getLocalizedGoals(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return [
      {
        'id': 'preschool',
        'icon': Icons.school,
        'title': l10n.goal_preschool_title,
        'description': l10n.goal_preschool_description,
      },
      {
        'id': 'cognitive',
        'icon': Icons.psychology,
        'title': l10n.goal_cognitive_title,
        'description': l10n.goal_cognitive_description,
      },
      {
        'id': 'replace_screen_time',
        'icon': Icons.timer,
        'title': l10n.goal_replace_screen_time_title,
        'description': l10n.goal_replace_screen_time_description,
      },
      {
        'id': 'keep_engaged',
        'icon': Icons.sentiment_very_satisfied,
        'title': l10n.goal_keep_engaged_title,
        'description': l10n.goal_keep_engaged_description,
      },
    ];
  }

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _animationController.forward();
    _selectedGoal = widget.state.learningGoal;
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _selectGoal(String goalId) {
    setState(() {
      _selectedGoal = goalId;
    });

    widget.state.learningGoal = goalId;
    widget.state.save();

    // Light haptic feedback
    HapticFeedback.lightImpact();

    // Auto-advance after selection with delay
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        widget.onContinue();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Progress indicator
              Row(
                children: [
                  Text(
                    '2/3',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: LinearProgressIndicator(
                      value: 2 / 3,
                      backgroundColor: Colors.grey.shade200,
                      valueColor: const AlwaysStoppedAnimation<Color>(
                        Color(0xFF4A90E2),
                      ),
                      minHeight: 6,
                      borderRadius: BorderRadius.circular(3),
                    ),
                  ),
                ],
              ),

              SizedBox(height: size.height * 0.04),

              // Icon with gentle pulse
              Center(
                child: ScaleTransition(
                  scale: Tween<double>(begin: 0.9, end: 1.0).animate(
                    CurvedAnimation(
                      parent: _animationController,
                      curve: Curves.easeOut,
                    ),
                  ),
                  child: Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: const Color(0xFFE3F2FD),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.favorite,
                      size: 40,
                      color: Color(0xFFF44336),
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Headline
              const Text(
                'What matters most to you\nright now?',
                style: TextStyle(
                  fontSize: 26,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1E3A5F),
                  height: 1.3,
                ),
              ),

              SizedBox(height: size.height * 0.04),

              // Goal cards
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: _getLocalizedGoals(context).asMap().entries.map((entry) {
                      final index = entry.key;
                      final goal = entry.value;
                      final isSelected = _selectedGoal == goal['id'];

                      return _buildGoalCard(
                        goal: goal,
                        isSelected: isSelected,
                        delay: index * 0.1,
                      );
                    }).toList(),
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // "All of these?" link
              Center(
                child: TextButton(
                  onPressed: widget.onSkip,
                  child: const Text(
                    'All of these? We\'ve got you covered',
                    style: TextStyle(
                      fontSize: 14,
                      color: Color(0xFF4A90E2),
                      decoration: TextDecoration.underline,
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 8),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildGoalCard({
    required Map<String, dynamic> goal,
    required bool isSelected,
    required double delay,
  }) {
    final delayedAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Interval(
        delay,
        1.0,
        curve: Curves.easeOut,
      ),
    );

    return FadeTransition(
      opacity: delayedAnimation,
      child: SlideTransition(
        position: Tween<Offset>(
          begin: const Offset(0, 0.3),
          end: Offset.zero,
        ).animate(delayedAnimation),
        child: GestureDetector(
          onTap: () => _selectGoal(goal['id']),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            margin: const EdgeInsets.only(bottom: 10),
            padding: const EdgeInsets.all(14),
            decoration: BoxDecoration(
              color: isSelected
                  ? const Color(0xFF4A90E2).withOpacity(0.1)
                  : Colors.white,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: isSelected
                    ? const Color(0xFF4CAF50)
                    : Colors.grey.shade300,
                width: isSelected ? 3 : 2,
              ),
              boxShadow: isSelected
                  ? [
                      BoxShadow(
                        color: const Color(0xFF4CAF50).withOpacity(0.3),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ]
                  : [],
            ),
            child: Row(
              children: [
                // Icon
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: isSelected
                        ? const Color(0xFF4A90E2)
                        : const Color(0xFFF5F5F5),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    goal['icon'],
                    size: 24,
                    color: isSelected ? Colors.white : Colors.grey.shade600,
                  ),
                ),
                const SizedBox(width: 16),
                // Text
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        goal['title'],
                        style: TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.bold,
                          color: isSelected
                              ? const Color(0xFF1E3A5F)
                              : Colors.grey.shade900,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 2),
                      Text(
                        goal['description'],
                        style: TextStyle(
                          fontSize: 13,
                          color: Colors.grey.shade700,
                          height: 1.3,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                // Checkmark
                if (isSelected)
                  const Icon(
                    Icons.check_circle,
                    color: Color(0xFF4CAF50),
                    size: 24,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// lib/onboarding/onboarding_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:brainy_bunny/models/onboarding_state.dart';
import 'package:brainy_bunny/models/onboarding_progress.dart';
import 'package:brainy_bunny/onboarding/screens/welcome_screen.dart';
import 'package:brainy_bunny/onboarding/screens/problem_screen.dart';
import 'package:brainy_bunny/onboarding/screens/solution_introduction_screen.dart';
import 'package:brainy_bunny/onboarding/screens/value_carousel_screen_1.dart';
import 'package:brainy_bunny/onboarding/screens/value_carousel_screen_2.dart';
import 'package:brainy_bunny/onboarding/screens/value_carousel_screen_3.dart';
import 'package:brainy_bunny/onboarding/screens/celebration_transition_screen.dart';
import 'package:brainy_bunny/onboarding/screens/child_age_screen.dart';
import 'package:brainy_bunny/onboarding/screens/goal_question_screen.dart';
import 'package:brainy_bunny/onboarding/screens/name_input_screen.dart';
import 'package:brainy_bunny/onboarding/screens/rewarding_transition_screen.dart';
import 'package:brainy_bunny/onboarding/screens/personalized_summary_screen.dart';
import 'package:brainy_bunny/onboarding/screens/pricing_plans_screen.dart';
import 'package:brainy_bunny/onboarding/screens/trial_explanation_screen.dart';
import 'package:brainy_bunny/onboarding/screens/unified_paywall_screen.dart';
import 'package:brainy_bunny/onboarding/screens/notification_permission_screen.dart';
import 'package:brainy_bunny/onboarding/screens/orientation_transition_screen.dart';
import 'package:brainy_bunny/services/analytics/analytics_manager.dart';

/// Main onboarding screen that manages the complete 15-screen flow
class OnboardingScreen extends StatefulWidget {
  final VoidCallback onComplete;

  const OnboardingScreen({
    super.key,
    required this.onComplete,
  });

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  late OnboardingState _state;
  late OnboardingProgress _progress;
  int _currentScreenIndex = 0;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeOnboarding();
  }

  Future<void> _initializeOnboarding() async {
    try {
      // Load state and progress
      _state = await OnboardingState.load();
      _progress = await OnboardingProgress.load();

      setState(() {
        _currentScreenIndex = _progress.currentScreenIndex;
        _isLoading = false;
      });

      // Track onboarding started if first time
      if (_currentScreenIndex == 0 && !_progress.isCompleted) {
        await AnalyticsManager.instance.trackOnboardingStarted();
        _state.startedAt = DateTime.now();
        await _state.save();
      }

      // Set initial orientation (all screens portrait except final)
      await _setOrientation(_isPortraitScreen(_currentScreenIndex));

    } catch (e) {
      if (kDebugMode) {
        print('Error initializing onboarding: $e');
      }

      // Create new instances on error
      _state = OnboardingState();
      _progress = OnboardingProgress();

      setState(() {
        _isLoading = false;
      });
    }
  }

  bool _isPortraitScreen(int index) {
    // All screens are portrait except screen 15 (final rotation to landscape)
    return index < 15;
  }

  Future<void> _setOrientation(bool portrait) async {
    try {
      if (portrait) {
        await SystemChrome.setPreferredOrientations([
          DeviceOrientation.portraitUp,
        ]);
      } else {
        await SystemChrome.setPreferredOrientations([
          DeviceOrientation.landscapeLeft,
          DeviceOrientation.landscapeRight,
        ]);
      }

      // Small delay to ensure orientation change completes
      await Future.delayed(const Duration(milliseconds: 300));

      if (kDebugMode) {
        print('Orientation set to ${portrait ? "portrait" : "landscape"}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error setting orientation: $e');
      }
    }
  }

  void _goToNextScreen() async {
    // Mark current screen as completed
    _progress.completeScreen(_currentScreenIndex);
    await _progress.save();

    // Track screen completion
    await AnalyticsManager.instance.trackOnboardingScreenView(
      'screen_$_currentScreenIndex',
    );

    // Check if orientation needs to change BEFORE moving to next screen
    final currentOrientation = _isPortraitScreen(_currentScreenIndex);
    final nextOrientation = _isPortraitScreen(_currentScreenIndex + 1);

    if (currentOrientation != nextOrientation) {
      // Change orientation first, then move to next screen
      await _setOrientation(nextOrientation);
    }

    setState(() {
      _currentScreenIndex++;
    });

    await _progress.save();

    // Check if onboarding is complete (reached final rotation screen)
    if (_currentScreenIndex >= 15) {
      await _completeOnboarding();
    }
  }

  void _goBackScreen() {
    if (_currentScreenIndex > 0) {
      // Check if orientation needs to change
      final currentOrientation = _isPortraitScreen(_currentScreenIndex);
      final previousOrientation = _isPortraitScreen(_currentScreenIndex - 1);

      setState(() {
        _currentScreenIndex--;
      });

      if (currentOrientation != previousOrientation) {
        _setOrientation(previousOrientation);
      }
    }
  }

  Future<void> _completeOnboarding() async {
    _progress.isCompleted = true;
    _state.completedAt = DateTime.now();

    await _progress.save();
    await _state.save();

    // Track completion
    await AnalyticsManager.instance.trackOnboardingCompleted();

    if (kDebugMode) {
      print('Onboarding completed successfully');
    }
  }

  Future<void> _finishOnboardingWithRotation() async {
    // Reset to landscape for home screen
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);

    // Call completion callback
    widget.onComplete();
  }

  Future<void> _skipToEnd() async {
    // User skipped - track this
    await AnalyticsManager.instance.trackEvent(
      eventName: 'onboarding_skipped',
      parameters: {
        'screen_index': _currentScreenIndex.toString(),
      },
    );

    // Go to final rotation screen
    setState(() {
      _currentScreenIndex = 15;
    });
  }

  Future<void> _handleSubscriptionPurchase() async {
    // User purchased subscription
    await AnalyticsManager.instance.trackEvent(
      eventName: 'onboarding_subscription_purchased',
      parameters: {
        'screen_index': _currentScreenIndex.toString(),
      },
    );

    // Show notification permission screen after subscription
    setState(() {
      _currentScreenIndex = 14; // Notification permission screen
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return WillPopScope(
      onWillPop: () async {
        // Prevent back button during onboarding
        return false;
      },
      child: _buildCurrentScreen(),
    );
  }

  Widget _buildCurrentScreen() {
    switch (_currentScreenIndex) {
      case 0: // Welcome Screen
        return WelcomeScreen(onContinue: _goToNextScreen);

      case 1: // Problem Screen
        return ProblemScreen(onContinue: _goToNextScreen);

      case 2: // Solution Introduction
        return SolutionIntroductionScreen(
          onContinue: _goToNextScreen,
          onSkip: _skipToEnd,
        );

      case 3: // Value Carousel 1: Variety (15 games, 15 ways to grow)
        return ValueCarouselScreen1(
          onContinue: _goToNextScreen,
          onSkip: _skipToEnd,
        );

      case 4: // Value Carousel 2: How It Works (Built for little learners)
        return ValueCarouselScreen2(
          onContinue: _goToNextScreen,
          onBack: _goBackScreen,
          onSkip: _skipToEnd,
        );

      case 5: // Value Carousel 3: Trust & Safety
        return ValueCarouselScreen3(
          onContinue: _goToNextScreen,
          onBack: _goBackScreen,
          onSkip: _skipToEnd,
        );

      case 6: // Celebration Transition
        return CelebrationTransitionScreen(
          onComplete: _goToNextScreen,
        );

      case 7: // Child Age Selection
        return ChildAgeScreen(
          state: _state,
          onContinue: _goToNextScreen,
        );

      case 8: // Goal Question
        return GoalQuestionScreen(
          state: _state,
          onContinue: _goToNextScreen,
          onSkip: _goToNextScreen,
        );

      case 9: // Name Input
        return NameInputScreen(
          state: _state,
          onContinue: _goToNextScreen,
          onSkip: _goToNextScreen,
        );

      case 10: // Rewarding Transition
        return RewardingTransitionScreen(
          state: _state,
          onComplete: _goToNextScreen,
        );

      case 11: // Personalized Summary
        return PersonalizedSummaryScreen(
          state: _state,
          progress: _progress,
          onContinue: _goToNextScreen,
        );

      case 12: // Trial Explanation Screen
        return TrialExplanationScreen(
          onContinue: _goToNextScreen, // Go to unified paywall
          onSkip: _skipToEnd, // Skip to final rotation
        );

      case 13: // Unified Paywall Screen
        return UnifiedPaywallScreen(
          onSubscribed: _handleSubscriptionPurchase, // Go to notification permission
          onSkip: _skipToEnd, // Skip to final rotation
          onBack: _goBackScreen,
        );

      case 14: // Notification Permission (optional, shown after purchase)
        return NotificationPermissionScreen(
          onComplete: () {
            setState(() {
              _currentScreenIndex = 15; // Go to rotation
            });
          },
        );

      case 15: // Final Rotation Screen to Landscape
        return OrientationTransitionScreen(
          message: 'Rotate to landscape for the best experience',
          toLandscape: true,
          onComplete: _finishOnboardingWithRotation,
        );

      default:
        // Shouldn't happen, but return welcome screen as fallback
        return WelcomeScreen(onContinue: _goToNextScreen);
    }
  }

  @override
  void dispose() {
    try {
      // Ensure orientation is reset to landscape for home screen
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);

      if (kDebugMode) {
        print('OnboardingScreen disposed, orientation reset to landscape');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error disposing onboarding screen: $e');
      }
    }
    super.dispose();
  }
}

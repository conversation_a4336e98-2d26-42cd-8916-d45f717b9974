{"@@locale": "en", "onboarding_welcome_headline": "Welcome to <PERSON><PERSON> Bunny", "@onboarding_welcome_headline": {"description": "Main headline on welcome screen - per publisher guidelines"}, "onboarding_welcome_subheading": "Educational matching games for little learners", "@onboarding_welcome_subheading": {"description": "Subheading on welcome screen - per publisher guidelines"}, "onboarding_welcome_cta": "Let's begin", "@onboarding_welcome_cta": {"description": "Call to action button on welcome screen - per publisher guidelines"}, "onboarding_name_headline": "Let's personalize your experience", "@onboarding_name_headline": {"description": "Headline for name input screen"}, "onboarding_name_hint": "Your name", "@onboarding_name_hint": {"description": "Hint text for name input field"}, "onboarding_name_mom": "Mom", "@onboarding_name_mom": {"description": "Quick select button for Mom"}, "onboarding_name_dad": "Dad", "@onboarding_name_dad": {"description": "Quick select button for Dad"}, "onboarding_name_parent": "Parent", "@onboarding_name_parent": {"description": "Quick select button for Parent"}, "onboarding_name_greeting": "Great, {name}!", "@onboarding_name_greeting": {"description": "Greeting after parent enters name", "placeholders": {"name": {"type": "String", "example": "<PERSON>"}}}, "onboarding_child_age_headline": "How old is your little one?", "@onboarding_child_age_headline": {"description": "Headline for child age selection - per publisher guidelines"}, "onboarding_child_age_subtext": "All games work for ages 2-5. This helps us provide age-appropriate tips.", "@onboarding_child_age_subtext": {"description": "Subtext for age selection screen"}, "onboarding_age_2": "2 years old", "@onboarding_age_2": {"description": "Option for 2-year-old child"}, "onboarding_age_3": "3 years old", "@onboarding_age_3": {"description": "Option for 3-year-old child"}, "onboarding_age_4": "4 years old", "@onboarding_age_4": {"description": "Option for 4-year-old child"}, "onboarding_age_5_plus": "5+ years old", "@onboarding_age_5_plus": {"description": "Option for 5 or older child"}, "onboarding_philosophy_headline": "Transform screen time into learning time", "@onboarding_philosophy_headline": {"description": "Educational philosophy headline"}, "onboarding_philosophy_aap": "Aligned with AAP screen time recommendations", "@onboarding_philosophy_aap": {"description": "American Academy of Pediatrics compliance statement"}, "onboarding_philosophy_learning": "Turn screen time into meaningful learning experiences", "@onboarding_philosophy_learning": {"description": "Learning benefit statement"}, "onboarding_philosophy_skills": "Develop cognitive skills through play-based learning", "@onboarding_philosophy_skills": {"description": "Skills development statement"}, "onboarding_transition_to_games": "Let's play! Rotate your device →", "@onboarding_transition_to_games": {"description": "Instruction to rotate device before demo games"}, "onboarding_transition_from_games": "Great learning! Let's finish up →", "@onboarding_transition_from_games": {"description": "Instruction to rotate device after demo games"}, "onboarding_solution_headline": "Learning through play", "@onboarding_solution_headline": {"description": "Main headline on solution introduction screen"}, "onboarding_solution_description": "While they match shapes and colors, they're building real cognitive skills.", "@onboarding_solution_description": {"description": "Description text on solution introduction screen"}, "onboarding_solution_benefit_engagement": "Active engagement", "@onboarding_solution_benefit_engagement": {"description": "Benefit 1: Active engagement"}, "onboarding_solution_benefit_pattern_recognition": "Pattern recognition", "@onboarding_solution_benefit_pattern_recognition": {"description": "Benefit 2: Pattern recognition"}, "onboarding_solution_benefit_cause_effect": "Cause-and-effect thinking", "@onboarding_solution_benefit_cause_effect": {"description": "Benefit 3: Cause-and-effect thinking"}, "onboarding_solution_benefit_screen_time": "Aligned with 1-hour screen time guidelines", "@onboarding_solution_benefit_screen_time": {"description": "Benefit 4: Screen time alignment"}, "onboarding_solution_research_text": "Research shows: Matching activities improve spatial reasoning...", "@onboarding_solution_research_text": {"description": "Research backing statement on solution introduction screen"}, "onboarding_solution_research_source": "Based on developmental psychology studies", "@onboarding_solution_research_source": {"description": "Research source note on solution introduction screen"}, "onboarding_solution_cta": "See how it works", "@onboarding_solution_cta": {"description": "Continue button text on solution introduction screen"}, "onboarding_problem_headline": "We understand your\nscreen time concerns", "@onboarding_problem_headline": {"description": "Main headline on problem screen - empathetic toward parents"}, "onboarding_problem_point_1_title": "Passive video consumption", "@onboarding_problem_point_1_title": {"description": "Problem point 1 title"}, "onboarding_problem_point_1_description": "Hours of mindless watching with zero interaction or learning", "@onboarding_problem_point_1_description": {"description": "Problem point 1 description"}, "onboarding_problem_point_1_statistic": "6x higher risk of language delays", "@onboarding_problem_point_1_statistic": {"description": "Problem point 1 statistic"}, "onboarding_problem_point_2_title": "Addictive mechanics", "@onboarding_problem_point_2_title": {"description": "Problem point 2 title"}, "onboarding_problem_point_2_description": "Apps use dopamine-driven design patterns to create dependency and compulsive usage", "@onboarding_problem_point_2_description": {"description": "Problem point 2 description"}, "onboarding_problem_point_2_statistic": "89% of apps use addictive features", "@onboarding_problem_point_2_statistic": {"description": "Problem point 2 statistic"}, "onboarding_problem_point_3_title": "Inappropriate content exposure", "@onboarding_problem_point_3_title": {"description": "Problem point 3 title"}, "onboarding_problem_point_3_description": "Ads, violence, and age-inappropriate material in 'kids' content", "@onboarding_problem_point_3_description": {"description": "Problem point 3 description"}, "onboarding_problem_point_3_statistic": "85% of kids' apps contain ads", "@onboarding_problem_point_3_statistic": {"description": "Problem point 3 statistic"}, "onboarding_problem_point_4_title": "Attention & focus problems", "@onboarding_problem_point_4_title": {"description": "Problem point 4 title"}, "onboarding_problem_point_4_description": "Fast-paced content destroys ability to concentrate and learn", "@onboarding_problem_point_4_description": {"description": "Problem point 4 description"}, "onboarding_problem_point_4_statistic": "40% increase in ADHD symptoms", "@onboarding_problem_point_4_statistic": {"description": "Problem point 4 statistic"}, "onboarding_problem_research_title": "Scientific Evidence", "@onboarding_problem_research_title": {"description": "Research section title on problem screen"}, "onboarding_problem_research_text": "Peer-reviewed studies confirm: excessive passive screen time causes brain changes, language delays, and attention problems in young children.", "@onboarding_problem_research_text": {"description": "Research backing statement on problem screen - compact version"}, "onboarding_problem_subtext": "You're not alone. 89% of parents share these concerns.", "@onboarding_problem_subtext": {"description": "Subtext on problem screen - more empathetic"}, "onboarding_problem_cta": "There's a better way", "@onboarding_problem_cta": {"description": "Continue button text on problem screen"}, "demo_game_1_skill": "Visual Matching & Shape Recognition", "@demo_game_1_skill": {"description": "Skill name for demo game 1"}, "demo_game_1_science": "Matching animals to silhouettes develops visual discrimination - the ability to notice differences between similar shapes. This skill is foundational for letter recognition and reading.", "@demo_game_1_science": {"description": "Educational explanation for demo game 1"}, "demo_game_1_citation": "<PERSON><PERSON> (1985) - Visual processing and categorical thinking", "@demo_game_1_citation": {"description": "Research citation for demo game 1"}, "demo_game_1_badge": "<PERSON><PERSON><PERSON> <PERSON>", "@demo_game_1_badge": {"description": "Badge name awarded after completing demo game 1"}, "demo_game_2_skill": "Visual Memory & Attention", "@demo_game_2_skill": {"description": "Skill name for demo game 2"}, "demo_game_2_science": "Finding matching pairs strengthens working memory - your child's ability to hold and manipulate information. This directly supports math problem-solving and following multi-step instructions.", "@demo_game_2_science": {"description": "Educational explanation for demo game 2"}, "demo_game_2_citation": "<PERSON><PERSON><PERSON> & <PERSON> (1987) - Categorization and cognitive flexibility", "@demo_game_2_citation": {"description": "Research citation for demo game 2"}, "demo_game_2_badge": "Memory Master", "@demo_game_2_badge": {"description": "Badge name awarded after completing demo game 2"}, "demo_game_3_skill": "Logical Association & Categorization", "@demo_game_3_skill": {"description": "Skill name for demo game 3"}, "demo_game_3_science": "Connecting objects to their users teaches categorization and logical thinking. Your child learns that things belong together for reasons - a key step in understanding cause and effect.", "@demo_game_3_science": {"description": "Educational explanation for demo game 3"}, "demo_game_3_citation": "<PERSON><PERSON><PERSON> (1952) - Pre-operational cognitive development", "@demo_game_3_citation": {"description": "Research citation for demo game 3"}, "demo_game_3_badge": "Logic Star", "@demo_game_3_badge": {"description": "Badge name awarded after completing demo game 3"}, "demo_game_4_skill": "Pattern Recognition & Matching", "@demo_game_4_skill": {"description": "Skill name for demo game 4"}, "demo_game_4_science": "Matching patterns builds pattern recognition - the ability to see relationships between things. This skill strongly predicts math success and helps children understand 'same' vs 'different.'", "@demo_game_4_science": {"description": "Educational explanation for demo game 4"}, "demo_game_4_citation": "<PERSON><PERSON><PERSON> et al. (2019) - Pattern skills and mathematics", "@demo_game_4_citation": {"description": "Research citation for demo game 4"}, "demo_game_4_badge": "Pattern Pro", "@demo_game_4_badge": {"description": "Badge name awarded after completing demo game 4"}, "demo_game_5_skill": "Symbolic Thinking & Real-World Connections", "@demo_game_5_skill": {"description": "Skill name for demo game 5"}, "demo_game_5_science": "Connecting tools to careers builds symbolic thinking - understanding that one thing can represent another. This abstract thinking is essential for language, math, and imagination.", "@demo_game_5_science": {"description": "Educational explanation for demo game 5"}, "demo_game_5_citation": "<PERSON><PERSON><PERSON><PERSON> (1978) - Symbolic representation in cognitive development", "@demo_game_5_citation": {"description": "Research citation for demo game 5"}, "demo_game_5_badge": "World Explorer", "@demo_game_5_badge": {"description": "Badge name awarded after completing demo game 5"}, "demo_game_next": "Next Game", "@demo_game_next": {"description": "Button to proceed to next demo game"}, "onboarding_summary_headline": "Your Journey So Far, {name}", "@onboarding_summary_headline": {"description": "Personalized summary headline", "placeholders": {"name": {"type": "String", "example": "<PERSON>"}}}, "onboarding_summary_age": "Perfect for {age}-year-olds", "@onboarding_summary_age": {"description": "Age-appropriate confirmation", "placeholders": {"age": {"type": "int", "example": "3"}}}, "onboarding_summary_skills": "5 skills practiced", "@onboarding_summary_skills": {"description": "Summary of skills practiced"}, "onboarding_summary_screen_time": "Healthy screen time approach aligned with AAP guidelines", "@onboarding_summary_screen_time": {"description": "Screen time approach statement"}, "onboarding_summary_cta": "See Your Personalized Plan", "@onboarding_summary_cta": {"description": "Call to action from summary screen"}, "trust_headline": "Trusted by Parents Worldwide", "@trust_headline": {"description": "Headline for trust proof screen"}, "trust_approved": "AAP Approved", "@trust_approved": {"description": "American Academy of Pediatrics approval badge"}, "trust_cta": "Unlock Full Learning Experience", "@trust_cta": {"description": "Call to action from trust screen"}, "paywall_headline": "Unlock All 15 Learning Games", "@paywall_headline": {"description": "Main paywall headline"}, "paywall_subheadline": "Continue your child's learning journey", "@paywall_subheadline": {"description": "Paywall subheadline"}, "paywall_feature_games": "15 educational games targeting key skills", "@paywall_feature_games": {"description": "Feature: number of games"}, "paywall_feature_progress": "Progress tracking for your child", "@paywall_feature_progress": {"description": "Feature: progress tracking"}, "paywall_feature_ad_free": "Ad-free, safe environment", "@paywall_feature_ad_free": {"description": "Feature: no ads"}, "paywall_feature_new_games": "New games added monthly", "@paywall_feature_new_games": {"description": "Feature: regular updates"}, "paywall_feature_ages": "Designed for ages 2-5", "@paywall_feature_ages": {"description": "Feature: age range"}, "paywall_trial_headline": "Try All Features FREE for 7 Days", "@paywall_trial_headline": {"description": "Trial offer headline"}, "paywall_trial_price": "Then just €0.87/week", "@paywall_trial_price": {"description": "Price after trial"}, "paywall_trial_today": "Today: Full Access Unlocked", "@paywall_trial_today": {"description": "Trial timeline: day 0"}, "paywall_trial_day5": "Day 5: We'll send a reminder", "@paywall_trial_day5": {"description": "Trial timeline: day 5"}, "paywall_trial_day7": "Day 7: Billing starts (cancel anytime before)", "@paywall_trial_day7": {"description": "Trial timeline: day 7"}, "paywall_trial_no_payment": "✓ No Payment Due Now", "@paywall_trial_no_payment": {"description": "No immediate payment notice"}, "paywall_trial_cta": "Start Free Trial →", "@paywall_trial_cta": {"description": "Start trial button"}, "paywall_weekly": "Weekly", "@paywall_weekly": {"description": "Weekly subscription plan name"}, "paywall_monthly": "Monthly", "@paywall_monthly": {"description": "Monthly subscription plan name"}, "paywall_yearly": "Yearly", "@paywall_yearly": {"description": "Yearly subscription plan name"}, "paywall_save_60": "Save 60%", "@paywall_save_60": {"description": "Savings badge for yearly plan"}, "paywall_most_popular": "Most popular choice", "@paywall_most_popular": {"description": "Most popular plan indicator"}, "paywall_cancel_anytime": "Cancel anytime from your device settings", "@paywall_cancel_anytime": {"description": "Cancel anytime text"}, "paywall_restore": "<PERSON><PERSON> Purchases", "@paywall_restore": {"description": "Restore purchases link"}, "subscription_premium": "Premium", "@subscription_premium": {"description": "Premium subscription badge"}, "subscription_trial": "7-Day Trial", "@subscription_trial": {"description": "Trial subscription badge"}, "subscription_lifetime": "Lifetime Access", "@subscription_lifetime": {"description": "Lifetime access badge for grandfathered users"}, "subscription_expired": "Subscription Ended", "@subscription_expired": {"description": "Expired subscription indicator"}, "subscription_manage": "Manage Subscription", "@subscription_manage": {"description": "Button to manage subscription"}, "subscription_cancel": "Cancel Subscription", "@subscription_cancel": {"description": "Button to cancel subscription"}, "subscription_change_plan": "Change Plan", "@subscription_change_plan": {"description": "Button to change subscription plan"}, "continue_button": "Continue", "@continue_button": {"description": "Generic continue button"}, "skip_button": "<PERSON><PERSON>", "@skip_button": {"description": "Generic skip button"}, "close_button": "Close", "@close_button": {"description": "Generic close button"}, "loading": "Loading...", "@loading": {"description": "Loading indicator text"}, "error_purchase_failed": "Purchase failed. If you already own this, try restarting the app.", "@error_purchase_failed": {"description": "Error message when purchase fails"}, "error_purchase_failed_message": "We couldn't complete your purchase. Please try again.", "@error_purchase_failed_message": {"description": "Purchase failure detailed message"}, "error_restore_failed": "No purchases found", "@error_restore_failed": {"description": "Restore purchases failure"}, "error_restore_failed_message": "We couldn't find any previous purchases. If you believe this is an error, please contact support.", "@error_restore_failed_message": {"description": "Restore failure detailed message"}, "error_network": "Network error", "@error_network": {"description": "Network connection error"}, "error_network_message": "Please check your internet connection and try again.", "@error_network_message": {"description": "Network error detailed message"}, "summary_headline": "Amazing Progress!", "@summary_headline": {"description": "Headline for personalized summary screen"}, "summary_message_age_2": "Great job, {name}! Your little one is building important skills through play. At age 2, every match they make strengthens their visual recognition and problem-solving abilities.", "@summary_message_age_2": {"description": "Personalized message for parents of 2-year-olds"}, "summary_message_age_3": "Wonderful, {name}! Your child is developing crucial cognitive skills. At age 3, these activities enhance their memory, focus, and logical thinking.", "@summary_message_age_3": {"description": "Personalized message for parents of 3-year-olds"}, "summary_message_age_4": "Excellent work, {name}! Your 4-year-old is mastering advanced problem-solving. These games are preparing them for kindergarten success.", "@summary_message_age_4": {"description": "Personalized message for parents of 4-year-olds"}, "summary_message_age_5_plus": "Fantastic, {name}! Your child is excelling at complex thinking. These skills will give them a strong foundation for school.", "@summary_message_age_5_plus": {"description": "Personalized message for parents of 5+ year-olds"}, "summary_badges_earned": "Badges Earned", "@summary_badges_earned": {"description": "Label for badges section"}, "summary_badges": "Badges", "@summary_badges": {"description": "Stat label for badge count"}, "summary_games": "Games", "@summary_games": {"description": "Stat label for games played"}, "summary_skills": "Skills", "@summary_skills": {"description": "Stat label for skill development"}, "trust_aap_description": "Our educational approach aligns with AAP guidelines for healthy screen time and early childhood development.", "@trust_aap_description": {"description": "Description of AAP approval"}, "trust_research_title": "Research-Backed Curriculum", "@trust_research_title": {"description": "Title for research-backed curriculum section"}, "trust_research_description": "Every game is designed based on peer-reviewed studies in cognitive development, featuring methods proven to enhance learning in children ages 2-5.", "@trust_research_description": {"description": "Description of research backing"}, "trust_testimonial_1_name": "<PERSON>, Mother of 3-year-old", "@trust_testimonial_1_name": {"description": "Name of first testimonial author"}, "trust_testimonial_1_quote": "\"My daughter went from struggling with shapes to confidently identifying them everywhere. The progress in just 2 weeks amazed me!\"", "@trust_testimonial_1_quote": {"description": "First parent testimonial"}, "trust_testimonial_2_name": "<PERSON>, Father of 4-year-old", "@trust_testimonial_2_name": {"description": "Name of second testimonial author"}, "trust_testimonial_2_quote": "\"Finally, screen time I feel good about! My son is learning while having fun, and I can see real cognitive improvements.\"", "@trust_testimonial_2_quote": {"description": "Second parent testimonial"}, "trust_downloads_title": "Join 100,000+ Families", "@trust_downloads_title": {"description": "Download stats title"}, "trust_downloads_description": "Trusted by parents in over 50 countries to give their children a head start in learning.", "@trust_downloads_description": {"description": "Download stats description"}, "trust_cta_headline": "Ready to unlock your child's full potential?", "@trust_cta_headline": {"description": "CTA headline on trust screen"}, "trust_cta_button": "Start Free Trial", "@trust_cta_button": {"description": "CTA button on trust screen"}, "paywall_premium_badge": "Premium Access", "@paywall_premium_badge": {"description": "Premium badge label"}, "paywall_step1_headline": "Unlock Your Child's Full Potential", "@paywall_step1_headline": {"description": "Headline for paywall step 1"}, "paywall_value_1_title": "15 Premium Games", "@paywall_value_1_title": {"description": "Value prop 1 title"}, "paywall_value_1_description": "Access all educational games designed for ages 2-5, covering shapes, colors, numbers, logic, and more", "@paywall_value_1_description": {"description": "Value prop 1 description"}, "paywall_value_2_title": "Track Progress", "@paywall_value_2_title": {"description": "Value prop 2 title"}, "paywall_value_2_description": "Detailed analytics showing your child's development and skill improvement over time", "@paywall_value_2_description": {"description": "Value prop 2 description"}, "paywall_value_3_title": "Personalized Learning", "@paywall_value_3_title": {"description": "Value prop 3 title"}, "paywall_value_3_description": "Games adapt to your child's age and skill level for optimal learning", "@paywall_value_3_description": {"description": "Value prop 3 description"}, "paywall_value_4_title": "New Content Monthly", "@paywall_value_4_title": {"description": "Value prop 4 title"}, "paywall_value_4_description": "Regular updates with new games and activities to keep learning fresh and engaging", "@paywall_value_4_description": {"description": "Value prop 4 description"}, "paywall_step1_cta": "See Plans", "@paywall_step1_cta": {"description": "CTA button for paywall step 1"}, "paywall_secure_payment": "Secure payment processing", "@paywall_secure_payment": {"description": "Payment security badge"}, "paywall_trial_badge": "7-Day Free Trial", "@paywall_trial_badge": {"description": "Free trial badge"}, "paywall_step2_headline": "Choose Your Plan", "@paywall_step2_headline": {"description": "Headline for paywall step 2"}, "paywall_step2_subheadline": "Start your 7-day free trial. Cancel anytime.", "@paywall_step2_subheadline": {"description": "Subheadline for paywall step 2"}, "paywall_plan_best_value": "Best Value", "@paywall_plan_best_value": {"description": "Badge for best value plan"}, "paywall_plan_yearly_title": "Yearly", "@paywall_plan_yearly_title": {"description": "Yearly plan title"}, "paywall_plan_yearly_period": "/year", "@paywall_plan_yearly_period": {"description": "Yearly plan period"}, "paywall_plan_yearly_per_month": "Just €3.33/month", "@paywall_plan_yearly_per_month": {"description": "Yearly plan monthly equivalent"}, "paywall_plan_yearly_savings": "Save 63% compared to monthly", "@paywall_plan_yearly_savings": {"description": "Yearly plan savings"}, "paywall_plan_monthly_title": "Monthly", "@paywall_plan_monthly_title": {"description": "Monthly plan title"}, "paywall_plan_monthly_period": "/month", "@paywall_plan_monthly_period": {"description": "Monthly plan period"}, "paywall_plan_weekly_title": "Weekly", "@paywall_plan_weekly_title": {"description": "Weekly plan title"}, "paywall_plan_weekly_period": "/week", "@paywall_plan_weekly_period": {"description": "Weekly plan period"}, "paywall_plan_weekly_note": "For short-term access", "@paywall_plan_weekly_note": {"description": "Weekly plan note"}, "paywall_trial_reminder": "Your free trial starts today. You won't be charged until day 8. Cancel anytime before then with no cost.", "@paywall_trial_reminder": {"description": "Trial reminder text"}, "paywall_step2_cta": "Continue", "@paywall_step2_cta": {"description": "CTA button for paywall step 2"}, "paywall_terms": "By continuing, you agree to our Terms of Service and Privacy Policy", "@paywall_terms": {"description": "Terms and privacy text"}, "paywall_urgency_text": "Limited Time Offer", "@paywall_urgency_text": {"description": "Urgency text"}, "paywall_step3_headline": "You're One Step Away!", "@paywall_step3_headline": {"description": "Headline for paywall step 3"}, "paywall_step3_included_title": "Everything You Get:", "@paywall_step3_included_title": {"description": "Title for included features section"}, "paywall_included_1": "All 15 premium educational games", "@paywall_included_1": {"description": "Included feature 1"}, "paywall_included_2": "Personalized learning paths for your child", "@paywall_included_2": {"description": "Included feature 2"}, "paywall_included_3": "Detailed progress tracking and insights", "@paywall_included_3": {"description": "Included feature 3"}, "paywall_included_4": "Monthly new content and activities", "@paywall_included_4": {"description": "Included feature 4"}, "paywall_included_5": "Ad-free experience for focused learning", "@paywall_included_5": {"description": "Included feature 5"}, "paywall_included_6": "Offline mode - learn anywhere, anytime", "@paywall_included_6": {"description": "Included feature 6"}, "paywall_guarantee_title": "100% Risk-Free Guarantee", "@paywall_guarantee_title": {"description": "Guarantee title"}, "paywall_guarantee_text": "Try it free for 7 days. If you're not completely satisfied, cancel before the trial ends and pay nothing. No questions asked.", "@paywall_guarantee_text": {"description": "Guarantee text"}, "paywall_step3_cta": "Start My Free Trial", "@paywall_step3_cta": {"description": "CTA button for paywall step 3"}, "pre_paywall_headline": "Your Learning Journey is Ready!", "@pre_paywall_headline": {"description": "Pre-paywall headline"}, "pre_paywall_subheadline_personalized": "Here's what we've prepared for {name}:", "@pre_paywall_subheadline_personalized": {"description": "Pre-paywall subheadline with child's name", "placeholders": {"name": {"type": "String"}}}, "pre_paywall_subheadline_age": "Here's what we've prepared for your {age}-year-old:", "@pre_paywall_subheadline_age": {"description": "Pre-paywall subheadline with child's age", "placeholders": {"age": {"type": "String"}}}, "pre_paywall_subheadline_generic": "Here's what we've prepared for your child:", "@pre_paywall_subheadline_generic": {"description": "Pre-paywall subheadline generic"}, "pre_paywall_card_1_title": "Age-Appropriate Content", "@pre_paywall_card_1_title": {"description": "Pre-paywall card 1 title"}, "pre_paywall_card_1_subtitle_age": "Perfect for {age} years old", "@pre_paywall_card_1_subtitle_age": {"description": "Pre-paywall card 1 subtitle with age", "placeholders": {"age": {"type": "String"}}}, "pre_paywall_card_1_subtitle_generic": "All 15 games work great for ages 2-5", "@pre_paywall_card_1_subtitle_generic": {"description": "Pre-paywall card 1 subtitle generic"}, "pre_paywall_card_2_title": "Learning Focus", "@pre_paywall_card_2_title": {"description": "Pre-paywall card 2 title"}, "pre_paywall_card_2_subtitle": "Building skills through play", "@pre_paywall_card_2_subtitle": {"description": "Pre-paywall card 2 subtitle"}, "pre_paywall_card_3_title": "15 Educational Games", "@pre_paywall_card_3_title": {"description": "Pre-paywall card 3 title"}, "pre_paywall_card_3_subtitle": "Shapes, colors, patterns, animals & more", "@pre_paywall_card_3_subtitle": {"description": "Pre-paywall card 3 subtitle"}, "pre_paywall_key_benefit": "Transform screen time from guilt into growth—educational content you can feel good about.", "@pre_paywall_key_benefit": {"description": "Pre-paywall key benefit statement"}, "pre_paywall_trust_1": "Teacher Approved", "@pre_paywall_trust_1": {"description": "Pre-paywall trust badge 1"}, "pre_paywall_trust_2": "Ad-Free", "@pre_paywall_trust_2": {"description": "Pre-paywall trust badge 2"}, "pre_paywall_trust_3": "Expert-Backed", "@pre_paywall_trust_3": {"description": "Pre-paywall trust badge 3 - Designed with child development specialists"}, "pre_paywall_cta_primary": "Start 7-Day FREE Trial", "@pre_paywall_cta_primary": {"description": "Pre-paywall primary CTA"}, "pre_paywall_cta_primary_subtext": "Unlock all 15 games • No charge today", "@pre_paywall_cta_primary_subtext": {"description": "Pre-paywall primary CTA subtext"}, "pre_paywall_cta_secondary": "Continue with 5 free games", "@pre_paywall_cta_secondary": {"description": "Pre-paywall secondary CTA"}, "pre_paywall_important_note": "Trial includes all 15 games. After trial: Keep 5 free games or subscribe for full access.", "@pre_paywall_important_note": {"description": "Pre-paywall important note"}, "onboarding_rotate_to_landscape": "Rotate your device to landscape", "@onboarding_rotate_to_landscape": {"description": "Message for rotating to landscape mode"}, "onboarding_rotate_to_portrait": "Rotate your device to portrait", "@onboarding_rotate_to_portrait": {"description": "Message for rotating to portrait mode"}, "demo_game_1_title": "Animal Shapes", "@demo_game_1_title": {"description": "Title for demo game 1"}, "demo_game_1_context": "Help your child match animals to their shapes! This builds visual recognition skills essential for reading.", "@demo_game_1_context": {"description": "Educational context for demo game 1"}, "demo_game_2_title": "Memory Match", "@demo_game_2_title": {"description": "Title for demo game 2"}, "demo_game_2_context": "Find matching pairs to strengthen working memory - crucial for following instructions and problem-solving.", "@demo_game_2_context": {"description": "Educational context for demo game 2"}, "demo_game_3_title": "Logic Puzzles", "@demo_game_3_title": {"description": "Title for demo game 3"}, "demo_game_3_context": "Solve puzzles to develop logical thinking and pattern recognition skills.", "@demo_game_3_context": {"description": "Educational context for demo game 3"}, "demo_game_4_title": "Pattern Fun", "@demo_game_4_title": {"description": "Title for demo game 4"}, "demo_game_4_context": "Recognize patterns to build math readiness and sequencing skills.", "@demo_game_4_context": {"description": "Educational context for demo game 4"}, "demo_game_5_title": "World Explorer", "@demo_game_5_title": {"description": "Title for demo game 5"}, "demo_game_5_context": "Explore the world to expand vocabulary and cultural awareness.", "@demo_game_5_context": {"description": "Educational context for demo game 5"}, "demo_game_congratulations": "Amazing! You earned a badge!", "@demo_game_congratulations": {"description": "Congratulations message after completing demo game"}, "paywall_benefit_all_games": "Access to all {gameCount} games", "@paywall_benefit_all_games": {"description": "In-game paywall benefit: access to all games", "placeholders": {"gameCount": {"type": "int"}}}, "paywall_benefit_age_appropriate": "Age-appropriate content", "@paywall_benefit_age_appropriate": {"description": "In-game paywall benefit: age-appropriate content"}, "paywall_benefit_progress_tracking": "Progress tracking", "@paywall_benefit_progress_tracking": {"description": "In-game paywall benefit: progress tracking"}, "paywall_benefit_offline_play": "Offline play supported", "@paywall_benefit_offline_play": {"description": "In-game paywall benefit: offline play"}, "paywall_benefit_no_ads": "No ads, kid-safe", "@paywall_benefit_no_ads": {"description": "In-game paywall benefit: no ads"}, "paywall_benefit_regular_updates": "Regular updates", "@paywall_benefit_regular_updates": {"description": "In-game paywall benefit: regular updates"}, "paywall_start_trial": "Start Free Trial", "@paywall_start_trial": {"description": "But<PERSON> text to start free trial"}, "paywall_step3_benefit_1": "Full access to all learning games", "@paywall_step3_benefit_1": {"description": "Final confirmation screen benefit 1"}, "paywall_step3_benefit_2": "Ad-free, safe learning environment", "@paywall_step3_benefit_2": {"description": "Final confirmation screen benefit 2"}, "paywall_step3_benefit_3": "Perfect for the whole family", "@paywall_step3_benefit_3": {"description": "Final confirmation screen benefit 3"}, "paywall_subscribe_button": "Subscribe Now", "@paywall_subscribe_button": {"description": "Subscribe button text on final step"}, "trial_explanation_headline": "Try All 15 Games Free for 7 Days", "@trial_explanation_headline": {"description": "Trial explanation screen headline"}, "trial_explanation_feature_1": "Full access to all 15 educational games", "@trial_explanation_feature_1": {"description": "Trial explanation feature 1"}, "trial_explanation_feature_2": "No charge for 7 days", "@trial_explanation_feature_2": {"description": "Trial explanation feature 2"}, "trial_explanation_feature_3": "We'll remind you 2 days before trial ends", "@trial_explanation_feature_3": {"description": "Trial explanation feature 3"}, "trial_explanation_feature_4": "Cancel anytime during trial - no cost", "@trial_explanation_feature_4": {"description": "Trial explanation feature 4"}, "trial_explanation_subtext": "You won't be charged until day 8 of your trial", "@trial_explanation_subtext": {"description": "Trial explanation subtext"}, "trial_explanation_cta": "See Plans", "@trial_explanation_cta": {"description": "Trial explanation CTA button"}, "unified_paywall_headline": "Choose Your Plan", "@unified_paywall_headline": {"description": "Unified paywall headline"}, "unified_paywall_subheadline": "All plans unlock 15 educational games", "@unified_paywall_subheadline": {"description": "Unified paywall subheadline"}, "unified_paywall_yearly_badge_save": "Save 60%", "@unified_paywall_yearly_badge_save": {"description": "Yearly plan save badge"}, "unified_paywall_yearly_badge_trial": "7-Day Free Trial", "@unified_paywall_yearly_badge_trial": {"description": "Yearly plan trial badge"}, "unified_paywall_yearly_title": "Yearly", "@unified_paywall_yearly_title": {"description": "Yearly plan title"}, "unified_paywall_yearly_price_breakdown": "Just {monthlyEquivalent}/month", "@unified_paywall_yearly_price_breakdown": {"description": "Yearly plan monthly equivalent price", "placeholders": {"monthlyEquivalent": {"type": "String"}}}, "unified_paywall_yearly_savings": "Save 63% • Try free for 7 days", "@unified_paywall_yearly_savings": {"description": "Yearly plan savings text"}, "unified_paywall_yearly_feature_1": "No charge for 7 days", "@unified_paywall_yearly_feature_1": {"description": "Yearly plan feature 1"}, "unified_paywall_yearly_feature_2": "Cancel anytime during trial", "@unified_paywall_yearly_feature_2": {"description": "Yearly plan feature 2"}, "unified_paywall_yearly_feature_3": "Full access to all 15 games", "@unified_paywall_yearly_feature_3": {"description": "Yearly plan feature 3"}, "unified_paywall_yearly_button": "Start Free Trial", "@unified_paywall_yearly_button": {"description": "Yearly plan button text"}, "unified_paywall_monthly_title": "Monthly", "@unified_paywall_monthly_title": {"description": "Monthly plan title"}, "unified_paywall_monthly_per_week": "{weeklyEquivalent}/week", "@unified_paywall_monthly_per_week": {"description": "Monthly plan weekly equivalent", "placeholders": {"weeklyEquivalent": {"type": "String"}}}, "unified_paywall_monthly_savings": "Flexible monthly plan", "@unified_paywall_monthly_savings": {"description": "Monthly plan savings text"}, "unified_paywall_monthly_feature_1": "Cancel anytime", "@unified_paywall_monthly_feature_1": {"description": "Monthly plan feature 1"}, "unified_paywall_monthly_feature_2": "Full access to all 15 games", "@unified_paywall_monthly_feature_2": {"description": "Monthly plan feature 2"}, "unified_paywall_monthly_button": "Subscribe Monthly", "@unified_paywall_monthly_button": {"description": "Monthly plan button text"}, "unified_paywall_weekly_title": "Weekly", "@unified_paywall_weekly_title": {"description": "Weekly plan title"}, "unified_paywall_weekly_savings": "Try for just one week", "@unified_paywall_weekly_savings": {"description": "Weekly plan savings text"}, "unified_paywall_weekly_feature_1": "Cancel anytime", "@unified_paywall_weekly_feature_1": {"description": "Weekly plan feature 1"}, "unified_paywall_weekly_feature_2": "Full access to all 15 games", "@unified_paywall_weekly_feature_2": {"description": "Weekly plan feature 2"}, "unified_paywall_weekly_button": "Subscribe Weekly", "@unified_paywall_weekly_button": {"description": "Weekly plan button text"}, "unified_paywall_trust_1": "Secure payment processing", "@unified_paywall_trust_1": {"description": "Unified paywall trust element 1"}, "unified_paywall_trust_2": "Manage in App/Play Store", "@unified_paywall_trust_2": {"description": "Unified paywall trust element 2"}, "unified_paywall_trust_3": "All plans include full access", "@unified_paywall_trust_3": {"description": "Unified paywall trust element 3"}, "unified_paywall_restore": "<PERSON><PERSON> Purchases", "@unified_paywall_restore": {"description": "Unified paywall restore purchases"}, "unified_paywall_terms": "By continuing, you agree to our Terms of Service and Privacy Policy", "@unified_paywall_terms": {"description": "Unified paywall terms text"}, "educational_value_headline": "Learning through play, not mindless entertainment", "@educational_value_headline": {"description": "Educational value screen headline - per publisher guidelines"}, "educational_value_point_1_title": "Develops pattern recognition & logical thinking", "@educational_value_point_1_title": {"description": "Educational value point 1 title"}, "educational_value_point_1_description": "Essential skills for math readiness and problem-solving", "@educational_value_point_1_description": {"description": "Educational value point 1 description"}, "educational_value_point_2_title": "Strengthens visual discrimination skills", "@educational_value_point_2_title": {"description": "Educational value point 2 title"}, "educational_value_point_2_description": "Helps children identify differences and similarities", "@educational_value_point_2_description": {"description": "Educational value point 2 description"}, "educational_value_point_3_title": "Builds problem-solving abilities", "@educational_value_point_3_title": {"description": "Educational value point 3 title"}, "educational_value_point_3_description": "Active engagement develops critical thinking", "@educational_value_point_3_description": {"description": "Educational value point 3 description"}, "educational_value_point_4_title": "Designed for healthy screen time", "@educational_value_point_4_title": {"description": "Educational value point 4 title"}, "educational_value_point_4_description": "Aligned with pediatric recommendation of 1 hour daily", "@educational_value_point_4_description": {"description": "Educational value point 4 description"}, "educational_value_research": "Research shows: Matching activities improve spatial reasoning and cognitive development in children ages 2-5.", "@educational_value_research": {"description": "Research backing statement - per publisher guidelines"}, "educational_value_research_source": "Based on developmental psychology studies", "@educational_value_research_source": {"description": "Research source note"}, "value_carousel_1_headline": "Screen time that builds skills", "@value_carousel_1_headline": {"description": "Value carousel screen 1 headline - per publisher guidelines"}, "value_carousel_1_description": "15 different matching games teaching shapes, colors, patterns, animals, professions, and cause-and-effect thinking.", "@value_carousel_1_description": {"description": "Value carousel screen 1 description - per publisher guidelines"}, "value_carousel_1_subtext": "Not just entertainment—actual learning in every match.", "@value_carousel_1_subtext": {"description": "Value carousel screen 1 subtext"}, "value_carousel_1_benefit_1_title": "Memory Development", "@value_carousel_1_benefit_1_title": {"description": "Benefit 1 title"}, "value_carousel_1_benefit_1_description": "Matching games that strengthen recall and recognition", "@value_carousel_1_benefit_1_description": {"description": "Benefit 1 description"}, "value_carousel_1_benefit_2_title": "Problem Solving", "@value_carousel_1_benefit_2_title": {"description": "Benefit 2 title"}, "value_carousel_1_benefit_2_description": "Puzzles that encourage logical thinking and strategy", "@value_carousel_1_benefit_2_description": {"description": "Benefit 2 description"}, "value_carousel_1_benefit_3_title": "Progressive Learning", "@value_carousel_1_benefit_3_title": {"description": "Benefit 3 title"}, "value_carousel_1_benefit_3_description": "Difficulty adapts as your child masters new skills", "@value_carousel_1_benefit_3_description": {"description": "Benefit 3 description"}, "value_carousel_2_headline": "15 ways to learn and grow", "@value_carousel_2_headline": {"description": "Value carousel screen 2 headline - per publisher guidelines"}, "value_carousel_2_description": "From simple shape matching to understanding relationships between objects and situations—each game targets specific developmental skills.", "@value_carousel_2_description": {"description": "Value carousel screen 2 description - per publisher guidelines"}, "value_carousel_2_category_1": "Shapes & Geometry", "@value_carousel_2_category_1": {"description": "Game category 1"}, "value_carousel_2_category_2": "Colors & Patterns", "@value_carousel_2_category_2": {"description": "Game category 2"}, "value_carousel_2_category_3": "Animals & Nature", "@value_carousel_2_category_3": {"description": "Game category 3"}, "value_carousel_2_category_4": "Professions & Roles", "@value_carousel_2_category_4": {"description": "Game category 4"}, "value_carousel_2_category_5": "Cause & Effect", "@value_carousel_2_category_5": {"description": "Game category 5"}, "value_carousel_2_category_6": "And more...", "@value_carousel_2_category_6": {"description": "Additional categories indicator"}, "value_carousel_2_feature_1_title": "100% Ad-Free", "@value_carousel_2_feature_1_title": {"description": "Feature 1 title"}, "value_carousel_2_feature_1_description": "No advertisements, no distractions, no inappropriate content", "@value_carousel_2_feature_1_description": {"description": "Feature 1 description"}, "value_carousel_2_feature_2_title": "Age-Appropriate", "@value_carousel_2_feature_2_title": {"description": "Feature 2 title"}, "value_carousel_2_feature_2_description": "Content designed specifically for ages 2-5", "@value_carousel_2_feature_2_description": {"description": "Feature 2 description"}, "value_carousel_2_feature_3_title": "Active Learning", "@value_carousel_2_feature_3_title": {"description": "Feature 3 title"}, "value_carousel_2_feature_3_description": "Engaging activities, not passive watching", "@value_carousel_2_feature_3_description": {"description": "Feature 3 description"}, "value_carousel_3_headline": "Teacher Approved, Parent Trusted", "@value_carousel_3_headline": {"description": "Value carousel screen 3 headline - per publisher guidelines"}, "value_carousel_3_trust_element_1": "Ad-free learning environment", "@value_carousel_3_trust_element_1": {"description": "Trust element 1"}, "value_carousel_3_trust_element_2": "No data collection", "@value_carousel_3_trust_element_2": {"description": "Trust element 2"}, "value_carousel_3_trust_element_3": "Based on child development expert recommendations", "@value_carousel_3_trust_element_3": {"description": "Trust element 3"}, "value_carousel_3_trust_element_4": "Safe for young children", "@value_carousel_3_trust_element_4": {"description": "Trust element 4"}, "value_carousel_3_feature_1_title": "Progress Tracking", "@value_carousel_3_feature_1_title": {"description": "Feature 1 title"}, "value_carousel_3_feature_1_description": "See which skills your child is developing", "@value_carousel_3_feature_1_description": {"description": "Feature 1 description"}, "value_carousel_3_feature_2_title": "Achievement Rewards", "@value_carousel_3_feature_2_title": {"description": "Feature 2 title"}, "value_carousel_3_feature_2_description": "Earn stars and badges that motivate continued learning", "@value_carousel_3_feature_2_description": {"description": "Feature 2 description"}, "value_carousel_3_feature_3_title": "Personalized Experience", "@value_carousel_3_feature_3_title": {"description": "Feature 3 title"}, "value_carousel_3_feature_3_description": "Games that adapt to your child's skill level", "@value_carousel_3_feature_3_description": {"description": "Feature 3 description"}, "summary_headline_new": "Perfect! Here's Your Personalized Learning Path", "@summary_headline_new": {"description": "Summary screen headline"}, "summary_learning_path_title": "What Your Child Will Learn:", "@summary_learning_path_title": {"description": "Learning path section title"}, "summary_skill_cognitive": "Cognitive Development", "@summary_skill_cognitive": {"description": "Cognitive skill title"}, "summary_skill_cognitive_desc": "Memory games and problem-solving activities perfect for age {age}", "@summary_skill_cognitive_desc": {"description": "Cognitive skill description", "placeholders": {"age": {"type": "int"}}}, "summary_skill_visual": "Visual Perception", "@summary_skill_visual": {"description": "Visual skill title"}, "summary_skill_visual_desc": "Shape recognition and spatial awareness games for {age}-year-olds", "@summary_skill_visual_desc": {"description": "Visual skill description", "placeholders": {"age": {"type": "int"}}}, "summary_skill_exploration": "Exploration & Discovery", "@summary_skill_exploration": {"description": "Exploration skill title"}, "summary_skill_exploration_desc": "Interactive games that encourage curiosity at age {age}", "@summary_skill_exploration_desc": {"description": "Exploration skill description", "placeholders": {"age": {"type": "int"}}}, "summary_next_step": "Next: Try Premium free for 7 days to unlock all games!", "@summary_next_step": {"description": "Next step callout"}, "trial_badge": "7-Day Free Trial", "@trial_badge": {"description": "Trial badge text"}, "trial_headline": "Try Premium Free for 7 Days", "@trial_headline": {"description": "Trial explanation headline"}, "trial_description": "Get full access to all premium games and features. Cancel anytime during your trial—no charges if you cancel before it ends.", "@trial_description": {"description": "Trial explanation description"}, "trial_feature_1_title": "All Premium Games", "@trial_feature_1_title": {"description": "Trial feature 1 title"}, "trial_feature_1_description": "Access every learning game in our library", "@trial_feature_1_description": {"description": "Trial feature 1 description"}, "trial_feature_2_title": "Ad-Free Experience", "@trial_feature_2_title": {"description": "Trial feature 2 title"}, "trial_feature_2_description": "Safe learning environment with no advertisements", "@trial_feature_2_description": {"description": "Trial feature 2 description"}, "trial_feature_3_title": "Progress Tracking", "@trial_feature_3_title": {"description": "Trial feature 3 title"}, "trial_feature_3_description": "See your child's development and achievements", "@trial_feature_3_description": {"description": "Trial feature 3 description"}, "trial_feature_4_title": "Regular Updates", "@trial_feature_4_title": {"description": "Trial feature 4 title"}, "trial_feature_4_description": "New games and features added regularly", "@trial_feature_4_description": {"description": "Trial feature 4 description"}, "trial_how_it_works_title": "How It Works:", "@trial_how_it_works_title": {"description": "How it works section title"}, "trial_step_1": "Start your free 7-day trial today", "@trial_step_1": {"description": "Trial step 1"}, "trial_step_2": "Enjoy full access to all premium features", "@trial_step_2": {"description": "Trial step 2"}, "trial_step_3": "Cancel anytime—no charges before trial ends", "@trial_step_3": {"description": "Trial step 3"}, "trial_cta": "Start My Free Trial", "@trial_cta": {"description": "Trial CTA button"}, "trial_disclaimer": "Free for 7 days, then your selected plan rate. Cancel anytime.", "@trial_disclaimer": {"description": "Trial disclaimer text"}, "notification_permission_headline": "Never miss a learning moment", "@notification_permission_headline": {"description": "Notification permission headline - per publisher guidelines"}, "notification_permission_description": "We'll send gentle reminders for:", "@notification_permission_description": {"description": "Notification permission description"}, "notification_benefit_1_title": "Trial Reminders", "@notification_benefit_1_title": {"description": "Notification benefit 1 title"}, "notification_benefit_1_description": "Get notified before your trial ends so you never lose access", "@notification_benefit_1_description": {"description": "Notification benefit 1 description"}, "notification_benefit_2_title": "Learning Milestones", "@notification_benefit_2_title": {"description": "Notification benefit 2 title"}, "notification_benefit_2_description": "Celebrate when your child reaches new achievements", "@notification_benefit_2_description": {"description": "Notification benefit 2 description"}, "notification_benefit_3_title": "Engagement Tips", "@notification_benefit_3_title": {"description": "Notification benefit 3 title"}, "notification_benefit_3_description": "Get suggestions for keeping learning fun and engaging", "@notification_benefit_3_description": {"description": "Notification benefit 3 description"}, "notification_privacy_note": "We respect your privacy. You can disable notifications anytime in settings.", "@notification_privacy_note": {"description": "Notification privacy note"}, "notification_enable_button": "Enable Notifications", "@notification_enable_button": {"description": "Enable notifications button"}, "notification_maybe_later": "Maybe Later", "@notification_maybe_later": {"description": "Skip notifications button"}, "subscription_management_title": "Manage Subscription", "@subscription_management_title": {"description": "Subscription management screen title"}, "subscription_status_active": "Premium Active", "@subscription_status_active": {"description": "Active subscription status"}, "subscription_status_active_description": "You have full access to all premium features", "@subscription_status_active_description": {"description": "Active subscription description"}, "subscription_status_inactive": "Free Version", "@subscription_status_inactive": {"description": "Inactive subscription status"}, "subscription_status_inactive_description": "Upgrade to premium for full access to all games", "@subscription_status_inactive_description": {"description": "Inactive subscription description"}, "subscription_actions_title": "Actions", "@subscription_actions_title": {"description": "Actions section title"}, "subscription_restore_title": "<PERSON><PERSON> Purchases", "@subscription_restore_title": {"description": "Restore purchases title"}, "subscription_restore_description": "Already subscribed on another device? Restore your purchases here.", "@subscription_restore_description": {"description": "Restore purchases description"}, "subscription_restore_button": "Rest<PERSON>", "@subscription_restore_button": {"description": "Restore button text"}, "subscription_manage_title": "Manage Your Subscription", "@subscription_manage_title": {"description": "Manage subscription title"}, "subscription_manage_description": "View, change, or cancel your subscription through your app store account.", "@subscription_manage_description": {"description": "Manage subscription description"}, "subscription_manage_button": "Open Subscription Settings", "@subscription_manage_button": {"description": "Manage subscription button"}, "subscription_help_title": "Help & Information", "@subscription_help_title": {"description": "Help section title"}, "subscription_cancel_title": "How to Cancel", "@subscription_cancel_title": {"description": "Cancel guide title"}, "subscription_cancel_description": "You can cancel your subscription anytime through your App Store or Google Play account settings. You'll keep access until the end of your billing period.", "@subscription_cancel_description": {"description": "Cancel guide description"}, "subscription_payment_failure_title": "Payment Issues", "@subscription_payment_failure_title": {"description": "Payment failure help title"}, "subscription_payment_failure_description": "If your payment fails, update your payment method in your app store account. We'll retry the payment automatically.", "@subscription_payment_failure_description": {"description": "Payment failure help description"}, "next_button": "Next", "@next_button": {"description": "Next button text"}, "back_button": "Back", "@back_button": {"description": "Back button text"}, "onboarding_priority_question": "What's most important to you?", "@onboarding_priority_question": {"description": "Parent priorities question - per publisher guidelines"}, "onboarding_priority_1": "Educational screen time", "@onboarding_priority_1": {"description": "Priority option 1"}, "onboarding_priority_1_sub": "Learning while playing", "@onboarding_priority_1_sub": {"description": "Priority option 1 subtext"}, "onboarding_priority_2": "School readiness", "@onboarding_priority_2": {"description": "Priority option 2"}, "onboarding_priority_2_sub": "Preparing for preschool/kindergarten", "@onboarding_priority_2_sub": {"description": "Priority option 2 subtext"}, "onboarding_priority_3": "Keeping them engaged", "@onboarding_priority_3": {"description": "Priority option 3"}, "onboarding_priority_3_sub": "Productive, happy screen time", "@onboarding_priority_3_sub": {"description": "Priority option 3 subtext"}, "onboarding_priority_4": "Learning through play", "@onboarding_priority_4": {"description": "Priority option 4"}, "onboarding_priority_4_sub": "Fun that builds skills", "@onboarding_priority_4_sub": {"description": "Priority option 4 subtext"}, "onboarding_transition_message": "Thank you for sharing!", "@onboarding_transition_message": {"description": "Transition screen message"}, "onboarding_transition_submessage": "We're creating the perfect experience for your {age}-year-old...", "@onboarding_transition_submessage": {"description": "Transition screen submessage", "placeholders": {"age": {"type": "int"}}}, "summary_result_headline": "Perfect for your {age}-year old!", "@summary_result_headline": {"description": "Summary results headline - per publisher guidelines", "placeholders": {"age": {"type": "int"}}}, "summary_card_1_title": "What They'll Learn:", "@summary_card_1_title": {"description": "Summary card 1 title"}, "summary_card_1_point_1": "Pattern recognition through 15 different games", "@summary_card_1_point_1": {"description": "Learning point 1"}, "summary_card_1_point_2": "Visual discrimination skills", "@summary_card_1_point_2": {"description": "Learning point 2"}, "summary_card_1_point_3": "Logical thinking and problem-solving", "@summary_card_1_point_3": {"description": "Learning point 3"}, "summary_card_1_point_4": "Real-world connections (animals, professions, nature)", "@summary_card_1_point_4": {"description": "Learning point 4"}, "summary_card_2_title": "Why It Works:", "@summary_card_2_title": {"description": "Summary card 2 title"}, "summary_card_2_point_1": "Active learning beats passive watching", "@summary_card_2_point_1": {"description": "Why it works point 1"}, "summary_card_2_point_2": "Immediate feedback keeps them engaged", "@summary_card_2_point_2": {"description": "Why it works point 2"}, "summary_card_2_point_3": "Variety prevents boredom", "@summary_card_2_point_3": {"description": "Why it works point 3"}, "summary_card_2_point_4": "Aligned with screen time guidelines", "@summary_card_2_point_4": {"description": "Why it works point 4"}, "summary_cta": "See what's included", "@summary_cta": {"description": "Summary screen CTA button"}, "free_trial_headline": "Try 5 games free, unlock 10 more", "@free_trial_headline": {"description": "Free trial explanation headline - per publisher guidelines"}, "free_trial_free_section": "Start with 5 free matching games", "@free_trial_free_section": {"description": "Free section title"}, "free_trial_free_point_1": "Shapes, colors, and more", "@free_trial_free_point_1": {"description": "Free feature 1"}, "free_trial_free_point_2": "No time limit", "@free_trial_free_point_2": {"description": "Free feature 2"}, "free_trial_free_point_3": "No credit card needed", "@free_trial_free_point_3": {"description": "Free feature 3"}, "free_trial_premium_section": "Unlock all 15 games with a subscription", "@free_trial_premium_section": {"description": "Premium section title"}, "free_trial_premium_point_1": "Full game library", "@free_trial_premium_point_1": {"description": "Premium feature 1"}, "free_trial_premium_point_2": "New learning categories", "@free_trial_premium_point_2": {"description": "Premium feature 2"}, "free_trial_premium_point_3": "Cancel anytime", "@free_trial_premium_point_3": {"description": "Premium feature 3"}, "free_trial_bottom_message": "Try the free games first—unlock more anytime!", "@free_trial_bottom_message": {"description": "Bottom message on free trial screen"}, "free_trial_cta_primary": "Start with free games", "@free_trial_cta_primary": {"description": "Primary CTA - start free games"}, "free_trial_cta_secondary": "See subscription options", "@free_trial_cta_secondary": {"description": "Secondary CTA - view subscriptions"}, "paywall_section1_headline": "Ready to unlock all 15 learning games?", "@paywall_section1_headline": {"description": "Paywall section 1 headline - per publisher guidelines"}, "paywall_section1_feature_1": "15 educational matching games", "@paywall_section1_feature_1": {"description": "Paywall feature 1"}, "paywall_section1_feature_2": "Shape, color, pattern & logic games", "@paywall_section1_feature_2": {"description": "Paywall feature 2"}, "paywall_section1_feature_3": "Animals, professions, nature themes", "@paywall_section1_feature_3": {"description": "Paywall feature 3"}, "paywall_section1_feature_4": "Cause-and-effect learning", "@paywall_section1_feature_4": {"description": "Paywall feature 4"}, "paywall_section1_feature_5": "No ads, no distractions", "@paywall_section1_feature_5": {"description": "Paywall feature 5"}, "paywall_section1_feature_6": "Teacher Approved", "@paywall_section1_feature_6": {"description": "Paywall feature 6"}, "paywall_section2_badge": "Teacher Approved", "@paywall_section2_badge": {"description": "Teacher approved badge text"}, "paywall_section2_text": "Educators recognize <PERSON><PERSON> for its developmental approach to early learning.", "@paywall_section2_text": {"description": "Social proof supporting text"}, "paywall_section3_weekly_title": "Weekly", "@paywall_section3_weekly_title": {"description": "Weekly plan title"}, "paywall_section3_weekly_subtext": "Try it out", "@paywall_section3_weekly_subtext": {"description": "Weekly plan subtext"}, "paywall_section3_weekly_feature": "Cancel anytime", "@paywall_section3_weekly_feature": {"description": "Weekly plan feature"}, "paywall_section3_weekly_button": "Subscribe", "@paywall_section3_weekly_button": {"description": "Weekly plan button"}, "paywall_section3_yearly_title": "Yearly", "@paywall_section3_yearly_title": {"description": "Yearly plan title"}, "paywall_section3_yearly_badge": "BEST VALUE - Save 60%", "@paywall_section3_yearly_badge": {"description": "Yearly plan savings badge"}, "paywall_section3_yearly_highlight": "7-day FREE trial", "@paywall_section3_yearly_highlight": {"description": "Yearly plan trial highlight"}, "paywall_section3_yearly_breakdown": "Just {monthlyEquivalent}/month", "@paywall_section3_yearly_breakdown": {"description": "Yearly plan monthly breakdown", "placeholders": {"monthlyEquivalent": {"type": "String"}}}, "paywall_section3_yearly_fine_print": "Then {yearlyPrice} annually", "@paywall_section3_yearly_fine_print": {"description": "Yearly plan fine print", "placeholders": {"yearlyPrice": {"type": "String"}}}, "paywall_section3_yearly_feature": "Cancel during trial - no charge", "@paywall_section3_yearly_feature": {"description": "Yearly plan feature"}, "paywall_section3_yearly_button": "Start FREE trial", "@paywall_section3_yearly_button": {"description": "Yearly plan button"}, "paywall_section3_monthly_title": "Monthly", "@paywall_section3_monthly_title": {"description": "Monthly plan title"}, "paywall_section3_monthly_subtext": "Flexible option", "@paywall_section3_monthly_subtext": {"description": "Monthly plan subtext"}, "paywall_section3_monthly_feature": "Cancel anytime", "@paywall_section3_monthly_feature": {"description": "Monthly plan feature"}, "paywall_section3_monthly_button": "Subscribe", "@paywall_section3_monthly_button": {"description": "Monthly plan button"}, "paywall_trust_element_1": "Secure payment", "@paywall_trust_element_1": {"description": "Trust element 1"}, "paywall_trust_element_2": "Cancel anytime during trial", "@paywall_trust_element_2": {"description": "Trust element 2"}, "paywall_trust_element_3": "Manage subscription in App Store/Play Store", "@paywall_trust_element_3": {"description": "Trust element 3"}, "paywall_trust_element_4": "Charged only after trial ends (for yearly)", "@paywall_trust_element_4": {"description": "Trust element 4"}, "paywall_disclaimer": "You won't be charged during your 7-day trial. Cancel anytime in your device settings.", "@paywall_disclaimer": {"description": "Important disclaimer below pricing"}, "paywall_continue_free_link": "Continue with free games", "@paywall_continue_free_link": {"description": "Link to dismiss paywall and use free version"}, "parent_gate_title": "Parent Verification Required", "@parent_gate_title": {"description": "Parent gate dialog title - per publisher guidelines"}, "parent_gate_instruction": "This purchase requires an adult. Please solve this problem:", "@parent_gate_instruction": {"description": "Parent gate instruction"}, "parent_gate_input_placeholder": "Enter answer", "@parent_gate_input_placeholder": {"description": "Parent gate input placeholder"}, "parent_gate_cancel": "Cancel", "@parent_gate_cancel": {"description": "Parent gate cancel button"}, "parent_gate_verify": "Verify", "@parent_gate_verify": {"description": "Parent gate verify button"}, "parent_gate_error": "Incorrect answer. Please try again.", "@parent_gate_error": {"description": "Parent gate error message"}, "notification_type_1": "New game unlocks", "@notification_type_1": {"description": "Notification type 1"}, "notification_type_2": "Learning streak milestones", "@notification_type_2": {"description": "Notification type 2"}, "notification_type_3": "Trial ending reminder (if applicable)", "@notification_type_3": {"description": "Notification type 3"}, "notification_type_4": "Daily learning encouragement", "@notification_type_4": {"description": "Notification type 4"}, "notification_trial_callout": "We'll remind you 2 days before your trial ends, so you're never surprised.", "@notification_trial_callout": {"description": "Special callout for trial users"}, "notification_benefit_1": "Stay consistent with learning", "@notification_benefit_1": {"description": "Notification benefit 1"}, "notification_benefit_2": "Never miss trial deadline", "@notification_benefit_2": {"description": "Notification benefit 2"}, "notification_benefit_3": "Celebrate progress together", "@notification_benefit_3": {"description": "Notification benefit 3"}, "notification_cta_enable": "Enable notifications", "@notification_cta_enable": {"description": "Enable notifications button"}, "notification_cta_skip": "Not now", "@notification_cta_skip": {"description": "Skip notifications button"}, "subscription_error_loading_title": "Loading Subscriptions", "@subscription_error_loading_title": {"description": "Title when loading subscription products"}, "subscription_error_loading_description": "Please wait while we load subscription options...", "@subscription_error_loading_description": {"description": "Description when loading subscription products"}, "subscription_error_offline_title": "No Internet Connection", "@subscription_error_offline_title": {"description": "Title when offline"}, "subscription_error_offline_description": "Please check your internet connection and try again. You need to be online to subscribe.", "@subscription_error_offline_description": {"description": "Description when offline"}, "subscription_error_not_available_title": "Subscriptions Not Available", "@subscription_error_not_available_title": {"description": "Title when IAP service not available"}, "subscription_error_not_available_description": "In-app purchases are not available on this device. Please try again later or contact support.", "@subscription_error_not_available_description": {"description": "Description when IAP service not available"}, "subscription_error_products_not_found_title": "Products Not Available", "@subscription_error_products_not_found_title": {"description": "Title when products not found in store"}, "subscription_error_products_not_found_description": "We couldn't load subscription products from the store. Please try again later.", "@subscription_error_products_not_found_description": {"description": "Description when products not found"}, "subscription_error_unknown_title": "Something Went Wrong", "@subscription_error_unknown_title": {"description": "Title for unknown error"}, "subscription_error_unknown_description": "An unexpected error occurred. Please try again.", "@subscription_error_unknown_description": {"description": "Description for unknown error"}, "subscription_error_retry": "Try Again", "@subscription_error_retry": {"description": "Button to retry loading subscriptions"}, "subscription_error_continue_free": "Continue with Free Games", "@subscription_error_continue_free": {"description": "Button to continue with free version"}, "subscription_loading": "Loading...", "@subscription_loading": {"description": "Generic loading message"}, "goal_preschool_title": "Prepare for preschool/kindergarten", "@goal_preschool_title": {"description": "Goal option: preschool preparation"}, "goal_preschool_description": "Building readiness skills", "@goal_preschool_description": {"description": "Goal option description: preschool"}, "goal_cognitive_title": "Develop cognitive abilities", "@goal_cognitive_title": {"description": "Goal option: cognitive development"}, "goal_cognitive_description": "Pattern recognition & problem-solving", "@goal_cognitive_description": {"description": "Goal option description: cognitive"}, "goal_replace_screen_time_title": "Replace passive screen time", "@goal_replace_screen_time_title": {"description": "Goal option: replace screen time"}, "goal_replace_screen_time_description": "Active learning instead of videos", "@goal_replace_screen_time_description": {"description": "Goal option description: screen time"}, "goal_keep_engaged_title": "Keep them engaged & learning", "@goal_keep_engaged_title": {"description": "Goal option: keep engaged"}, "goal_keep_engaged_description": "Fun that actually builds skills", "@goal_keep_engaged_description": {"description": "Goal option description: engagement"}, "summary_age2_headline": "Perfect for your 2-year-old explorer", "@summary_age2_headline": {"description": "Personalized summary headline for age 2"}, "summary_age2_card1_title": "What they'll learn", "@summary_age2_card1_title": {"description": "Age 2 learning card title"}, "summary_age2_card1_point1": "Basic shape recognition (circles, squares, triangles)", "@summary_age2_card1_point1": {"description": "Age 2 learning point 1"}, "summary_age2_card1_point2": "Simple color matching", "@summary_age2_card1_point2": {"description": "Age 2 learning point 2"}, "summary_age2_card1_point3": "Hand-eye coordination through drag-and-drop", "@summary_age2_card1_point3": {"description": "Age 2 learning point 3"}, "summary_age2_card1_point4": "Cause and effect understanding", "@summary_age2_card1_point4": {"description": "Age 2 learning point 4"}, "summary_age2_card2_title": "Why it works for age 2", "@summary_age2_card2_title": {"description": "Age 2 why it works card title"}, "summary_age2_card2_point1": "Extra-large pieces perfect for tiny fingers", "@summary_age2_card2_point1": {"description": "Age 2 why it works point 1"}, "summary_age2_card2_point2": "Simple 1-2 pair matching to start", "@summary_age2_card2_point2": {"description": "Age 2 why it works point 2"}, "summary_age2_card2_point3": "Instant positive feedback builds confidence", "@summary_age2_card2_point3": {"description": "Age 2 why it works point 3"}, "summary_age2_card2_point4": "Sessions designed for 5-10 minute attention spans", "@summary_age2_card2_point4": {"description": "Age 2 why it works point 4"}, "summary_age3_headline": "Designed for your curious 3-year-old", "@summary_age3_headline": {"description": "Personalized summary headline for age 3"}, "summary_age3_card1_title": "What they'll learn", "@summary_age3_card1_title": {"description": "Age 3 learning card title"}, "summary_age3_card1_point1": "Advanced shape recognition and sorting", "@summary_age3_card1_point1": {"description": "Age 3 learning point 1"}, "summary_age3_card1_point2": "Pattern identification", "@summary_age3_card1_point2": {"description": "Age 3 learning point 2"}, "summary_age3_card1_point3": "Color mixing and matching concepts", "@summary_age3_card1_point3": {"description": "Age 3 learning point 3"}, "summary_age3_card1_point4": "Early problem-solving skills", "@summary_age3_card1_point4": {"description": "Age 3 learning point 4"}, "summary_age3_card2_title": "Why it works for age 3", "@summary_age3_card2_title": {"description": "Age 3 why it works card title"}, "summary_age3_card2_point1": "Progressive difficulty grows with their skills", "@summary_age3_card2_point1": {"description": "Age 3 why it works point 1"}, "summary_age3_card2_point2": "Builds on preschool learning concepts", "@summary_age3_card2_point2": {"description": "Age 3 why it works point 2"}, "summary_age3_card2_point3": "Cele<PERSON>tes small wins to boost motivation", "@summary_age3_card2_point3": {"description": "Age 3 why it works point 3"}, "summary_age3_card2_point4": "Perfect for emerging independence", "@summary_age3_card2_point4": {"description": "Age 3 why it works point 4"}, "summary_age4_headline": "Tailored for your smart 4-year-old", "@summary_age4_headline": {"description": "Personalized summary headline for age 4"}, "summary_age4_card1_title": "What they'll learn", "@summary_age4_card1_title": {"description": "Age 4 learning card title"}, "summary_age4_card1_point1": "Complex pattern recognition", "@summary_age4_card1_point1": {"description": "Age 4 learning point 1"}, "summary_age4_card1_point2": "Categorical thinking (animals, professions, objects)", "@summary_age4_card1_point2": {"description": "Age 4 learning point 2"}, "summary_age4_card1_point3": "Spatial reasoning and relationships", "@summary_age4_card1_point3": {"description": "Age 4 learning point 3"}, "summary_age4_card1_point4": "Pre-reading visual discrimination skills", "@summary_age4_card1_point4": {"description": "Age 4 learning point 4"}, "summary_age4_card2_title": "Why it works for age 4", "@summary_age4_card2_title": {"description": "Age 4 why it works card title"}, "summary_age4_card2_point1": "Challenges that match pre-K curriculum", "@summary_age4_card2_point1": {"description": "Age 4 why it works point 1"}, "summary_age4_card2_point2": "Multiple rounds build sustained focus", "@summary_age4_card2_point2": {"description": "Age 4 why it works point 2"}, "summary_age4_card2_point3": "Vocabulary expansion through themed games", "@summary_age4_card2_point3": {"description": "Age 4 why it works point 3"}, "summary_age4_card2_point4": "Prepares for kindergarten readiness", "@summary_age4_card2_point4": {"description": "Age 4 why it works point 4"}, "summary_age5_headline": "Engaging games for your 5+ year-old", "@summary_age5_headline": {"description": "Personalized summary headline for age 5+"}, "summary_age5_card1_title": "What they'll learn", "@summary_age5_card1_title": {"description": "Age 5+ learning card title"}, "summary_age5_card1_point1": "Advanced categorization and sorting", "@summary_age5_card1_point1": {"description": "Age 5+ learning point 1"}, "summary_age5_card1_point2": "Abstract pattern completion", "@summary_age5_card1_point2": {"description": "Age 5+ learning point 2"}, "summary_age5_card1_point3": "Critical thinking and strategy", "@summary_age5_card1_point3": {"description": "Age 5+ learning point 3"}, "summary_age5_card1_point4": "Visual memory enhancement", "@summary_age5_card1_point4": {"description": "Age 5+ learning point 4"}, "summary_age5_card2_title": "Why it works for age 5+", "@summary_age5_card2_title": {"description": "Age 5+ why it works card title"}, "summary_age5_card2_point1": "Kindergarten-level cognitive challenges", "@summary_age5_card2_point1": {"description": "Age 5+ why it works point 1"}, "summary_age5_card2_point2": "Builds confidence for school success", "@summary_age5_card2_point2": {"description": "Age 5+ why it works point 2"}, "summary_age5_card2_point3": "Reinforces classroom learning at home", "@summary_age5_card2_point3": {"description": "Age 5+ why it works point 3"}, "summary_age5_card2_point4": "Keeps advanced learners engaged", "@summary_age5_card2_point4": {"description": "Age 5+ why it works point 4"}, "parental_gate_overlay_title": "Parental Verification", "@parental_gate_overlay_title": {"description": "Parental gate overlay title"}, "parental_gate_overlay_instruction": "Please solve this problem to continue:", "@parental_gate_overlay_instruction": {"description": "Parental gate overlay instruction"}, "error_purchase_verification_failed": "Purchase verification failed. Please restart the app.", "@error_purchase_verification_failed": {"description": "Error message when purchase verification fails"}, "paywall_step2_badge_save": "Save 60%", "@paywall_step2_badge_save": {"description": "Paywall step 2 savings badge"}, "paywall_step2_badge_trial": "7-Day Trial", "@paywall_step2_badge_trial": {"description": "Paywall step 2 trial badge"}, "paywall_step2_yearly_title": "Yearly", "@paywall_step2_yearly_title": {"description": "Paywall step 2 yearly plan title"}, "paywall_step2_yearly_per_month": "Just {price}/month", "@paywall_step2_yearly_per_month": {"description": "Paywall step 2 yearly monthly equivalent", "placeholders": {"price": {"type": "String"}}}, "paywall_step2_yearly_savings": "Save 63% • Try free for 7 days", "@paywall_step2_yearly_savings": {"description": "Paywall step 2 yearly savings text"}, "paywall_step2_yearly_feature1": "No charge for 7 days", "@paywall_step2_yearly_feature1": {"description": "Paywall step 2 yearly feature 1"}, "paywall_step2_yearly_feature2": "Cancel anytime during trial", "@paywall_step2_yearly_feature2": {"description": "Paywall step 2 yearly feature 2"}, "paywall_step2_yearly_feature3": "We'll remind you 2 days before trial ends", "@paywall_step2_yearly_feature3": {"description": "Paywall step 2 yearly feature 3"}, "paywall_step2_yearly_feature4": "Full access to all 15 games", "@paywall_step2_yearly_feature4": {"description": "Paywall step 2 yearly feature 4"}, "paywall_step2_yearly_button": "Start FREE Trial", "@paywall_step2_yearly_button": {"description": "Paywall step 2 yearly button text"}, "paywall_step2_monthly_title": "Monthly", "@paywall_step2_monthly_title": {"description": "Paywall step 2 monthly plan title"}, "paywall_step2_monthly_per_week": "{price}/week", "@paywall_step2_monthly_per_week": {"description": "Paywall step 2 monthly weekly equivalent", "placeholders": {"price": {"type": "String"}}}, "paywall_step2_monthly_savings": "Flexible monthly plan", "@paywall_step2_monthly_savings": {"description": "Paywall step 2 monthly savings text"}, "paywall_step2_monthly_feature1": "Cancel anytime", "@paywall_step2_monthly_feature1": {"description": "Paywall step 2 monthly feature 1"}, "paywall_step2_monthly_feature2": "Full access to all 15 games", "@paywall_step2_monthly_feature2": {"description": "Paywall step 2 monthly feature 2"}, "paywall_step2_monthly_button": "Subscribe Monthly", "@paywall_step2_monthly_button": {"description": "Paywall step 2 monthly button text"}, "paywall_step2_weekly_title": "Weekly", "@paywall_step2_weekly_title": {"description": "Paywall step 2 weekly plan title"}, "paywall_step2_weekly_savings": "Try for just one week", "@paywall_step2_weekly_savings": {"description": "Paywall step 2 weekly savings text"}, "paywall_step2_weekly_feature1": "Cancel anytime", "@paywall_step2_weekly_feature1": {"description": "Paywall step 2 weekly feature 1"}, "paywall_step2_weekly_feature2": "Full access to all 15 games", "@paywall_step2_weekly_feature2": {"description": "Paywall step 2 weekly feature 2"}, "paywall_step2_weekly_button": "Subscribe Weekly", "@paywall_step2_weekly_button": {"description": "Paywall step 2 weekly button text"}, "locked_game_headline_personalized": "Unlock All Games for {childName}!", "@locked_game_headline_personalized": {"description": "Pre-paywall headline with child's name", "placeholders": {"childName": {"type": "String"}}}, "locked_game_headline_generic": "Unlock All 15 Educational Games!", "@locked_game_headline_generic": {"description": "Pre-paywall headline without child's name"}, "locked_game_card1_title_age": "Perfect for Your {age}-Year-Old", "@locked_game_card1_title_age": {"description": "Pre-paywall card 1 title with age", "placeholders": {"age": {"type": "int"}}}, "locked_game_card1_title_generic": "Age-Appropriate Learning", "@locked_game_card1_title_generic": {"description": "Pre-paywall card 1 title without age"}, "locked_game_card2_title": "Building Cognitive Abilities", "@locked_game_card2_title": {"description": "Pre-paywall card 2 title"}, "locked_game_card3_title": "{count} More Games to Explore", "@locked_game_card3_title": {"description": "Pre-paywall card 3 title", "placeholders": {"count": {"type": "int"}}}, "locked_game_card3_subtitle": "Unlock the full collection of educational games", "@locked_game_card3_subtitle": {"description": "Pre-paywall card 3 subtitle"}, "locked_game_age_skill_1_generic": "Shape and color recognition", "@locked_game_age_skill_1_generic": {"description": "Generic skill 1"}, "locked_game_age_skill_2_generic": "Problem-solving abilities", "@locked_game_age_skill_2_generic": {"description": "Generic skill 2"}, "locked_game_age_skill_3_generic": "Hand-eye coordination", "@locked_game_age_skill_3_generic": {"description": "Generic skill 3"}, "locked_game_age_skill_4_generic": "Memory and focus", "@locked_game_age_skill_4_generic": {"description": "Generic skill 4"}, "locked_game_age_skill_1_age2": "Basic shape recognition", "@locked_game_age_skill_1_age2": {"description": "Age 2 skill 1"}, "locked_game_age_skill_2_age2": "Simple color matching", "@locked_game_age_skill_2_age2": {"description": "Age 2 skill 2"}, "locked_game_age_skill_3_age2": "Hand-eye coordination", "@locked_game_age_skill_3_age2": {"description": "Age 2 skill 3"}, "locked_game_age_skill_4_age2": "Cause and effect understanding", "@locked_game_age_skill_4_age2": {"description": "Age 2 skill 4"}, "locked_game_age_skill_1_age3": "Advanced shape sorting", "@locked_game_age_skill_1_age3": {"description": "Age 3 skill 1"}, "locked_game_age_skill_2_age3": "Pattern identification", "@locked_game_age_skill_2_age3": {"description": "Age 3 skill 2"}, "locked_game_age_skill_3_age3": "Color mixing concepts", "@locked_game_age_skill_3_age3": {"description": "Age 3 skill 3"}, "locked_game_age_skill_4_age3": "Early problem-solving", "@locked_game_age_skill_4_age3": {"description": "Age 3 skill 4"}, "locked_game_age_skill_1_age4": "Complex pattern recognition", "@locked_game_age_skill_1_age4": {"description": "Age 4 skill 1"}, "locked_game_age_skill_2_age4": "Categorical thinking", "@locked_game_age_skill_2_age4": {"description": "Age 4 skill 2"}, "locked_game_age_skill_3_age4": "Spatial reasoning", "@locked_game_age_skill_3_age4": {"description": "Age 4 skill 3"}, "locked_game_age_skill_4_age4": "Pre-reading visual skills", "@locked_game_age_skill_4_age4": {"description": "Age 4 skill 4"}, "locked_game_age_skill_1_age5": "Advanced categorization", "@locked_game_age_skill_1_age5": {"description": "Age 5+ skill 1"}, "locked_game_age_skill_2_age5": "Abstract pattern completion", "@locked_game_age_skill_2_age5": {"description": "Age 5+ skill 2"}, "locked_game_age_skill_3_age5": "Critical thinking", "@locked_game_age_skill_3_age5": {"description": "Age 5+ skill 3"}, "locked_game_age_skill_4_age5": "Visual memory enhancement", "@locked_game_age_skill_4_age5": {"description": "Age 5+ skill 4"}, "locked_game_card2_content_default": "Develop essential cognitive skills through play", "@locked_game_card2_content_default": {"description": "Card 2 default content"}, "locked_game_card2_content_school": "Build skills for preschool and kindergarten success", "@locked_game_card2_content_school": {"description": "Card 2 content for school readiness goal"}, "locked_game_card2_content_cognitive": "Enhance memory, focus, and problem-solving abilities", "@locked_game_card2_content_cognitive": {"description": "Card 2 content for cognitive development goal"}, "locked_game_card2_content_screentime": "Quality educational content that parents can feel good about", "@locked_game_card2_content_screentime": {"description": "Card 2 content for screen time replacement goal"}, "locked_game_card2_content_engagement": "Keep your child engaged with fun, educational activities", "@locked_game_card2_content_engagement": {"description": "Card 2 content for engagement goal"}}
// lib/ui/screens/paywall_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:brainy_bunny/services/subscription_manager.dart';
import 'package:brainy_bunny/ui/widgets/parent_gate_dialog.dart';
import 'package:brainy_bunny/constants/app_constants.dart';

/// New 3-Step Paywall Screen
/// Single scrollable screen showing value, social proof, and pricing
class PaywallScreen extends StatefulWidget {
  final VoidCallback? onClose;
  final VoidCallback? onSubscribed;

  const PaywallScreen({
    super.key,
    this.onClose,
    this.onSubscribed,
  });

  @override
  State<PaywallScreen> createState() => _PaywallScreenState();
}

class _PaywallScreenState extends State<PaywallScreen> {
  final _subscriptionManager = SubscriptionManager();
  bool _isLoading = false;
  String? _errorMessage;

  Future<void> _handleSubscribe(String productId) async {
    // Show parent gate first
    final verified = await showParentGate(context);
    if (!verified) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final success = await _subscriptionManager.purchaseSubscription(productId);

      if (success && mounted) {
        // Purchase successful
        widget.onSubscribed?.call();
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final size = MediaQuery.of(context).size;

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Stack(
          children: [
            // Main content
            SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    const SizedBox(height: 48), // Space for close button

                    // SECTION 1: Value Reminder
                    _buildSection1(l10n),

                    const SizedBox(height: 32),

                    // SECTION 2: Social Proof
                    _buildSection2(l10n),

                    const SizedBox(height: 32),

                    // SECTION 3: Pricing (MOST IMPORTANT)
                    _buildSection3(l10n),

                    const SizedBox(height: 24),

                    // Error message
                    if (_errorMessage != null) ...[
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.red.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.red.shade200),
                        ),
                        child: Text(
                          _errorMessage!,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.red.shade900,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      const SizedBox(height: 24),
                    ],

                    // Continue with free games link
                    if (widget.onClose != null)
                      TextButton(
                        onPressed: widget.onClose,
                        child: Text(
                          l10n.paywall_continue_free_link,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade600,
                            decoration: TextDecoration.underline,
                          ),
                        ),
                      ),

                    const SizedBox(height: 24),
                  ],
                ),
              ),
            ),

            // Close button (X)
            Positioned(
              top: 8,
              right: 8,
              child: IconButton(
                onPressed: widget.onClose,
                icon: Icon(
                  Icons.close,
                  color: Colors.grey.shade700,
                  size: 28,
                ),
              ),
            ),

            // Loading overlay
            if (_isLoading)
              Container(
                color: Colors.black54,
                child: const Center(
                  child: CircularProgressIndicator(
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildSection1(AppLocalizations l10n) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Headline
        Text(
          l10n.paywall_section1_headline,
          style: const TextStyle(
            fontSize: 26,
            fontWeight: FontWeight.bold,
            color: Color(0xFF2C3E50),
            height: 1.3,
          ),
          textAlign: TextAlign.center,
        ),

        const SizedBox(height: 24),

        // Game category icons grid (simplified - 15 game icons)
        Wrap(
          alignment: WrapAlignment.center,
          spacing: 12,
          runSpacing: 12,
          children: [
            _GameCategoryIcon(Icons.category, Colors.blue),
            _GameCategoryIcon(Icons.palette, Colors.orange),
            _GameCategoryIcon(Icons.pets, Colors.green),
            _GameCategoryIcon(Icons.numbers, Colors.purple),
            _GameCategoryIcon(Icons.abc, Colors.red),
            _GameCategoryIcon(Icons.emoji_nature, Colors.teal),
            _GameCategoryIcon(Icons.work, Colors.indigo),
            _GameCategoryIcon(Icons.sports_soccer, Colors.amber),
            _GameCategoryIcon(Icons.music_note, Colors.pink),
            _GameCategoryIcon(Icons.restaurant, Colors.brown),
            _GameCategoryIcon(Icons.directions_car, Colors.cyan),
            _GameCategoryIcon(Icons.stars, Colors.deepOrange),
            _GameCategoryIcon(Icons.extension, Colors.lime),
            _GameCategoryIcon(Icons.auto_awesome, Colors.deepPurple),
            _GameCategoryIcon(Icons.favorite, Colors.redAccent),
          ],
        ),

        const SizedBox(height: 24),

        // 6 checkmarked features
        _CheckmarkFeature(text: l10n.paywall_section1_feature_1),
        const SizedBox(height: 8),
        _CheckmarkFeature(text: l10n.paywall_section1_feature_2),
        const SizedBox(height: 8),
        _CheckmarkFeature(text: l10n.paywall_section1_feature_3),
        const SizedBox(height: 8),
        _CheckmarkFeature(text: l10n.paywall_section1_feature_4),
        const SizedBox(height: 8),
        _CheckmarkFeature(text: l10n.paywall_section1_feature_5),
        const SizedBox(height: 8),
        _CheckmarkFeature(text: l10n.paywall_section1_feature_6),
      ],
    );
  }

  Widget _buildSection2(AppLocalizations l10n) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            const Color(0xFF27AE60).withOpacity(0.1),
            const Color(0xFF27AE60).withOpacity(0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(0xFF27AE60).withOpacity(0.3),
          width: 2,
        ),
      ),
      child: Column(
        children: [
          // Large badge
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            decoration: BoxDecoration(
              color: const Color(0xFF27AE60),
              borderRadius: BorderRadius.circular(24),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF27AE60).withOpacity(0.3),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.verified,
                  color: Colors.white,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Text(
                  l10n.paywall_section2_badge,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Supporting text
          Text(
            l10n.paywall_section2_text,
            style: TextStyle(
              fontSize: 15,
              color: Colors.grey.shade800,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSection3(AppLocalizations l10n) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Pricing cards
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Weekly plan
            Expanded(
              child: _PricingCard(
                title: l10n.paywall_section3_weekly_title,
                price: _subscriptionManager.weeklyProduct?.price ??
                    AppConstants.FALLBACK_PRICE_WEEKLY,
                subtext: l10n.paywall_section3_weekly_subtext,
                feature: l10n.paywall_section3_weekly_feature,
                buttonText: l10n.paywall_section3_weekly_button,
                onPressed: () => _handleSubscribe(
                  AppConstants.WEEKLY_SUBSCRIPTION_IOS,
                ),
                isHighlighted: false,
              ),
            ),

            const SizedBox(width: 12),

            // Yearly plan (BEST VALUE)
            Expanded(
              child: _PricingCard(
                title: l10n.paywall_section3_yearly_title,
                price: _subscriptionManager.yearlyProduct?.price ??
                    AppConstants.FALLBACK_PRICE_YEARLY,
                badge: l10n.paywall_section3_yearly_badge,
                highlight: l10n.paywall_section3_yearly_highlight,
                subtext: _getMonthlyEquivalent(),
                finePrint: l10n.paywall_section3_yearly_fine_print(
                  _subscriptionManager.yearlyProduct?.price ??
                      AppConstants.FALLBACK_PRICE_YEARLY,
                ),
                feature: l10n.paywall_section3_yearly_feature,
                buttonText: l10n.paywall_section3_yearly_button,
                onPressed: () => _handleSubscribe(
                  AppConstants.YEARLY_SUBSCRIPTION_IOS,
                ),
                isHighlighted: true,
              ),
            ),

            const SizedBox(width: 12),

            // Monthly plan
            Expanded(
              child: _PricingCard(
                title: l10n.paywall_section3_monthly_title,
                price: _subscriptionManager.monthlyProduct?.price ??
                    AppConstants.FALLBACK_PRICE_MONTHLY,
                subtext: l10n.paywall_section3_monthly_subtext,
                feature: l10n.paywall_section3_monthly_feature,
                buttonText: l10n.paywall_section3_monthly_button,
                onPressed: () => _handleSubscribe(
                  AppConstants.MONTHLY_SUBSCRIPTION_IOS,
                ),
                isHighlighted: false,
              ),
            ),
          ],
        ),

        const SizedBox(height: 24),

        // Trust elements
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey.shade100,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              _TrustElement(text: l10n.paywall_trust_element_1),
              const SizedBox(height: 8),
              _TrustElement(text: l10n.paywall_trust_element_2),
              const SizedBox(height: 8),
              _TrustElement(text: l10n.paywall_trust_element_3),
              const SizedBox(height: 8),
              _TrustElement(text: l10n.paywall_trust_element_4),
            ],
          ),
        ),

        const SizedBox(height: 16),

        // Disclaimer
        Text(
          l10n.paywall_disclaimer,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
            height: 1.4,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  String _getMonthlyEquivalent() {
    final l10n = AppLocalizations.of(context)!;
    final yearlyPrice = _subscriptionManager.yearlyProduct?.price ??
        AppConstants.FALLBACK_PRICE_YEARLY;

    // Extract numeric value from price string (e.g., "€39.99" -> "3.33")
    final numericValue = yearlyPrice.replaceAll(RegExp(r'[^\d.]'), '');
    final yearlyAmount = double.tryParse(numericValue) ?? 39.99;
    final monthlyEquivalent = (yearlyAmount / 12).toStringAsFixed(2);

    // Keep currency symbol from original price
    final currencySymbol = yearlyPrice.replaceAll(RegExp(r'[\d.]'), '').trim();

    return l10n.paywall_section3_yearly_breakdown(
      '$currencySymbol$monthlyEquivalent',
    );
  }
}

class _GameCategoryIcon extends StatelessWidget {
  final IconData icon;
  final Color color;

  const _GameCategoryIcon(this.icon, this.color);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: color.withOpacity(0.15),
        shape: BoxShape.circle,
      ),
      child: Icon(
        icon,
        color: color,
        size: 24,
      ),
    );
  }
}

class _CheckmarkFeature extends StatelessWidget {
  final String text;

  const _CheckmarkFeature({required this.text});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        const Icon(
          Icons.check_circle,
          color: Color(0xFF27AE60),
          size: 20,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            text,
            style: TextStyle(
              fontSize: 15,
              color: Colors.grey.shade800,
              height: 1.3,
            ),
          ),
        ),
      ],
    );
  }
}

class _PricingCard extends StatelessWidget {
  final String title;
  final String price;
  final String? badge;
  final String? highlight;
  final String subtext;
  final String? finePrint;
  final String feature;
  final String buttonText;
  final VoidCallback onPressed;
  final bool isHighlighted;

  const _PricingCard({
    required this.title,
    required this.price,
    this.badge,
    this.highlight,
    required this.subtext,
    this.finePrint,
    required this.feature,
    required this.buttonText,
    required this.onPressed,
    required this.isHighlighted,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isHighlighted
            ? const Color(0xFF27AE60).withOpacity(0.1)
            : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isHighlighted
              ? const Color(0xFF27AE60)
              : Colors.grey.shade300,
          width: isHighlighted ? 2 : 1,
        ),
      ),
      child: Column(
        children: [
          // Badge (if present)
          if (badge != null) ...[
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: const Color(0xFFF39C12),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                badge!,
                style: const TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 8),
          ],

          // Title
          Text(
            title,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade900,
            ),
          ),

          const SizedBox(height: 4),

          // Highlight (if present)
          if (highlight != null) ...[
            Text(
              highlight!,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: Color(0xFF27AE60),
              ),
            ),
            const SizedBox(height: 4),
          ],

          // Price
          Text(
            price,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: isHighlighted
                  ? const Color(0xFF27AE60)
                  : const Color(0xFF2C3E50),
            ),
          ),

          // Subtext
          Text(
            subtext,
            style: TextStyle(
              fontSize: 11,
              color: Colors.grey.shade600,
            ),
            textAlign: TextAlign.center,
          ),

          // Fine print (if present)
          if (finePrint != null) ...[
            const SizedBox(height: 2),
            Text(
              finePrint!,
              style: TextStyle(
                fontSize: 10,
                color: Colors.grey.shade500,
              ),
              textAlign: TextAlign.center,
            ),
          ],

          const SizedBox(height: 8),

          // Feature
          Text(
            feature,
            style: TextStyle(
              fontSize: 11,
              color: Colors.grey.shade700,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 12),

          // Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: onPressed,
              style: ElevatedButton.styleFrom(
                backgroundColor: isHighlighted
                    ? const Color(0xFF27AE60)
                    : const Color(0xFF4A90E2),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 10),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                elevation: 0,
              ),
              child: Text(
                buttonText,
                style: const TextStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _TrustElement extends StatelessWidget {
  final String text;

  const _TrustElement({required this.text});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Icon(
          Icons.check_circle_outline,
          color: const Color(0xFF27AE60),
          size: 16,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            text,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade700,
            ),
          ),
        ),
      ],
    );
  }
}

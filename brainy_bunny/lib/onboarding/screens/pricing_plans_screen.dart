// lib/onboarding/screens/pricing_plans_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:brainy_bunny/constants/subscription_constants.dart';
import 'package:brainy_bunny/constants/app_constants.dart';
import 'package:brainy_bunny/onboarding/widgets/parental_gate_dialog.dart';
import 'package:brainy_bunny/services/subscription_manager.dart';

/// Consolidated pricing screen with 3 subscription plans
/// Responsive design for mobile and tablet/desktop
class PricingPlansScreen extends StatefulWidget {
  final VoidCallback onContinue; // Called after successful purchase
  final VoidCallback onSkip; // Called when user wants to use free version

  const PricingPlansScreen({
    super.key,
    required this.onContinue,
    required this.onSkip,
  });

  @override
  State<PricingPlansScreen> createState() => _PricingPlansScreenState();
}

class _PricingPlansScreenState extends State<PricingPlansScreen> {
  String _selectedPlan = SubscriptionConstants.SUBSCRIPTION_YEARLY; // Default to yearly
  bool _isLoading = false;
  String? _errorMessage;

  // Product prices (will be loaded from store)
  String _yearlyPrice = SubscriptionConstants.FALLBACK_YEARLY_PRICE;
  String _monthlyPrice = SubscriptionConstants.FALLBACK_MONTHLY_PRICE;
  String _weeklyPrice = SubscriptionConstants.FALLBACK_WEEKLY_PRICE;
  String _yearlyMonthlyEquivalent = '€3.33';

  @override
  void initState() {
    super.initState();
    _loadPrices();
  }

  Future<void> _loadPrices() async {
    try {
      // Load real prices from the store
      final subscriptionManager = SubscriptionManager();
      await subscriptionManager.initialize(
        revenueCatApiKey: AppConstants.REVENUECAT_API_KEY,
      );

      // Get actual prices from store using product getters
      if (mounted && subscriptionManager.productsLoaded) {
        setState(() {
          // Yearly product
          final yearlyProduct = subscriptionManager.yearlyProduct;
          if (yearlyProduct != null) {
            _yearlyPrice = yearlyProduct.price;
            // Calculate monthly equivalent
            final yearlyAmount = _parsePrice(yearlyProduct.price);
            if (yearlyAmount != null) {
              final monthlyEquivalent = yearlyAmount / 12;
              _yearlyMonthlyEquivalent = '€${monthlyEquivalent.toStringAsFixed(2)}';
            }
          }

          // Monthly product
          final monthlyProduct = subscriptionManager.monthlyProduct;
          if (monthlyProduct != null) {
            _monthlyPrice = monthlyProduct.price;
          }

          // Weekly product
          final weeklyProduct = subscriptionManager.weeklyProduct;
          if (weeklyProduct != null) {
            _weeklyPrice = weeklyProduct.price;
          }
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('Failed to load prices: $e');
      }
      // Keep using fallback prices
    }
  }

  double? _parsePrice(String priceString) {
    // Remove currency symbols and parse
    final cleanPrice = priceString.replaceAll(RegExp(r'[^\d.,]'), '');
    final normalizedPrice = cleanPrice.replaceAll(',', '.');
    return double.tryParse(normalizedPrice);
  }

  void _showParentalGate() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => ParentalGateDialog(
        onVerified: _handleSubscribe,
      ),
    );
  }

  Future<void> _handleSubscribe() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final subscriptionManager = SubscriptionManager();
      final success = await subscriptionManager.purchaseSubscription(_selectedPlan);

      if (mounted) {
        if (success) {
          // Purchase successful - call onContinue
          widget.onContinue();
        } else {
          setState(() {
            _errorMessage = 'Purchase failed. Please try again.';
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'An error occurred. Please try again.';
          _isLoading = false;
        });
      }
      if (kDebugMode) {
        print('Subscription error: $e');
      }
    }
  }

  Future<void> _handleRestorePurchases() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final subscriptionManager = SubscriptionManager();
      final restored = await subscriptionManager.restorePurchases();

      if (mounted) {
        if (restored) {
          // Purchase restored - call onContinue
          widget.onContinue();
        } else {
          setState(() {
            _errorMessage = 'No previous purchases found.';
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to restore purchases.';
          _isLoading = false;
        });
      }
      if (kDebugMode) {
        print('Restore error: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final isTabletOrDesktop = size.width >= 600;

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const SizedBox(height: 16),

                      // Header
                      const Text(
                        'Choose Your Plan',
                        style: TextStyle(
                          fontSize: 32,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF333333),
                        ),
                        textAlign: TextAlign.center,
                      ),

                      const SizedBox(height: 8),

                      Text(
                        'Start with a 7-day free trial',
                        style: TextStyle(
                          fontSize: 18,
                          color: Colors.grey.shade600,
                        ),
                        textAlign: TextAlign.center,
                      ),

                      const SizedBox(height: 32),

                      // Pricing plans (responsive layout)
                      if (isTabletOrDesktop)
                        _buildHorizontalPlans()
                      else
                        _buildVerticalPlans(),

                      const SizedBox(height: 24),

                      // Trial info box
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: const Color(0xFFE3F2FD),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: const Color(0xFF4A90E2).withOpacity(0.3),
                            width: 1.5,
                          ),
                        ),
                        child: Column(
                          children: [
                            const Row(
                              children: [
                                Icon(
                                  Icons.info_outline,
                                  color: Color(0xFF4A90E2),
                                  size: 20,
                                ),
                                SizedBox(width: 8),
                                Text(
                                  'About Your Free Trial',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Color(0xFF333333),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Try all 15 games free for 7 days. Cancel anytime before trial ends to avoid charges. After trial, subscription auto-renews unless canceled.',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey.shade700,
                                height: 1.4,
                              ),
                            ),
                          ],
                        ),
                      ),

                      if (_errorMessage != null) ...[
                        const SizedBox(height: 16),
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.red.shade50,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: Colors.red.shade300,
                              width: 1,
                            ),
                          ),
                          child: Text(
                            _errorMessage!,
                            style: TextStyle(
                              color: Colors.red.shade700,
                              fontSize: 14,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ],

                      const SizedBox(height: 24),

                      // Subscribe button
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: _showParentalGate,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF4A90E2),
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 18),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(28),
                            ),
                            elevation: 2,
                          ),
                          child: const Text(
                            'Start Free Trial',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),

                      const SizedBox(height: 16),

                      // Restore purchases link
                      TextButton(
                        onPressed: _handleRestorePurchases,
                        child: Text(
                          'Restore Purchases',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade600,
                            decoration: TextDecoration.underline,
                          ),
                        ),
                      ),

                      const SizedBox(height: 8),

                      // Skip to free version
                      TextButton(
                        onPressed: widget.onSkip,
                        child: Text(
                          'Continue with 5 free games',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ),

                      const SizedBox(height: 16),
                    ],
                  ),
                ),
              ),
      ),
    );
  }

  Widget _buildVerticalPlans() {
    return Column(
      children: [
        _buildPlanCard(
          planId: SubscriptionConstants.SUBSCRIPTION_YEARLY,
          title: 'Yearly',
          price: _yearlyPrice,
          period: 'per year',
          badge: 'Save ${SubscriptionConstants.YEARLY_DISCOUNT_PERCENT}%',
          showTrial: true,
          equivalent: 'Just $_yearlyMonthlyEquivalent/month',
          isRecommended: true,
        ),
        const SizedBox(height: 12),
        _buildPlanCard(
          planId: SubscriptionConstants.SUBSCRIPTION_MONTHLY,
          title: 'Monthly',
          price: _monthlyPrice,
          period: 'per month',
        ),
        const SizedBox(height: 12),
        _buildPlanCard(
          planId: SubscriptionConstants.SUBSCRIPTION_WEEKLY,
          title: 'Weekly',
          price: _weeklyPrice,
          period: 'per week',
        ),
      ],
    );
  }

  Widget _buildHorizontalPlans() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: _buildPlanCard(
            planId: SubscriptionConstants.SUBSCRIPTION_YEARLY,
            title: 'Yearly',
            price: _yearlyPrice,
            period: 'per year',
            badge: 'Save ${SubscriptionConstants.YEARLY_DISCOUNT_PERCENT}%',
            showTrial: true,
            equivalent: 'Just $_yearlyMonthlyEquivalent/month',
            isRecommended: true,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildPlanCard(
            planId: SubscriptionConstants.SUBSCRIPTION_MONTHLY,
            title: 'Monthly',
            price: _monthlyPrice,
            period: 'per month',
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildPlanCard(
            planId: SubscriptionConstants.SUBSCRIPTION_WEEKLY,
            title: 'Weekly',
            price: _weeklyPrice,
            period: 'per week',
          ),
        ),
      ],
    );
  }

  Widget _buildPlanCard({
    required String planId,
    required String title,
    required String price,
    required String period,
    String? badge,
    bool showTrial = false,
    String? equivalent,
    bool isRecommended = false,
  }) {
    final isSelected = _selectedPlan == planId;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedPlan = planId;
        });
      },
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: isSelected
              ? const Color(0xFF4A90E2).withOpacity(0.1)
              : Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected
                ? const Color(0xFF4A90E2)
                : Colors.grey.shade300,
            width: isSelected ? 2.5 : 1.5,
          ),
          boxShadow: isRecommended
              ? [
                  BoxShadow(
                    color: const Color(0xFF4A90E2).withOpacity(0.2),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ]
              : null,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Badges row
            if (badge != null || showTrial)
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  if (badge != null)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: const Color(0xFF4CAF50),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        badge,
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  if (showTrial)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: const Color(0xFF4A90E2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Text(
                        '7-Day Free Trial',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                ],
              ),

            if (badge != null || showTrial) const SizedBox(height: 12),

            // Plan title
            Text(
              title,
              style: const TextStyle(
                fontSize: 22,
                fontWeight: FontWeight.bold,
                color: Color(0xFF333333),
              ),
            ),

            const SizedBox(height: 8),

            // Price
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  price,
                  style: const TextStyle(
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF4A90E2),
                  ),
                ),
                const SizedBox(width: 4),
                Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Text(
                    period,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ),
              ],
            ),

            // Weekly equivalent for yearly
            if (equivalent != null) ...[
              const SizedBox(height: 4),
              Text(
                equivalent,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],

            const SizedBox(height: 12),

            // Selection indicator
            if (isSelected)
              Row(
                children: [
                  const Icon(
                    Icons.check_circle,
                    color: Color(0xFF4A90E2),
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Selected',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey.shade800,
                    ),
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }
}

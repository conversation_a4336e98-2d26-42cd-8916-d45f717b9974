// lib/services/subscription_manager.dart
import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:in_app_purchase_android/in_app_purchase_android.dart';
import 'package:in_app_purchase_storekit/in_app_purchase_storekit.dart';
import 'package:purchases_flutter/purchases_flutter.dart' as revenue_cat;
import 'package:brainy_bunny/constants/app_constants.dart';
import 'package:brainy_bunny/constants/subscription_constants.dart';
import 'package:brainy_bunny/services/analytics/analytics_manager.dart';
import 'package:brainy_bunny/services/purchase_manager.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';

/// Subscription manager using native in_app_purchase for all purchase operations
/// RevenueCat is used ONLY for customer tracking and analytics
/// Handles trial period, subscription tiers, and grandfathering of lifetime users
class SubscriptionManager {
  // Singleton pattern
  static final SubscriptionManager _instance = SubscriptionManager._internal();
  factory SubscriptionManager() => _instance;
  SubscriptionManager._internal();

  // Core services
  final InAppPurchase _inAppPurchase = InAppPurchase.instance;
  static const _secureStorage = FlutterSecureStorage();

  // State
  bool _isInitialized = false;
  bool _isAvailable = false;
  bool _hasActiveSubscription = false;
  bool _isLifetimeUser = false;
  bool _isInTrialPeriod = false;
  String? _activeProductId;
  DateTime? _expirationDate;
  DateTime? _purchaseDate;

  // Product details
  Map<String, ProductDetails> _products = {};
  bool _productsLoaded = false;
  String? _productLoadError;

  // Subscriptions
  StreamSubscription<List<PurchaseDetails>>? _purchaseSubscription;
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;

  // Streams
  final _subscriptionStateController = StreamController<bool>.broadcast();
  Stream<bool> get subscriptionStream => _subscriptionStateController.stream;

  // Getters
  bool get isInitialized => _isInitialized;
  bool get isAvailable => _isAvailable;
  bool get hasActiveSubscription => _hasActiveSubscription || _isLifetimeUser;
  bool get isLifetimeUser => _isLifetimeUser;
  bool get isInTrialPeriod => _isInTrialPeriod;
  bool get productsLoaded => _productsLoaded;
  String? get productLoadError => _productLoadError;
  String? get activeProductId => _activeProductId;
  DateTime? get expirationDate => _expirationDate;

  // Product details getters
  ProductDetails? get weeklyProduct => _products[AppConstants.WEEKLY_SUBSCRIPTION_IOS];
  ProductDetails? get monthlyProduct => _products[AppConstants.MONTHLY_SUBSCRIPTION_IOS];
  ProductDetails? get yearlyProduct => _products[AppConstants.YEARLY_SUBSCRIPTION_IOS];

  /// Get active subscription tier
  String? get activeSubscriptionTier {
    if (_isLifetimeUser) return SubscriptionConstants.TIER_LIFETIME;
    if (_activeProductId == null) return null;

    if (_activeProductId!.contains('weekly')) return SubscriptionConstants.TIER_WEEKLY;
    if (_activeProductId!.contains('monthly')) return SubscriptionConstants.TIER_MONTHLY;
    if (_activeProductId!.contains('yearly')) return SubscriptionConstants.TIER_YEARLY;

    return null;
  }

  /// Initialize subscription manager
  Future<void> initialize({required String revenueCatApiKey}) async {
    if (_isInitialized) return;

    try {
      if (kDebugMode) {
        print('🔧 === SUBSCRIPTION MANAGER INITIALIZATION ===');
        print('   Platform: ${Platform.operatingSystem}');
        print('   Bundle ID: com.goodkarmalab.brainyBunny');
      }

      // Check IAP availability
      _isAvailable = await _inAppPurchase.isAvailable();

      if (kDebugMode) {
        print('   IAP Available: $_isAvailable');
      }

      if (!_isAvailable) {
        _productLoadError = 'IAP service not available';
        _isInitialized = true;
        return;
      }

      // Initialize RevenueCat for analytics/tracking only
      await _initializeRevenueCat(revenueCatApiKey);

      // Setup purchase listener
      _setupPurchaseListener();

      // Check for grandfathered lifetime users (from old one-time purchase system)
      await _checkLifetimeUser();

      // Load subscription status from local storage and cloud
      await _loadSubscriptionStatus();

      // Load product details
      await loadProducts();

      // Check for existing subscriptions
      await restorePurchases();

      // Setup connectivity listener for sync
      _setupConnectivityListener();

      _isInitialized = true;

      if (kDebugMode) {
        print('✅ === SUBSCRIPTION INITIALIZATION COMPLETE ===');
        print('   Available: $_isAvailable');
        print('   Active subscription: $_hasActiveSubscription');
        print('   Lifetime user: $_isLifetimeUser');
        print('   In trial: $_isInTrialPeriod');
        print('   Product ID: $_activeProductId');
        print('   Expiration: $_expirationDate');
        print('   Products loaded: $_productsLoaded');
        print('============================================');
      }

    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('❌ Subscription initialization error: $e');
        print('   Stack trace: $stackTrace');
      }

      _isInitialized = true;
      _isAvailable = false;
      _productLoadError = 'Initialization failed: $e';
    }
  }

  /// Initialize RevenueCat for analytics tracking only
  Future<void> _initializeRevenueCat(String apiKey) async {
    try {
      final configuration = revenue_cat.PurchasesConfiguration(apiKey);
      await revenue_cat.Purchases.configure(configuration);

      if (kDebugMode) {
        await revenue_cat.Purchases.setLogLevel(revenue_cat.LogLevel.debug);
        print('   RevenueCat configured for tracking');
      }
    } catch (e) {
      if (kDebugMode) {
        print('   ⚠️ RevenueCat initialization failed: $e');
        print('   Continuing without RevenueCat tracking');
      }
    }
  }

  /// Load subscription product details from App Store/Google Play
  Future<bool> loadProducts() async {
    if (!_isAvailable) {
      _productLoadError = 'IAP service not available';
      return false;
    }

    try {
      if (kDebugMode) {
        print('🏷️ === LOADING SUBSCRIPTION PRODUCTS ===');
      }

      final response = await _inAppPurchase
          .queryProductDetails(AppConstants.SUBSCRIPTION_PRODUCT_IDS)
          .timeout(const Duration(seconds: 10));

      if (kDebugMode) {
        print('   Products found: ${response.productDetails.length}');
        print('   Not found: ${response.notFoundIDs}');
      }

      if (response.error != null) {
        _productLoadError = response.error!.message;
        if (kDebugMode) {
          print('   ❌ Error: ${response.error!.code} - ${response.error!.message}');
        }
        return false;
      }

      if (response.productDetails.isEmpty) {
        _productLoadError = 'No subscription products found in store';
        if (kDebugMode) {
          print('   ❌ No products found. Check:');
          print('   - Product IDs configured in store');
          print('   - App uploaded to store (even for testing)');
          print('   - Test account signed in');
        }
        return false;
      }

      // Store products by ID
      for (var product in response.productDetails) {
        _products[product.id] = product;
        if (kDebugMode) {
          print('   ✅ ${product.id}: ${product.price}');
        }
      }

      _productsLoaded = true;
      _productLoadError = null;

      return true;

    } catch (e) {
      _productLoadError = 'Failed to load products: $e';
      if (kDebugMode) {
        print('   ❌ Product loading error: $e');
      }
      return false;
    }
  }

  /// Purchase a subscription
  Future<bool> purchaseSubscription(String productId) async {
    if (!_isAvailable) {
      if (kDebugMode) {
        print('❌ Cannot purchase: IAP not available');
      }
      return false;
    }

    if (!_productsLoaded) {
      if (kDebugMode) {
        print('❌ Cannot purchase: Products not loaded');
      }
      return false;
    }

    final product = _products[productId];
    if (product == null) {
      if (kDebugMode) {
        print('❌ Cannot purchase: Product not found: $productId');
      }
      return false;
    }

    try {
      if (kDebugMode) {
        print('🛒 === STARTING SUBSCRIPTION PURCHASE ===');
        print('   Product: ${product.title}');
        print('   Price: ${product.price}');
        print('   ID: $productId');
      }

      // Track purchase attempt
      await AnalyticsManager.instance.trackEvent(
        eventName: SubscriptionConstants.SUBSCRIPTION_IDS.contains(productId)
            ? AppConstants.EVENT_TRIAL_STARTED
            : AppConstants.EVENT_SUBSCRIPTION_PURCHASED,
        parameters: {
          'product_id': productId,
          'price': product.rawPrice.toString(),
          'currency': product.currencyCode,
        },
      );

      // Initiate purchase
      final purchaseParam = PurchaseParam(productDetails: product);
      final success = await _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);

      if (kDebugMode) {
        print('   Purchase initiated: $success');
      }

      return true;

    } catch (e) {
      if (kDebugMode) {
        print('❌ Purchase error: $e');
      }
      return false;
    }
  }

  /// Setup purchase listener to handle purchase updates
  void _setupPurchaseListener() {
    final purchaseStream = _inAppPurchase.purchaseStream;
    _purchaseSubscription = purchaseStream.listen(
      _handlePurchaseUpdates,
      onError: (error) {
        if (kDebugMode) {
          print('❌ Purchase stream error: $error');
        }
      },
    );
  }

  /// Handle purchase updates from the store
  void _handlePurchaseUpdates(List<PurchaseDetails> purchaseDetailsList) async {
    for (final purchaseDetails in purchaseDetailsList) {
      if (kDebugMode) {
        print('🔄 Purchase update: ${purchaseDetails.status}');
        print('   Product ID: ${purchaseDetails.productID}');
        print('   Purchase ID: ${purchaseDetails.purchaseID}');
      }

      if (purchaseDetails.status == PurchaseStatus.pending) {
        if (kDebugMode) {
          print('⏳ Purchase pending...');
        }
      } else if (purchaseDetails.status == PurchaseStatus.error) {
        if (kDebugMode) {
          print('❌ Purchase error: ${purchaseDetails.error}');
        }

        // Track error
        await AnalyticsManager.instance.trackEvent(
          eventName: AppConstants.EVENT_PURCHASE_FAILED,
          parameters: {
            'error_code': purchaseDetails.error?.code ?? 'unknown',
            'error_message': purchaseDetails.error?.message ?? 'Unknown error',
          },
        );

      } else if (purchaseDetails.status == PurchaseStatus.canceled) {
        if (kDebugMode) {
          print('🚫 Purchase canceled by user');
        }

      } else if (purchaseDetails.status == PurchaseStatus.purchased ||
          purchaseDetails.status == PurchaseStatus.restored) {
        if (kDebugMode) {
          print('✅ Purchase successful!');
        }

        await _handleSuccessfulPurchase(purchaseDetails);
      }

      // Complete the purchase
      if (purchaseDetails.pendingCompletePurchase) {
        await _inAppPurchase.completePurchase(purchaseDetails);
        if (kDebugMode) {
          print('✅ Purchase completed');
        }
      }
    }
  }

  /// Handle successful purchase
  Future<void> _handleSuccessfulPurchase(PurchaseDetails purchaseDetails) async {
    try {
      // Update local state
      _hasActiveSubscription = true;
      _activeProductId = purchaseDetails.productID;
      _purchaseDate = DateTime.now();

      // Calculate expiration based on subscription type
      _expirationDate = _calculateExpirationDate(purchaseDetails.productID);

      // Check if in trial period (for yearly with 7-day trial)
      if (purchaseDetails.productID.contains('yearly')) {
        _isInTrialPeriod = _purchaseDate != null &&
            DateTime.now().difference(_purchaseDate!).inDays < 7;
      }

      // Save to local storage
      await _saveSubscriptionState(purchaseDetails);

      // Save to Firebase
      await _saveToFirebase(purchaseDetails);

      // Send to RevenueCat for tracking only
      await _trackWithRevenueCat(purchaseDetails);

      // Notify listeners
      _subscriptionStateController.add(true);

      // Track analytics
      await AnalyticsManager.instance.trackEvent(
        eventName: AppConstants.EVENT_SUBSCRIPTION_PURCHASED,
        parameters: {
          'product_id': purchaseDetails.productID,
          'is_trial': _isInTrialPeriod.toString(),
        },
      );

      if (kDebugMode) {
        print('✅ Subscription activated:');
        print('   Product: $_activeProductId');
        print('   Expiration: $_expirationDate');
        print('   Trial: $_isInTrialPeriod');
      }

    } catch (e) {
      if (kDebugMode) {
        print('❌ Error handling successful purchase: $e');
      }
    }
  }

  /// Calculate expiration date based on subscription type
  DateTime _calculateExpirationDate(String productId) {
    final now = DateTime.now();

    if (productId.contains('weekly')) {
      return now.add(const Duration(days: 7));
    } else if (productId.contains('monthly')) {
      return DateTime(now.year, now.month + 1, now.day);
    } else if (productId.contains('yearly')) {
      return DateTime(now.year + 1, now.month, now.day);
    }

    // Default to 1 month
    return DateTime(now.year, now.month + 1, now.day);
  }

  /// Save subscription state to local storage
  Future<void> _saveSubscriptionState(PurchaseDetails purchaseDetails) async {
    try {
      // Secure storage
      await _secureStorage.write(
          key: 'subscription_product_id', value: purchaseDetails.productID);
      await _secureStorage.write(
          key: 'subscription_purchase_id', value: purchaseDetails.purchaseID);
      await _secureStorage.write(
          key: 'subscription_purchase_date', value: _purchaseDate?.toIso8601String());
      await _secureStorage.write(
          key: 'subscription_expiration', value: _expirationDate?.toIso8601String());
      await _secureStorage.write(
          key: 'subscription_is_trial', value: _isInTrialPeriod.toString());

      // Generate hash for verification
      final hash = _generateSubscriptionHash(purchaseDetails.productID);
      await _secureStorage.write(key: 'subscription_hash', value: hash);

      // Shared preferences for quick access
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('has_active_subscription', true);
      await prefs.setString('active_product_id', purchaseDetails.productID);

    } catch (e) {
      if (kDebugMode) {
        print('❌ Error saving subscription state: $e');
      }
    }
  }

  /// Save subscription to Firebase for cross-device sync
  Future<void> _saveToFirebase(PurchaseDetails purchaseDetails) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return;

      final data = {
        'product_id': purchaseDetails.productID,
        'purchase_id': purchaseDetails.purchaseID,
        'purchase_date': FieldValue.serverTimestamp(),
        'expiration_date': Timestamp.fromDate(_expirationDate!),
        'is_trial': _isInTrialPeriod,
        'platform': Platform.isIOS ? 'ios' : 'android',
        'verification_data': purchaseDetails.verificationData.serverVerificationData,
      };

      await FirebaseFirestore.instance
          .collection('subscriptions')
          .doc(user.uid)
          .set(data, SetOptions(merge: true));

      if (kDebugMode) {
        print('   Saved to Firebase');
      }

    } catch (e) {
      if (kDebugMode) {
        print('   ⚠️ Firebase save failed: $e');
      }
    }
  }

  /// Track purchase with RevenueCat for analytics only
  Future<void> _trackWithRevenueCat(PurchaseDetails purchaseDetails) async {
    try {
      // Send purchase receipt to RevenueCat for tracking
      if (Platform.isIOS) {
        await revenue_cat.Purchases.syncPurchases();
      } else if (Platform.isAndroid) {
        await revenue_cat.Purchases.syncPurchases();
      }

      if (kDebugMode) {
        print('   Tracked with RevenueCat');
      }

    } catch (e) {
      if (kDebugMode) {
        print('   ⚠️ RevenueCat tracking failed: $e');
      }
    }
  }

  /// Restore previous purchases
  Future<bool> restorePurchases() async {
    if (!_isAvailable) {
      if (kDebugMode) {
        print('❌ Cannot restore: IAP not available');
      }
      return false;
    }

    try {
      if (kDebugMode) {
        print('🔄 === RESTORING PURCHASES ===');
      }

      await _inAppPurchase.restorePurchases();

      // Check Firebase for server-side subscription state
      await _syncWithFirebase();

      if (kDebugMode) {
        print('   Restore complete. Active: $_hasActiveSubscription');
      }

      return _hasActiveSubscription || _isLifetimeUser;

    } catch (e) {
      if (kDebugMode) {
        print('❌ Restore error: $e');
      }
      return false;
    }
  }

  /// Load subscription status from local storage
  Future<void> _loadSubscriptionStatus() async {
    try {
      // Load from secure storage
      final productId = await _secureStorage.read(key: 'subscription_product_id');
      final expirationStr = await _secureStorage.read(key: 'subscription_expiration');
      final isTrialStr = await _secureStorage.read(key: 'subscription_is_trial');
      final hash = await _secureStorage.read(key: 'subscription_hash');

      if (productId != null && expirationStr != null) {
        // Verify hash
        final expectedHash = _generateSubscriptionHash(productId);
        if (hash == expectedHash) {
          _activeProductId = productId;
          _expirationDate = DateTime.parse(expirationStr);
          _isInTrialPeriod = isTrialStr == 'true';

          // Check if subscription is still active
          if (_expirationDate!.isAfter(DateTime.now())) {
            _hasActiveSubscription = true;

            if (kDebugMode) {
              print('   ✅ Active subscription found in local storage');
            }
          } else {
            if (kDebugMode) {
              print('   ⏰ Subscription expired');
            }
          }
        }
      }

    } catch (e) {
      if (kDebugMode) {
        print('   ⚠️ Error loading subscription status: $e');
      }
    }
  }

  /// Sync subscription status with Firebase
  Future<void> _syncWithFirebase() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) return;

      final doc = await FirebaseFirestore.instance
          .collection('subscriptions')
          .doc(user.uid)
          .get();

      if (doc.exists) {
        final data = doc.data()!;
        final expirationTimestamp = data['expiration_date'] as Timestamp?;

        if (expirationTimestamp != null) {
          final expiration = expirationTimestamp.toDate();

          if (expiration.isAfter(DateTime.now())) {
            _hasActiveSubscription = true;
            _activeProductId = data['product_id'];
            _expirationDate = expiration;
            _isInTrialPeriod = data['is_trial'] ?? false;

            if (kDebugMode) {
              print('   🔥 Active subscription synced from Firebase');
            }
          }
        }
      }

    } catch (e) {
      if (kDebugMode) {
        print('   ⚠️ Firebase sync failed: $e');
      }
    }
  }

  /// Check for grandfathered lifetime users (old one-time purchase)
  Future<void> _checkLifetimeUser() async {
    try {
      final purchaseManager = PurchaseManager();
      if (!purchaseManager.isInitialized) {
        await purchaseManager.initialize();
      }

      if (purchaseManager.isPurchased) {
        _isLifetimeUser = true;

        if (kDebugMode) {
          print('   🎁 Grandfathered lifetime user detected');
        }

        await AnalyticsManager.instance.trackEvent(
          eventName: 'user_grandfathered',
          parameters: {'source': 'lifetime_purchase'},
        );
      }

    } catch (e) {
      if (kDebugMode) {
        print('   ⚠️ Error checking lifetime status: $e');
      }
    }
  }

  /// Check if content is unlocked
  bool isContentUnlocked(int contentIndex) {
    // Free content (first 5 games)
    if (contentIndex < 5) return true;

    // Premium content requires subscription or lifetime access
    return _hasActiveSubscription || _isLifetimeUser;
  }

  /// Get human-readable subscription status
  String getSubscriptionStatusText() {
    if (_isLifetimeUser) {
      return 'Lifetime Premium';
    }

    if (!_hasActiveSubscription) {
      return 'No Active Subscription';
    }

    if (_isInTrialPeriod) {
      final daysLeft = _expirationDate != null
          ? _expirationDate!.difference(DateTime.now()).inDays
          : 7;
      return 'Free Trial ($daysLeft days left)';
    }

    String planName = 'Premium';
    if (_activeProductId != null) {
      if (_activeProductId!.contains('weekly')) planName = 'Weekly';
      if (_activeProductId!.contains('monthly')) planName = 'Monthly';
      if (_activeProductId!.contains('yearly')) planName = 'Yearly';
    }

    return '$planName Subscription';
  }

  /// Setup connectivity listener for syncing
  void _setupConnectivityListener() {
    _connectivitySubscription = Connectivity().onConnectivityChanged.listen(
      (List<ConnectivityResult> results) {
        if (!results.contains(ConnectivityResult.none)) {
          _syncWithFirebase();
        }
      },
    );
  }

  /// Generate hash for subscription verification
  String _generateSubscriptionHash(String productId) {
    final data = '$productId:${AppConstants.APP_SALT}';
    final bytes = utf8.encode(data);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// Dispose resources
  void dispose() {
    try {
      _purchaseSubscription?.cancel();
      _connectivitySubscription?.cancel();

      if (!_subscriptionStateController.isClosed) {
        _subscriptionStateController.close();
      }

      if (kDebugMode) {
        print('SubscriptionManager disposed');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error disposing SubscriptionManager: $e');
      }
    }
  }
}

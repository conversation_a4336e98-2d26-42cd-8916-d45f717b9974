# Brainy Bunny - Testing Guide

## Overview

This guide covers testing procedures for all major features of Brainy Bunny, with focus on onboarding, subscriptions, and critical user flows.

**Last Updated:** January 2025
**Version:** 1.0

---

## Table of Contents

1. [Pre-Testing Setup](#pre-testing-setup)
2. [Onboarding Flow Testing](#onboarding-flow-testing)
3. [Subscription Testing](#subscription-testing)
4. [Orientation Testing](#orientation-testing)
5. [Audio Testing](#audio-testing)
6. [Memory & Performance Testing](#memory--performance-testing)
7. [Analytics Testing](#analytics-testing)
8. [Edge Cases & Error Scenarios](#edge-cases--error-scenarios)

---

## Pre-Testing Setup

### Required Tools

- **Physical iOS Device** (iPhone/iPad) - for real purchase testing
- **Xcode** with simulator - for development testing
- **RevenueCat Sandbox Account** - for subscription testing
- **App Store Connect Sandbox Tester Account** - for IAP testing
- **Adjust Dashboard** - for analytics verification
- **Firebase Console** - for analytics and Crashlytics

### Test Device Preparation

1. **Configure Sandbox Account:**
   ```
   Settings → App Store → Sandbox Account → Sign in with test account
   ```

2. **Enable Debug Logging:**
   - Build in debug mode
   - Check Xcode console for logs

3. **Clear App Data Between Tests:**
   ```
   Settings → General → iPhone Storage → Brainy Bunny → Delete App
   OR
   Long press app → Remove App → Delete App
   ```

4. **Reset Advertising Identifier:**
   ```
   Settings → Privacy → Apple Advertising → Reset Advertising Identifier
   ```

---

## Onboarding Flow Testing

### Test Case 1: First Launch - Complete Flow

**Objective:** Verify complete onboarding flow from start to finish

**Steps:**
1. Fresh install app (delete if already installed)
2. Launch app
3. Wait for splash screen animation
4. Verify onboarding starts automatically

**Expected Screens (in order):**
1. ✅ Welcome Screen (portrait)
2. ✅ Name Input Screen (portrait)
3. ✅ Child Age Selection (portrait)
4. ✅ Educational Philosophy (portrait)
5. ✅ Orientation Transition "Rotate to landscape" (portrait)
6. ✅ Demo Game 1 - Shapes (landscape)
7. ✅ Demo Game 2 - Memory (landscape)
8. ✅ Demo Game 3 - Logic (landscape)
9. ✅ Demo Game 4 - Patterns (landscape)
10. ✅ Demo Game 5 - World (landscape)
11. ✅ Orientation Transition "Rotate to portrait" (landscape)
12. ✅ Personalized Summary (portrait)
13. ✅ Trust & Social Proof (portrait)
14. ✅ Paywall Step 1 - Value Props (portrait)
15. ✅ Paywall Step 2 - Plan Selection (portrait)
16. ✅ Paywall Step 3 - Final Confirmation (portrait)
17. ✅ Navigate to Home Screen (landscape)

**Verification Points:**
- [ ] All screens display correctly
- [ ] Orientation changes smoothly
- [ ] Animations work properly
- [ ] Progress persists if app is backgrounded
- [ ] Can navigate back on paywall screens 2-3
- [ ] Can skip paywall on screen 1
- [ ] Badges collected during demo games (5 total)

**Time Estimate:** 8-12 minutes

---

### Test Case 2: Demo Games - Match Completion

**Objective:** Verify demo games complete after 3 matches

**For each demo game (1-5):**
- [ ] Game loads with 3 pairs to match
- [ ] Educational context visible at top
- [ ] Can collapse/expand context
- [ ] Drag items match correctly
- [ ] Progress shows X/3 matches
- [ ] After 3rd match: confetti animation plays
- [ ] Badge awarded and displayed
- [ ] Auto-advances to next screen after ~2 seconds

**Critical Check:**
- Games must complete after exactly 3 matches
- No game should hang or fail to advance

---

### Test Case 3: Onboarding State Persistence

**Objective:** Verify progress is saved and can be resumed

**Steps:**
1. Start onboarding (fresh install)
2. Complete first 3 screens (Welcome, Name, Age)
3. Force close app (swipe up from app switcher)
4. Reopen app

**Expected:**
- [ ] Onboarding resumes at screen 4 (Educational Philosophy)
- [ ] Previous inputs saved (name, age)
- [ ] Progress indicator shows correct completion

**Steps continued:**
5. Complete to Demo Game 2
6. Force close app again
7. Reopen app

**Expected:**
- [ ] Resumes at Demo Game 2
- [ ] Orientation is landscape
- [ ] Badge from Demo Game 1 is saved

---

### Test Case 4: Onboarding Skip Functionality

**Objective:** Verify skip option works on Paywall Step 1

**Steps:**
1. Complete onboarding through Paywall Step 1
2. Tap "Skip" or "Continue without premium" button
3. Verify app navigates to Home Screen
4. Close and reopen app

**Expected:**
- [ ] Home screen displays
- [ ] Premium games show lock icon
- [ ] Onboarding marked as complete (doesn't restart)
- [ ] Can access Settings to purchase later

---

## Subscription Testing

### Test Case 5: Subscription Price Loading

**Objective:** Verify RevenueCat fetches correct prices

**Note:** Onboarding paywall uses fallback prices (€4.99, €8.99, €44.99) which is intentional for fast loading. This test is for post-onboarding purchase flows.

**Steps:**
1. Complete onboarding
2. Navigate to Settings → Subscription Management
3. Wait for prices to load

**Expected:**
- [ ] Prices load within 5 seconds
- [ ] Correct prices displayed for all 3 tiers:
  - Weekly: €4.99/week
  - Monthly: €8.99/month
  - Yearly: €44.99/year
- [ ] "7-Day Free Trial" badge shown
- [ ] Savings percentage shown for yearly (58%)

**If prices fail to load:**
- [ ] Fallback prices displayed
- [ ] Error message shown: "Could not load prices"
- [ ] Retry button available

**Debug Checks:**
```
Check logs for:
✅ RevenueCat configured with API key
✅ Current offering loaded: Packages: 3
❌ Error fetching offerings: [error message]
```

---

### Test Case 6: Sandbox Subscription Purchase

**Objective:** Verify subscription purchase flow in sandbox

**Prerequisites:**
- Signed in with App Store Sandbox account
- RevenueCat configured with sandbox mode

**Steps:**
1. Complete onboarding to Paywall Step 3
2. Select "Yearly Plan" (€44.99/year)
3. Tap "Start Free Trial" button
4. Complete App Store purchase flow
   - Touch ID/Face ID prompt
   - Confirm sandbox purchase

**Expected:**
- [ ] Apple payment sheet appears
- [ ] Shows "7 Day Free Trial" clearly
- [ ] After trial: "€44.99 per year"
- [ ] Purchase processes successfully
- [ ] Success confirmation shown
- [ ] All games unlock immediately
- [ ] Navigate to Home Screen

**Verification:**
- [ ] Check RevenueCat dashboard: user has active subscription
- [ ] Check Firestore: `users/{userId}/hasActiveSubscription = true`
- [ ] Check Analytics: `subscription_purchased` event tracked

---

### Test Case 7: Restore Purchases

**Objective:** Verify restore purchases works correctly

**Prerequisites:**
- Previously purchased subscription in sandbox
- App deleted and reinstalled

**Steps:**
1. Fresh install app
2. Complete onboarding, skip purchase
3. Navigate to Settings
4. Tap "Restore Purchases" button

**Expected:**
- [ ] Loading indicator shown
- [ ] Success message: "Purchases restored successfully"
- [ ] All premium games unlock
- [ ] Subscription status shows active

**If no purchases found:**
- [ ] Error message: "No purchases found"
- [ ] Games remain locked

---

### Test Case 8: Grandfathered Lifetime Users

**Objective:** Verify existing lifetime users maintain access

**Prerequisites:**
- User with previous one-time lifetime purchase

**Steps:**
1. Launch app with existing user data
2. Complete/skip onboarding
3. Check game access

**Expected:**
- [ ] All games unlocked without subscription
- [ ] Subscription status shows "Lifetime Premium"
- [ ] No subscription prompts shown
- [ ] Analytics event: `user_grandfathered` tracked

---

## Orientation Testing

### Test Case 9: Orientation Transitions

**Objective:** Verify smooth orientation changes during onboarding

**Critical Transitions:**
1. **Screen 4 → 5:** Portrait → Landscape (before Demo Game 1)
2. **Screen 10 → 11:** Landscape → Portrait (after Demo Game 5)
3. **Screen 16 → Home:** Portrait → Landscape (end of onboarding)

**For each transition:**
- [ ] Transition screen shows rotating phone icon
- [ ] Clear message: "Rotate to landscape/portrait"
- [ ] Auto-advances after ~2 seconds
- [ ] Next screen displays correctly in new orientation
- [ ] No layout glitches or stretching
- [ ] No content cut off

**Common Issues to Watch For:**
- ❌ Screen stuck in wrong orientation
- ❌ Layout compressed or stretched
- ❌ Keyboard covers inputs (if portrait)
- ❌ Back button not accessible

---

### Test Case 10: Home Screen Orientation Lock

**Objective:** Verify home screen always stays landscape

**Steps:**
1. Complete onboarding, reach Home Screen
2. Physically rotate device to portrait
3. Navigate to a game
4. Exit game back to Home
5. Try to rotate device

**Expected:**
- [ ] Home screen always landscape only
- [ ] Device rotation has no effect
- [ ] Games also landscape
- [ ] Settings modal landscape

---

## Audio Testing

### Test Case 11: Menu Music Playback

**Objective:** Verify menu music plays correctly

**Steps:**
1. Launch app (fresh install)
2. Wait through splash screen
3. Start onboarding (portrait screens)

**Expected:**
- [ ] No music during splash (silent)
- [ ] No music during portrait onboarding screens
- [ ] Music starts when entering landscape sections

**Home Screen:**
4. Complete onboarding, reach Home Screen

**Expected:**
- [ ] Menu music (Menu.mp3) starts playing
- [ ] Music loops continuously
- [ ] Volume at 50% (default)

---

### Test Case 12: Game Music Transition

**Objective:** Verify music switches between menu and game

**Steps:**
1. Home screen (menu music playing)
2. Tap on any game to start
3. Listen for music change
4. Exit game back to home

**Expected:**
- [ ] Menu music stops when game starts
- [ ] Random game music starts (Background_1/2/3.mp3)
- [ ] Game music plays during gameplay
- [ ] Game music stops when exiting
- [ ] Menu music resumes on home screen

---

### Test Case 13: Background/Foreground Audio

**Objective:** Verify audio pauses/resumes with app lifecycle

**Steps:**
1. Home screen with menu music playing
2. Press home button (background app)
3. Wait 5 seconds
4. Return to app

**Expected:**
- [ ] Music pauses when app backgrounds
- [ ] Music resumes when app foregrounds
- [ ] No audio glitches or overlapping

**During Game:**
5. Start a game (game music playing)
6. Background app
7. Foreground app

**Expected:**
- [ ] Game music pauses
- [ ] Game music resumes
- [ ] Gameplay state preserved

---

### Test Case 14: Audio Memory Leaks

**Objective:** Verify audio resources are properly disposed

**Steps:**
1. Launch app, complete onboarding
2. Play 5 different games (one after another)
3. Each time: start game → exit → return home
4. Monitor Xcode memory graph

**Expected:**
- [ ] Memory usage stays stable (~150-200 MB)
- [ ] No increase with each game launch
- [ ] Audio files properly unloaded

**Debug Checks:**
```
Check logs for:
🧹 Disposing audio service resources
✅ Audio service disposed successfully
🧹 Demo game resources cleared
```

---

## Memory & Performance Testing

### Test Case 15: Memory Usage During Onboarding

**Objective:** Track memory consumption through onboarding flow

**Tools:**
- Xcode Memory Debugger
- Instruments (Allocations)

**Baseline Measurements:**
- App launch: ~80-100 MB
- After splash: ~100-120 MB
- Portrait onboarding screens: ~120-150 MB
- Demo games (landscape): ~180-220 MB
- Home screen: ~150-200 MB

**Steps:**
1. Launch Instruments with Allocations template
2. Start app recording
3. Complete full onboarding flow
4. Continue to home screen and play 2 games
5. Stop recording

**Analysis:**
- [ ] No persistent growth over time (indicates leaks)
- [ ] Memory drops after exiting demo games
- [ ] Flame components properly disposed
- [ ] Sprites released from memory

**Red Flags:**
- ❌ Memory increasing steadily without dropping
- ❌ Orphaned game components after exiting
- ❌ Multiple instances of same sprite in memory

---

### Test Case 16: Performance on Older Devices

**Objective:** Verify smooth performance on minimum spec device

**Test Device:** iPhone 8 (or equivalent)

**Metrics to Track:**
- Frame rate: Should maintain 60 FPS
- Animation smoothness
- Touch responsiveness
- Load times

**Critical Moments:**
1. Demo game loading: < 2 seconds
2. Orientation transitions: < 500ms
3. Screen transitions: < 300ms
4. Confetti animation: No frame drops

**Expected:**
- [ ] All animations smooth (no stuttering)
- [ ] Touch input responsive (< 100ms delay)
- [ ] No visible lag during gameplay

---

## Analytics Testing

### Test Case 17: Onboarding Analytics Events

**Objective:** Verify all analytics events fire correctly

**Tools:**
- Adjust Dashboard (Events page)
- Firebase Analytics DebugView
- RevenueCat Dashboard (Customer Timeline)

**Enable Debug Mode:**
```bash
# iOS
xcrun simctl spawn booted log config --mode "level:debug" --subsystem com.your.bundle.id

# Or use Firebase Debug View with:
-FIRDebugEnabled
```

**Events to Verify:**

| Event | Trigger | Parameters |
|-------|---------|------------|
| `app_install` | First launch | `platform`, `app_version` |
| `onboarding_started` | Screen 0 shown | `timestamp` |
| `onboarding_screen_view` | Each screen advance | `screen_index`, `screen_name` |
| `onboarding_demo_game_completed` | Each demo game done | `game_index`, `game_name`, `badge_earned` |
| `onboarding_paywall_viewed` | Paywall Step 1 shown | `step: 1` |
| `onboarding_paywall_plan_selected` | Plan selected | `plan_id`, `price` |
| `onboarding_skipped` | Skip button tapped | `screen_index` |
| `onboarding_completed` | All 16 screens done | `duration`, `skipped: true/false` |
| `subscription_purchase_initiated` | Purchase button tapped | `product_id`, `price` |
| `subscription_purchased` | Purchase successful | `product_id`, `price`, `is_trial` |
| `trial_started` | Trial begins | `product_id`, `expiration_date` |

**Verification Steps:**
1. Complete full onboarding with purchase
2. Check Adjust dashboard: all events appear within 1 minute
3. Check Firebase: events with correct parameters
4. Check RevenueCat: customer profile updated

---

### Test Case 18: Attribution Tracking

**Objective:** Verify Adjust attribution works

**Prerequisites:**
- Adjust app configured in dashboard
- SKAdNetwork IDs added to Info.plist

**Steps:**
1. Fresh install (sandbox mode)
2. Complete onboarding
3. Make sandbox purchase

**Check Adjust Dashboard:**
- [ ] Install attributed to test campaign
- [ ] Events (onboarding, purchase) tied to user
- [ ] Revenue tracked correctly (€44.99)
- [ ] ADID visible in customer details

---

## Edge Cases & Error Scenarios

### Test Case 19: Network Errors

**Objective:** Verify app handles network failures gracefully

**Scenario 1: No Internet During Onboarding**
1. Enable Airplane Mode
2. Launch app
3. Try to complete onboarding

**Expected:**
- [ ] Onboarding proceeds normally (offline-first)
- [ ] Analytics queued for later
- [ ] Purchase buttons disabled with message
- [ ] Can skip and complete onboarding

**Scenario 2: Network Lost During Purchase**
1. Start purchase flow (Paywall Step 3)
2. Tap "Start Free Trial"
3. Enable Airplane Mode mid-purchase

**Expected:**
- [ ] Error message: "Network error. Please try again."
- [ ] No partial/corrupted state
- [ ] Can retry when network returns
- [ ] Analytics event: `purchase_network_error`

---

### Test Case 20: App Termination During Onboarding

**Objective:** Verify state recovery after force quit

**Scenario 1: Terminate During Demo Game**
1. Start Demo Game 3
2. Complete 2/3 matches
3. Force quit app (swipe up from app switcher)
4. Relaunch app

**Expected:**
- [ ] Onboarding resumes at Demo Game 3
- [ ] Game reloads from beginning (not mid-game)
- [ ] Progress: 2 badges already earned
- [ ] No crash or data loss

**Scenario 2: Terminate During Purchase**
1. Initiate purchase
2. Apple payment sheet shown
3. Force quit app (don't complete purchase)
4. Relaunch app

**Expected:**
- [ ] Onboarding resumes at Paywall Step 3
- [ ] No purchase completed
- [ ] Can retry purchase
- [ ] App Store handles incomplete transaction

---

### Test Case 21: Storage Limits

**Objective:** Verify app handles low storage gracefully

**Simulation:**
- Fill device storage to < 500 MB free

**Expected:**
- [ ] App launches successfully
- [ ] Onboarding loads (minimal storage needed)
- [ ] Games load (but may be slower)
- [ ] Clear error if storage critically low
- [ ] No data corruption

---

### Test Case 22: Rapid Screen Transitions

**Objective:** Verify no race conditions in navigation

**Steps:**
1. Start onboarding
2. Rapidly tap "Continue" on Welcome screen (10+ times)
3. Observe behavior

**Expected:**
- [ ] Screen advances only once
- [ ] No duplicate screens
- [ ] No crash
- [ ] Progress tracking correct

**For Demo Games:**
4. Load Demo Game 1
5. While loading, background app and immediately foreground
6. Repeat 5 times

**Expected:**
- [ ] Game loads correctly
- [ ] No duplicate game instances
- [ ] Memory stable

---

## Automated Testing

### Unit Tests (when implemented)

**Models:**
```bash
flutter test test/models/onboarding_state_test.dart
flutter test test/models/onboarding_progress_test.dart
```

**Services:**
```bash
flutter test test/services/subscription_manager_test.dart
flutter test test/services/audio_service_test.dart
flutter test test/services/analytics_manager_test.dart
```

**Run All Tests:**
```bash
flutter test
```

---

## Test Summary Checklist

### Pre-Release Checklist

**Onboarding:**
- [ ] All 16 screens display correctly
- [ ] Orientation transitions smooth
- [ ] Demo games complete properly
- [ ] State persistence works
- [ ] Skip functionality works
- [ ] Analytics events tracked

**Subscriptions:**
- [ ] Prices load correctly
- [ ] Sandbox purchases work
- [ ] Restore purchases works
- [ ] Grandfathered users maintain access
- [ ] Server verification working

**Audio:**
- [ ] Menu music plays on home
- [ ] Game music plays during games
- [ ] Transitions smooth
- [ ] No memory leaks

**Performance:**
- [ ] Memory usage stable
- [ ] 60 FPS maintained
- [ ] Fast load times
- [ ] Works on iPhone 8+

**Edge Cases:**
- [ ] Handles network errors
- [ ] Survives force quit
- [ ] Low storage handled
- [ ] No race conditions

---

## Troubleshooting

### Common Issues

**Issue:** Onboarding doesn't start on first launch
- **Check:** OnboardingProgress stored as completed
- **Fix:** Delete app data, reinstall

**Issue:** Orientation stuck in wrong mode
- **Check:** SystemChrome.setPreferredOrientations called
- **Fix:** Force quit app, relaunch

**Issue:** Prices not loading
- **Check:** RevenueCat API key configured
- **Check:** Network connection
- **Check:** Subscription products created in App Store Connect
- **Fix:** Verify configuration, use fallback prices

**Issue:** Audio not playing
- **Check:** Device not on silent mode
- **Check:** AudioService initialized
- **Fix:** Check logs for audio initialization errors

**Issue:** Purchase not completing
- **Check:** Signed in with sandbox account
- **Check:** RevenueCat webhook configured
- **Fix:** Check RevenueCat dashboard for errors

---

## Contact & Support

- **Developer:** [Your Name]
- **Bug Reports:** GitHub Issues
- **RevenueCat Support:** <EMAIL>
- **Adjust Support:** <EMAIL>

---

**End of Testing Guide**

# Brainy Bunny: Complete Implementation Plan for Autonomous Coding Agent

## Document Purpose
This document provides step-by-step implementation instructions for transforming Brainy Bunny from a simple freemium game into a conversion-optimized educational app with comprehensive analytics, subscription monetization, and onboarding flow.

---

## PROJECT OVERVIEW

### Current State
- Flutter/Flame game with 15 drag-and-drop matching games
- One-time purchase (€6.99) for games 6-15
- Direct game selection from home screen
- Manual in-app-purchase implementation
- Firebase authentication and storage

### Target State
- Research-backed onboarding flow with 5 demo games
- Subscription model (weekly/monthly/yearly with 7-day trial)
- Full analytics integration (Adjust, RevenueCat, Meta)
- Multi-language support (18 languages)
- Server-side purchase verification
- Optimized conversion funnel

### Game Analysis from Screenshots

**Game 1 - Animal Shadow Matching** (Image 1)
- Top row: 5 colored animals (deer, chicken, horse, rabbit, dog)
- Bottom row: 5 black silhouettes to match
- Skill: Visual matching, shape recognition, animal identification

**Game 2 - Animal Matching** (Image 2)
- Grid layout: 10 cute animals in pastoral setting
- Duplicates to find pairs
- Skill: Memory, visual discrimination, attention to detail

**Game 3 - Object Categorization** (Image 3)
- Top row: 5 objects (carrot, cabbage, flower, banana, bone)
- Bottom row: 5 animals (lamb, rabbit, bee, monkey, dog)
- Skill: Associative thinking, categorization, logical reasoning

**Game 4 - Backpack Matching** (Image 4)
- Top row: 5 backpacks with different symbols
- Bottom row: 5 diverse children
- Skill: Pattern matching, detail observation, one-to-one correspondence

**Game 5 - Career/Object Matching** (Image 5)
- Top row: 5 career-related objects (extinguisher, ladle, shoes, helmet, stethoscope)
- Bottom row: 5 children in career costumes
- Skill: Logical association, early career awareness, symbolic thinking

---

## PHASE 1: FOUNDATION SETUP (Week 1)

### Task 1.1: Project Preparation

**Instructions:**
1. Create git branch named `feature/onboarding-subscriptions-analytics`
2. Update `pubspec.yaml` dependencies:
   - Add `adjust_sdk: ^4.38.0`
   - Add `purchases_flutter: ^6.29.0`
   - Add `flutter_localizations` (from Flutter SDK)
   - Add `intl: ^0.19.0` for localization
   - Keep all existing dependencies
3. Run dependency resolution and verify no conflicts
4. Create new directory structure:
   - `lib/onboarding/screens/` - Onboarding UI screens
   - `lib/onboarding/widgets/` - Reusable onboarding components
   - `lib/services/analytics/` - Analytics services
   - `lib/services/subscription/` - Subscription management
   - `lib/l10n/` - Localization files
5. Commit with message: "chore: project structure for onboarding and analytics"

### Task 1.2: Localization Infrastructure

**Instructions:**
1. Enable Flutter localization in `MaterialApp`:
   - Add `localizationsDelegates` parameter
   - Add `supportedLocales` for all 18 languages:
     - `en` (English)
     - `ar` (Arabic)
     - `es` (Spanish Spain)
     - `es-MX` (Spanish Mexico)
     - `pt-BR` (Portuguese Brazil)
     - `pt` (Portuguese Portugal)
     - `ko` (Korean)
     - `zh-Hans` (Chinese Simplified)
     - `zh-Hant` (Chinese Traditional)
     - `hi` (Hindi)
     - `id` (Indonesian)
     - `ru` (Russian)
     - `fr` (French France)
     - `fr-CA` (French Canada)
     - `de` (German)
     - `ja` (Japanese)
     - `it` (Italian)
     - `ca` (Catalan)
2. Create ARB file: `lib/l10n/app_en.arb` as base template
3. Create empty ARB files for all other 17 languages
4. Add script `scripts/check_translations.dart` to verify all keys exist in all language files
5. Document translation process in `docs/LOCALIZATION.md`

### Task 1.3: Constants Reorganization

**Instructions:**
1. Create `lib/constants/onboarding_constants.dart`:
   - Define screen count constant
   - Define demo game indices (0-4 from existing games)
   - Define question options data structures
2. Update `lib/constants/app_constants.dart`:
   - Remove old single product ID constants
   - Add subscription product ID constants:
     - `SUBSCRIPTION_WEEKLY`
     - `SUBSCRIPTION_MONTHLY`
     - `SUBSCRIPTION_YEARLY`
   - Add analytics event name constants
   - Keep game-related constants
3. Create `lib/constants/subscription_constants.dart`:
   - Trial duration constant (7 days)
   - Pricing constants (for display, not enforcement)
   - Grace period constants
4. Commit with message: "refactor: reorganize constants for new features"

---

## PHASE 2: ANALYTICS INTEGRATION (Week 1-2)

### Task 2.1: Adjust SDK Setup

**Instructions:**
1. Create `lib/services/analytics/adjust_service.dart`:
   - Implement singleton pattern matching existing services
   - Create initialization method accepting app token
   - Implement event tracking method with parameters
   - Implement ADID retrieval method
   - Add error handling with fallback to local logging
2. iOS-specific setup in `ios/Runner/Info.plist`:
   - Add key `NSAdvertisingAttributionReportEndpoint`
   - Set value to `https://skan-rocapine.com`
   - Add SKAdNetwork IDs from Adjust documentation
3. Android-specific setup:
   - No changes needed in manifest (SDK handles)
4. Initialize in `app_initialization_service.dart`:
   - Call Adjust initialization with token `2k0f6660xxxc`
   - Set environment to production
   - Enable event buffering
5. Create event definitions in Adjust dashboard:
   - `app_installed`
   - `onboarding_started`
   - `onboarding_completed`
   - `game_played`
   - `paywall_viewed`
   - `trial_started`
   - `subscription_purchased`
6. Document event tokens in `docs/ANALYTICS_EVENTS.md`
7. Test installation tracking per Adjust documentation
8. Commit with message: "feat: integrate Adjust SDK for attribution"

### Task 2.2: RevenueCat Integration (Tracking Only)

**Instructions:**
1. Create `lib/services/analytics/revenuecat_service.dart`:
   - Initialize RevenueCat with API key (obtain from Rocapine)
   - Do NOT use RevenueCat's paywall builders
   - Only use for purchase event tracking
2. Implement Adjust ID mapping:
   - After Adjust initialization, retrieve Adjust ID
   - Set as RevenueCat subscriber attribute with key `$adjustId`
   - Retry mechanism if Adjust ID not immediately available
3. Configure RevenueCat webhook in dashboard:
   - Set webhook URL to Adjust's webhook endpoint
   - Map events from provided CSV:
     - Initial purchase
     - Renewal
     - Cancellation
     - Billing issue
4. Add RevenueCat event tokens to Adjust dashboard
5. Create test flow documentation:
   - How to verify events reach RevenueCat
   - How to verify RevenueCat forwards to Adjust (Response 200 check)
6. Commit with message: "feat: integrate RevenueCat for purchase tracking"

### Task 2.3: Meta SDK Native Integration

**Instructions:**

**iOS Bridge Setup:**
1. Add to `ios/Podfile`:
   - `pod 'FBSDKCoreKit'`
2. Run `pod install` in ios directory
3. Configure `ios/Runner/Info.plist`:
   - Add Facebook App ID: `2012260112880464`
   - Add URL scheme `fb2012260112880464`
   - Add Facebook Client Token (obtain from Meta dashboard)
   - Add Facebook Display Name: `Brainy Bunny`
4. Update `ios/Runner/AppDelegate.swift`:
   - Import FBSDKCoreKit
   - Initialize Facebook SDK in `didFinishLaunchingWithOptions`
5. Create platform channel `com.goodkarmalab.brainy_bunny/meta`:
   - Handle method `logEvent` with eventName and parameters
   - Use `AppEvents.shared.logEvent()` to send to Meta

**Android Bridge Setup:**
1. Add to `android/app/build.gradle`:
   - `implementation 'com.facebook.android:facebook-android-sdk:16.0.0'`
2. Configure `android/app/src/main/AndroidManifest.xml`:
   - Add meta-data for Facebook App ID
   - Add meta-data for Facebook Client Token
3. Add to `android/app/src/main/res/values/strings.xml`:
   - Facebook app ID string resource
   - Facebook client token string resource
4. Update `MainActivity.kt`:
   - Initialize Facebook SDK
   - Create method channel handler for `logEvent`
   - Use `AppEventsLogger.logEvent()` to send to Meta

**Flutter Service Layer:**
1. Create `lib/services/analytics/meta_service.dart`:
   - Create method channel instance
   - Implement `logCompleteRegistration()` method
   - Implement `logStartTrial()` method with parameters
   - Implement `logSubscribe()` method with value and currency
   - Add error handling and logging
2. Test events appear in Meta Event Manager
3. Commit with message: "feat: integrate Meta SDK for campaign tracking"

### Task 2.4: Unified Analytics Service

**Instructions:**
1. Create `lib/services/analytics/analytics_manager.dart`:
   - Aggregate all three analytics services
   - Provide single method for each business event
   - Call appropriate service methods for each event
   - Example: `trackOnboardingComplete()` calls Adjust and Meta
2. Implement event methods:
   - `trackAppInstall()`
   - `trackOnboardingStarted()`
   - `trackOnboardingCompleted()`
   - `trackGamePlayed(gameId, gameName)`
   - `trackPaywallViewed()`
   - `trackTrialStarted(subscriptionId)`
   - `trackSubscriptionPurchased(subscriptionId, price, currency)`
3. Add debug mode that logs all events to console
4. Create analytics testing screen (debug builds only):
   - Buttons to trigger each event
   - Display last 10 events fired
   - Verify in Adjust/Meta dashboards
5. Commit with message: "feat: create unified analytics manager"

---

## PHASE 3: ONBOARDING FLOW IMPLEMENTATION (Week 2-4)

### Task 3.1: Onboarding Data Models

**Instructions:**
1. Create `lib/models/onboarding_state.dart`:
   - Properties: parentName, childAge, learningGoal, screenTimeGoal
   - Serialization methods for local persistence
   - Validation methods for required fields
2. Create `lib/models/onboarding_progress.dart`:
   - Track which screens completed
   - Track which demo games played
   - Boolean flags for each milestone
3. Update initialization to check onboarding status:
   - If first launch AND not subscribed: show onboarding
   - If returning user: skip to home screen
   - Store completion flag in SharedPreferences
4. Commit with message: "feat: add onboarding data models"

### Task 3.2: Onboarding Navigation Framework

**Instructions:**
1. Create `lib/onboarding/onboarding_coordinator.dart`:
   - Manage screen progression
   - Handle orientation changes (portrait ↔ landscape)
   - Persist progress between sessions
   - Provide skip functionality (disabled by default)
2. Create screen enumeration:
   - Welcome (portrait)
   - NameInput (portrait)
   - ChildAge (portrait)
   - EducationalPhilosophy (portrait)
   - OrientationTransitionToGames (portrait)
   - DemoGame1 through DemoGame5 (landscape)
   - OrientationTransitionFromGames (landscape)
   - PersonalizedSummary (portrait)
   - TrustProof (portrait)
   - PaywallStep1, PaywallStep2, PaywallStep3 (portrait)
3. Implement orientation management:
   - Force portrait for non-game screens
   - Show transition animation before orientation change
   - Force landscape for demo games
   - Lock orientation during screen (prevent accidental rotation)
4. Commit with message: "feat: implement onboarding navigation framework"

### Task 3.3: Welcome and Personalization Screens (Portrait)

**Instructions:**

**Screen 1 - Welcome Screen:**
1. Create `lib/onboarding/screens/welcome_screen.dart`
2. Layout (9:16 portrait):
   - Top 20%: Bunny mascot illustration (use existing assets or create)
   - Middle 50%: Content area
     - Headline: Localized string `onboarding_welcome_headline`
     - Subheading: Localized string `onboarding_welcome_subheading`
     - Badge: "Approved by Teachers" with Google Play badge icon
     - Scientific backing text: Localized string `onboarding_welcome_science`
   - Bottom 30%: CTA button + skip option (hidden)
3. Animations:
   - Bunny gentle bounce animation on load
   - Fade in content with stagger
   - Button scale pulse to draw attention
4. Analytics: Track `onboarding_started` when screen loads
5. Rounded fonts throughout (Quicksand or similar child-friendly font)

**Screen 2 - Name Input:**
1. Create `lib/onboarding/screens/name_input_screen.dart`
2. Layout:
   - Icon: Heart with sparkles
   - Headline: Localized string `onboarding_name_headline`
   - Text field with hint: "Your name"
   - Suggestion buttons: "Mom", "Dad", "Parent" (localized)
   - Progress indicator: 1/4
3. Validation:
   - Minimum 2 characters
   - Maximum 20 characters
   - No special characters except spaces and hyphens
4. Store in onboarding state
5. Use name in all subsequent screens: "Great, [Name]!"

**Screen 3 - Child Age:**
1. Create `lib/onboarding/screens/child_age_screen.dart`
2. Layout:
   - Icon: Child with growth chart
   - Headline: "How old is your child?"
   - 4 large selectable cards with icons:
     - "2 years old" (toddler icon)
     - "3 years old" (young child icon)
     - "4 years old" (preschooler icon)
     - "5+ years old" (kindergarten icon)
   - Progress indicator: 2/4
3. Each card animates on selection
4. Proceed automatically after selection (0.5s delay)
5. Store age in onboarding state

**Screen 4 - Educational Philosophy:**
1. Create `lib/onboarding/screens/educational_philosophy_screen.dart`
2. Layout:
   - Icon: Brain with heart
   - Headline: Localized string `onboarding_philosophy_headline`
   - 3 bullet points with icons:
     - AAP screen time recommendation (with citation)
     - Transform screen time to learning time
     - Develop cognitive skills through play
   - Statistical badge: "95% of parents report improvement" (if verifiable)
   - Progress indicator: 3/4
3. Animations: Staggered fade-in for each bullet
4. Auto-advance after 5 seconds OR manual button tap

### Task 3.4: Demo Game Integration (Landscape)

**Instructions:**

**Transition Screen:**
1. Create `lib/onboarding/screens/orientation_transition_screen.dart`
2. Show before first demo game:
   - Animation: Phone rotation icon
   - Text: "Let's play! Rotate your device →"
   - Force landscape orientation after 2 seconds
3. Use same screen when returning to portrait after games

**Demo Game Implementation Strategy:**
1. Create `lib/onboarding/screens/demo_game_wrapper_screen.dart`:
   - Wraps existing game components
   - Adds educational context header (collapsible)
   - Simplifies game to 2-3 matches only
   - Shows progress indicator (1/5, 2/5, etc.)
   - Celebration animation on completion
   - "Next Game" button after completion
2. For each demo game, create configuration:
   - Which existing game to use
   - Number of matches
   - Educational context text
   - Skill badge to award

**Demo Game 1 - Animal Shadow Matching:**
1. File: `lib/onboarding/screens/demo_game_1_screen.dart`
2. Educational context (collapsible header):
   - Skill: "Visual Matching & Shape Recognition"
   - For Parents: Localized string `demo_game_1_science`
     - "Matching animals to silhouettes develops visual discrimination - the ability to notice differences between similar shapes. This skill is foundational for letter recognition and reading."
     - Research citation: "Bornstein (1985) - Visual processing and categorical thinking"
3. Simplified game:
   - Use 3 animals only (deer, rabbit, dog from screenshot)
   - 3 silhouettes to match
   - Larger touch targets than full game
4. Completion:
   - Award "Shape Detective" badge with animation
   - Track analytics: `game_played` with `game_id: demo_1`
5. Scientific backing in ARB file:
   - Key developmental window: ages 2-4
   - Connects to: Pre-reading skills, attention to detail

**Demo Game 2 - Animal Memory Pairs:**
1. File: `lib/onboarding/screens/demo_game_2_screen.dart`
2. Educational context:
   - Skill: "Visual Memory & Attention"
   - For Parents: `demo_game_2_science`
     - "Finding matching pairs strengthens working memory - your child's ability to hold and manipulate information. This directly supports math problem-solving and following multi-step instructions."
     - Research citation: "Gopnik & Meltzoff (1987) - Categorization and cognitive flexibility"
3. Simplified game:
   - 6 cards total (3 pairs)
   - Use animals from screenshot: rabbit, chicken, dog
   - Flip cards to find matches
4. Completion:
   - Award "Memory Master" badge
   - Track: `game_played` with `game_id: demo_2`
5. Scientific backing:
   - Working memory capacity grows significantly ages 3-5
   - Practice improves retention and recall speed

**Demo Game 3 - Object-Animal Association:**
1. File: `lib/onboarding/screens/demo_game_3_screen.dart`
2. Educational context:
   - Skill: "Logical Association & Categorization"
   - For Parents: `demo_game_3_science`
     - "Connecting objects to their users teaches categorization and logical thinking. Your child learns that things belong together for reasons - a key step in understanding cause and effect."
     - Research citation: "Piaget (1952) - Pre-operational cognitive development"
3. Simplified game:
   - 3 pairs only: bone→dog, carrot→rabbit, flower→bee
   - Drag object to matching animal
4. Completion:
   - Award "Logic Star" badge
   - Track: `game_played` with `game_id: demo_3`
5. Scientific backing:
   - Associative reasoning emerges ages 3-4
   - Foundation for understanding relationships and systems

**Demo Game 4 - Pattern Matching (Backpacks):**
1. File: `lib/onboarding/screens/demo_game_4_screen.dart`
2. Educational context:
   - Skill: "Pattern Recognition & Matching"
   - For Parents: `demo_game_4_science`
     - "Matching patterns builds pattern recognition - the ability to see relationships between things. This skill strongly predicts math success and helps children understand 'same' vs 'different.'"
     - Research citation: "Rittle-Johnson et al. (2019) - Pattern skills and mathematics"
3. Simplified game:
   - 3 backpacks with distinct symbols (star, heart, circle)
   - 3 children to match
   - Match backpack symbol to child's preference
4. Completion:
   - Award "Pattern Pro" badge
   - Track: `game_played` with `game_id: demo_4`
5. Scientific backing:
   - Pattern recognition peaks in development ages 4-5
   - Directly correlates with algebraic thinking later

**Demo Game 5 - Career Association:**
1. File: `lib/onboarding/screens/demo_game_5_screen.dart`
2. Educational context:
   - Skill: "Symbolic Thinking & Real-World Connections"
   - For Parents: `demo_game_5_science`
     - "Connecting tools to careers builds symbolic thinking - understanding that one thing can represent another. This abstract thinking is essential for language, math, and imagination."
     - Research citation: "Vygotsky (1978) - Symbolic representation in cognitive development"
3. Simplified game:
   - 3 pairs: stethoscope→doctor, helmet→astronaut, ladle→chef
   - Drag tool to matching career person
4. Completion:
   - Award "World Explorer" badge
   - Track: `game_played` with `game_id: demo_5`
5. Scientific backing:
   - Symbolic representation develops ages 4-5
   - Critical for pretend play, language, and literacy

**Progress Tracking:**
- After each demo game, update progress indicator
- Show collected badges in summary screen
- Personalize encouragement: "Amazing work, [Name]! Your child will love these games!"

### Task 3.5: Summary and Trust Screens (Portrait)

**Instructions:**

**Return to Portrait Transition:**
1. Show orientation transition screen
2. Text: "Great learning! Let's finish up →"
3. Force portrait orientation
4. Smooth animation transition

**Screen 10 - Personalized Summary:**
1. Create `lib/onboarding/screens/summary_screen.dart`
2. Layout:
   - Headline: "Your Journey So Far, [Name]"
   - Icon: Checklist with checkmarks
   - Summary bullets (dynamically generated):
     - "Perfect for [age]-year-olds learning [inferred from games played]"
     - "Games adapted to [child's name]'s pace" (if name was asked/default to "your child's")
     - "5 skills practiced: [list badge names]"
     - "Healthy screen time approach aligned with AAP guidelines"
   - Expert voice box:
     - "Educational games help [child's name] develop essential cognitive skills during critical developmental windows."
   - CTA: "See Your Personalized Plan"
3. Show earned badges in animated grid
4. Use warm, reassuring tone
5. Track analytics: `onboarding_completed`

**Screen 11 - Trust and Social Proof:**
1. Create `lib/onboarding/screens/trust_proof_screen.dart`
2. Layout:
   - Headline: "Trusted by Parents & Educators"
   - Badge: "Approved by Teachers" (Google Play official)
   - Parent testimonials (3-4 scrollable cards):
     - Use REAL App Store/Play Store reviews
     - Format: Photo avatar, "[Name], parent of [age]-year-old"
     - Quote emphasizing educational value
     - Example: "My 3-year-old daughter learned her colors in just two weeks! Love that it's actually educational."
   - Star rating: Show actual rating from stores
   - Scientific backing: "Based on early childhood development research"
   - CTA: "Unlock Full Learning Experience"
3. Fetch reviews dynamically if possible, or curate 10-15 real reviews
4. Rotate which reviews show to keep fresh
5. Track: `paywall_viewed` when proceeding

### Task 3.6: Three-Step Paywall Implementation (Portrait)

**Instructions:**

**Paywall Screen 1 - Value Proposition:**
1. Create `lib/onboarding/screens/paywall_step1_screen.dart`
2. Layout:
   - Headline: "Unlock All 15 Learning Games"
   - Subheadline: "Continue [child's name]'s learning journey"
   - Visual: Carousel of game screenshots (landscape images)
   - Features with checkmarks:
     - "15 educational games targeting key skills"
     - "Progress tracking for [child's name]"
     - "Ad-free, safe environment"
     - "New games added monthly"
     - "Designed for ages 2-5"
   - Social proof badge: "Approved by Teachers"
   - CTA: "See Plans →"
3. Carousel auto-advances every 3 seconds
4. Emphasize educational value over entertainment
5. Smooth slide transition to step 2

**Paywall Screen 2 - Free Trial Offer:**
1. Create `lib/onboarding/screens/paywall_step2_screen.dart`
2. Layout:
   - Hero image: Happy parent and child with device
   - Large headline: "Try All Features FREE for 7 Days"
   - Subheadline: "Then just €0.87/week"
   - Timeline visualization (vertical):
     - Icon + "Today: Full Access Unlocked"
     - Icon + "Day 5: We'll send a reminder"
     - Icon + "Day 7: Billing starts (cancel anytime before)"
   - Large checkbox: "✓ No Payment Due Now"
   - Fine print: "Cancel anytime in settings. After trial, €44.99/year."
   - CTA: "Start Free Trial →"
3. Ensure clarity about trial terms
4. Make cancellation process clear
5. Slide transition to step 3

**Paywall Screen 3 - Pricing Selection:**
1. Create `lib/onboarding/screens/paywall_step3_screen.dart`
2. Layout:
   - Top: "Women's Choice" badge with laurels + "500K+ users" (ONLY if verifiable)
   - Testimonial card (scrollable carousel)
   - 3 pricing cards (vertical stack):

     **Card 1 - Weekly:**
     - Border: Standard
     - Title: "Weekly"
     - Price: "€4.99/week"
     - Billing: "Billed €4.99 weekly"
     - Radio button selection

     **Card 2 - Yearly (RECOMMENDED):**
     - Border: Highlighted with glow
     - Badge: "Save 60%" in corner
     - Badge: "7-Day Free Trial"
     - Title: "Yearly"
     - Price: "€0.87/week"
     - Billing: "€44.99 billed annually"
     - Subtext: "Most popular choice"
     - Radio button (pre-selected)

     **Card 3 - Monthly:**
     - Border: Standard
     - Title: "Monthly"
     - Price: "€8.99/month"
     - Billing: "Billed €8.99 monthly"
     - Radio button selection

   - Fine print: "No commitment, cancel anytime"
   - Primary CTA: "Start My 7-Day Free Trial" (large button)
   - Secondary link: "View All Plans" (expands cards if collapsed)
   - Link: "Restore Purchases"

3. Card selection updates primary CTA text
4. Pricing fetched from store, fallback to displayed prices
5. Track: `subscription_selected` with `subscription_id` parameter
6. On CTA tap: Show existing parent gate math verification
7. After verification: Process purchase using new subscription system

---

## PHASE 4: SUBSCRIPTION SYSTEM (Week 4-5)

### Task 4.1: App Store Product Configuration

**Instructions:**

**iOS - App Store Connect:**
1. Log into App Store Connect
2. Navigate to app: Brainy Bunny
3. Create Subscription Group:
   - Name: "Brainy Bunny Premium"
   - Reference name: "premium_access"
4. Add three subscriptions to group:

   **Product 1:**
   - Product ID: `brainy_weekly`
   - Reference name: "Weekly Premium Access"
   - Duration: 1 week
   - Price: €4.99 (set for all territories)
   - No free trial

   **Product 2:**
   - Product ID: `brainy_yearly`
   - Reference name: "Yearly Premium Access"
   - Duration: 1 year
   - Price: €44.99 (set for all territories)
   - Introductory Offer: 7-day free trial
   - After trial: €44.99/year

   **Product 3:**
   - Product ID: `brainy_monthly`
   - Reference name: "Monthly Premium Access"
   - Duration: 1 month
   - Price: €8.99 (set for all territories)
   - No free trial

5. Set up subscription localization for all 18 languages:
   - Display name
   - Description emphasizing educational value
6. Configure App Store subscription management:
   - Enable family sharing if desired
   - Set grace period: 16 days
   - Set billing retry period: 60 days
7. Submit products for review
8. Document product IDs in `docs/SUBSCRIPTION_PRODUCTS.md`

**Android - Google Play Console:**
1. Log into Google Play Console
2. Navigate to app: Brainy Bunny
3. Go to Monetization → Products → In-app products
4. Create three subscription products:

   **Product 1:**
   - Product ID: `brainy_weekly`
   - Name: "Weekly Premium Access"
   - Description: Educational games for cognitive development
   - Billing period: 1 week
   - Price: €4.99 (set for all countries)
   - No free trial

   **Product 2:**
   - Product ID: `brainy_yearly`
   - Name: "Yearly Premium Access"
   - Description: Full year of educational games
   - Billing period: 1 year
   - Price: €44.99 (set for all countries)
   - Free trial: 7 days

   **Product 3:**
   - Product ID: `brainy_monthly`
   - Name: "Monthly Premium Access"
   - Description: Monthly educational game access
   - Billing period: 1 month
   - Price: €8.99 (set for all countries)
   - No free trial

5. Set up localization for all 18 languages
6. Configure subscription features:
   - Enable grace period: 3 days
   - Enable account hold: Yes
7. Activate products
8. Test with license testing account

### Task 4.2: Update PurchaseManager for Subscriptions

**Instructions:**

1. **Remove old code:**
   - Delete all one-time purchase logic
   - Delete `PRODUCT_ID` constant
   - Delete `buyNonConsumable()` method
   - Keep device ID, Firebase sync, and storage logic

2. **Add subscription constants:**
   - In `AppConstants` or `SubscriptionConstants`:
     - `SUBSCRIPTION_WEEKLY = 'brainy_weekly'`
     - `SUBSCRIPTION_MONTHLY = 'brainy_monthly'`
     - `SUBSCRIPTION_YEARLY = 'brainy_yearly'`
     - `SUBSCRIPTION_IDS = {weekly, monthly, yearly}`
     - `TRIAL_DURATION_DAYS = 7`

3. **Update storage structure:**
   - Add fields:
     - `subscription_tier` (weekly/monthly/yearly)
     - `subscription_expiry` (DateTime)
     - `subscription_status` (active/cancelled/expired)
     - `is_trial_active` (boolean)
     - `trial_start_date` (DateTime)
     - `original_purchase_date` (DateTime)
   - Migrate existing purchasers:
     - Convert old `full_game_purchased` flag
     - Set subscription_tier to `lifetime` for grandfathered users
     - Set expiry to far future date (year 2099)

4. **Implement new methods:**
   - `buySubscription(subscriptionId)`:
     - Load product details for subscription
     - Verify availability
     - Call `InAppPurchase.buyNonConsumable()` (yes, same method works)
     - Handle purchase flow

   - `isSubscriptionActive()`:
     - Check subscription_status == 'active'
     - Check expiry date is in future
     - If expired, trigger renewal check
     - Return boolean

   - `checkSubscriptionStatus()`:
     - Call platform to verify current entitlements
     - Update local storage with fresh data
# Brainy Bunny: Complete Implementation Plan (Continued)

## PHASE 4: SUBSCRIPTION SYSTEM (Week 4-5) - Continued

### Task 4.2: Update PurchaseManager for Subscriptions (Continued)

**Instructions (continued):**

4. **Implement new methods (continued):**

   - `checkSubscriptionStatus()` (continued):
     - Sync with RevenueCat for server state
     - If discrepancy found, use server as source of truth
     - Return current subscription tier and expiry

   - `handleSubscriptionExpiry()`:
     - Called when subscription detected as expired
     - Clear premium access locally
     - Show grace period notice if applicable
     - Don't delete user data, only access rights

   - `restorePurchases()` override:
     - Query platform for active subscriptions
     - Update local state for any found subscriptions
     - Handle grandfathered one-time purchases
     - Return success/failure with details

   - `isGameUnlocked(gameIndex)` modification:
     - Check if grandfathered (lifetime access)
     - Otherwise check `isSubscriptionActive()`
     - Return boolean for game access

5. **Update purchase flow handling:**
   - In `_handlePurchaseUpdates()`:
     - Extract subscription info from PurchaseDetails
     - Store subscription tier from product ID
     - Calculate expiry date based on duration
     - Check if purchase includes trial
     - Set trial flags appropriately

   - Handle trial-to-paid conversion:
     - Detect when trial period ends
     - Update `is_trial_active` to false
     - Verify billing was successful
     - Show confirmation to user

6. **Add subscription management UI:**
   - Create method `openSubscriptionManagement()`:
     - iOS: Deep link to App Store subscriptions
     - Android: Deep link to Google Play subscriptions
     - Fallback: Show instructions
   - Add button in app settings

7. **Test scenarios:**
   - Document test cases in `docs/SUBSCRIPTION_TESTING.md`:
     - New trial start
     - Trial conversion to paid
     - Subscription cancellation during trial
     - Subscription renewal
     - Failed payment handling
     - Restore purchases on new device
     - Downgrade/upgrade between tiers
     - Grandfathered user migration

### Task 4.3: Server-Side Verification Setup

**Instructions:**

1. **Create Firebase Cloud Function:**
   - File: `functions/src/verifyPurchase.ts` (create functions directory)
   - Initialize Firebase Functions and Admin SDK
   - Create HTTPS callable function `verifyPurchase`
   - Accept parameters: platform, receiptData, userId

2. **iOS Receipt Verification:**
   - Implement App Store Server API calls:
     - Use production URL: `https://buy.itunes.apple.com/verifyReceipt`
     - Fallback to sandbox for testing
     - Parse response for subscription status
     - Extract expiry date, product ID
     - Check auto-renew status
   - Handle receipt validation errors:
     - 21007: Use sandbox environment
     - 21010: Receipt from test environment
     - Other codes: Document in error handling

3. **Android Receipt Verification:**
   - Implement Google Play Developer API:
     - Use Google Play Billing Library server-side
     - Authenticate with service account
     - Query subscription purchase status
     - Extract expiry, auto-renew, payment state
   - Handle subscription states:
     - Active
     - Expired
     - In grace period
     - On hold (payment issue)

4. **Firestore Data Storage:**
   - Create collection: `verified_subscriptions`
   - Document ID: userId
   - Fields:
     - platform (ios/android)
     - subscription_tier
     - status (active/expired/cancelled)
     - expiry_timestamp
     - auto_renew_enabled
     - original_purchase_date
     - last_verified_timestamp
   - Security rules:
     - Users can read own document
     - Only Cloud Functions can write

5. **Client Integration:**
   - Update PurchaseManager:
     - After purchase, call Cloud Function
     - Pass receipt data to function
     - Wait for verification response
     - Only grant access after server confirmation
   - On app launch:
     - Check Firestore for verified status
     - If expired, trigger verification
     - Update local state from server

6. **Security Measures:**
   - Store App Store shared secret in Firebase Config
   - Store Google service account JSON securely
   - Rate limit verification requests (10/minute per user)
   - Log all verification attempts
   - Alert on suspicious patterns

7. **Deploy and Test:**
   - Deploy Cloud Function: `firebase deploy --only functions`
   - Test with TestFlight/Internal Testing purchases
   - Verify Firestore documents created
   - Test receipt tampering detection
   - Document deployment process

### Task 4.4: Grandfathering Existing Users

**Instructions:**

1. **Create migration script:**
   - File: `lib/services/subscription/user_migration_service.dart`
   - Run once on first app update
   - Check for old purchase flag: `full_game_purchased`
   - If true and no subscription data:
     - Create subscription record with tier: `lifetime`
     - Set expiry to DateTime(2099, 12, 31)
     - Set status: `active`
     - Mark as grandfathered: `is_grandfathered: true`
   - Set migration completed flag
   - Log migration for analytics

2. **Update purchase manager:**
   - In `isSubscriptionActive()`:
     - Check for `is_grandfathered` flag
     - If true, always return true (lifetime access)
     - Don't check expiry for grandfathered users
   - In subscription management:
     - Show special badge: "Lifetime Access"
     - Don't show renewal or cancellation options
     - Thank user for early support

3. **Communication to users:**
   - Create in-app message for migrated users:
     - Title: "Thank You, Early Supporter!"
     - Body: "As a thank you for your early purchase, you now have lifetime access to all games - no subscription needed!"
     - CTA: "Continue Playing"
   - Show once on first launch after update

4. **Test migration:**
   - Install old version
   - Make test purchase
   - Update to new version
   - Verify lifetime access granted
   - Verify no subscription prompts shown

---

## PHASE 5: HOME SCREEN MODIFICATIONS (Week 5)

### Task 5.1: Remove Old Purchase Flow

**Instructions:**

1. **Delete unused overlays:**
   - Remove `PurchaseOfferOverlay` (replaced by paywall)
   - Keep `AgeVerificationOverlay` (moved to paywall flow)
   - Remove `PurchasePromptOverlay`
   - Keep error and pending overlays (still needed)

2. **Update game selection handler:**
   - In `ToddlerGameMenu._onGameButtonTap()`:
     - If game locked and NOT subscribed:
       - Don't show purchase overlay
       - Show simple dialog instead:
         - Title: "Premium Game"
         - Message: "This game is part of Brainy Bunny Premium. Subscribe to unlock all 15 educational games!"
         - CTA 1: "See Plans" → Navigate to paywall
         - CTA 2: "Already Premium? Restore" → Call restore
     - If subscribed: Launch game normally

3. **Update MenuButton:**
   - Keep lock icon visual
   - Change lock icon to "Premium" badge:
     - Add star icon overlay on lock
     - Change color to gold gradient
   - Update tooltip: "Premium Game - Subscribe to Unlock"

4. **Simplify unlock animation:**
   - Keep existing unlock animation
   - Trigger when subscription verified
   - Remove confetti (only needed in onboarding)
   - Play success sound

5. **Update RestorePurchases:**
   - Keep button in settings/menu
   - Update to check subscriptions instead of purchases
   - Show loading indicator during restore
   - Show success message with subscription details
   - Show error if no subscription found

### Task 5.2: Subscription Status Display

**Instructions:**

1. **Add subscription indicator:**
   - Create `lib/ui/widgets/subscription_badge.dart`
   - Show in top-right of home screen
   - Display based on status:
     - Active: "Premium" with crown icon
     - Trial: "7-Day Trial" with timer
     - Expired: "Subscription Ended" (if recently expired)
     - Grandfathered: "Lifetime Access" with special badge
   - Tappable to open subscription details

2. **Create subscription details screen:**
   - File: `lib/ui/screens/subscription_details_screen.dart`
   - Show current plan details:
     - Plan name (Weekly/Monthly/Yearly/Lifetime)
     - Next billing date (if active)
     - Renewal price
     - Days remaining in trial (if applicable)
   - Action buttons:
     - "Manage Subscription" → Opens store management
     - "Cancel Subscription" → Opens store management
     - "Change Plan" → Shows paywall with other options
   - For grandfathered users:
     - Show "Lifetime Access - Thank You!"
     - No management buttons needed

3. **Trial expiry reminder:**
   - Create notification service (if not exists)
   - Schedule local notification:
     - Day 5 of trial: "Your trial ends in 2 days"
     - Day 6 of trial: "Your trial ends tomorrow"
   - Don't spam - one notification per day max
   - Include deep link to subscription management

---

## PHASE 6: CRITICAL BUG FIXES (Week 6)

### Task 6.1: Orientation Lock (Android Focus)

**Instructions:**

1. **AndroidManifest.xml configuration:**
   - Open `android/app/src/main/AndroidManifest.xml`
   - Find `<activity android:name=".MainActivity">`
   - Add attribute: `android:screenOrientation="sensorLandscape"`
   - Add attribute: `android:configChanges="orientation|screenSize|keyboard|keyboardHidden"`
   - This prevents activity restart on orientation change

2. **Remove Dart orientation code:**
   - Search codebase for `SystemChrome.setPreferredOrientations`
   - Delete ALL calls except:
     - In onboarding coordinator (for portrait/landscape transitions)
   - Delete `_setLandscapeOrientation()` method from HomeScreen
   - Delete `_refreshGameLayoutAfterDelay()` method

3. **Handle onboarding orientation properly:**
   - Only in `OnboardingCoordinator`:
     - Force portrait for screens 1-4, 10-14
     - Force landscape for screens 5-9 (demo games)
   - After onboarding complete:
     - Reset to landscape (default for app)
   - Use `SystemChrome.setPreferredOrientations()` ONLY in coordinator

4. **iOS Configuration:**
   - Open Xcode project
   - In Target → General → Deployment Info:
     - Uncheck Portrait
     - Check Landscape Left
     - Check Landscape Right
   - In Info.plist:
     - Set `UISupportedInterfaceOrientations` to landscape only

5. **Test scenarios:**
   - Rotate device during gameplay
   - Take screenshot (common trigger)
   - Switch to split-screen mode
   - Receive phone call
   - Verify orientation locks correctly
   - Verify no white flash or restart

### Task 6.2: Layout System Fixes

**Instructions:**

1. **Fix MenuButton rendering:**
   - Open `menu_button.dart`
   - Replace hack in `set isLocked()`:
     ```
     // REMOVE THIS:
     if (isMounted) {
       removeFromParent();
       parent?.add(this);
     }
     ```
   - Replace with proper repaint:
     - Add property: `bool _needsRepaint = false`
     - In setter: `_needsRepaint = true`
     - In update method: Check flag and call rendering update
   - Use Flame's component lifecycle properly

2. **Fix MenuGrid layout:**
   - Remove `_needsRelayout` flag hack
   - Remove `forceRelayout()` method
   - Implement proper `onGameResize()`:
     - Only recalculate layout when size actually changes
     - Don't trigger on every frame
     - Cache last size to detect changes
   - Remove `Future.delayed()` layout calls

3. **Optimize game transitions:**
   - Remove manual `removeFromParent()/add()` cycles
   - Use Flame's proper game switching:
     - Pause current game
     - Add new game to tree
     - Remove old game after transition
   - Don't force manual cleanup every time

4. **Test performance:**
   - Profile with Flutter DevTools
   - Check for excessive rebuilds
   - Verify smooth 60fps during gameplay
   - Test on low-end devices (if available)

### Task 6.3: Audio System Refactor

**Instructions:**

1. **Create audio state enum:**
   - File: `lib/services/audio/audio_state.dart`
   - Define enum: `AudioState { stopped, playingMenu, playingGame }`
   - Remove all boolean flags from AudioService

2. **Refactor AudioService:**
   - Replace `_isMusicPlaying`, `_isMusicPaused`, `_currentMusicType` with single `_audioState`
   - Remove all `Future.delayed()` calls
   - Implement state transitions:
     ```
     _setState(AudioState newState) {
       if (_audioState == newState) return;
       _audioState = newState;
       _executeStateTransition();
     }
     ```
   - In `_executeStateTransition()`:
     - Stop current audio if needed
     - Start new audio based on state
     - No delays, immediate execution

3. **Configure audio player properly:**
   - Set release mode: `FlameAudio.bgm.audioPlayer.setReleaseMode(ReleaseMode.stop)`
   - Set volume: `FlameAudio.bgm.audioPlayer.setVolume()`
   - Handle audio focus for platform integration

4. **Remove force methods:**
   - Delete `forcePlayMenuMusic()`
   - Replace with simple `playMenuMusic()`
   - Let state machine handle conflicts
   - Trust single source of truth

5. **Test audio scenarios:**
   - Background/foreground transitions
   - Game to menu transitions
   - Multiple rapid transitions
   - Device volume changes
   - Verify no audio glitches or overlaps

### Task 6.4: Memory Leak Prevention

**Instructions:**

1. **Audit StreamControllers:**
   - Search for `StreamController` in codebase
   - List all controllers with their locations
   - For each controller:
     - Verify `close()` called in dispose
     - Verify no listeners after dispose
     - Add disposal tracking if needed

2. **Fix PurchaseManager:**
   - Ensure streams closed in `dispose()`
   - Wrap in try-catch to handle errors
   - Nullify subscriptions after cancel:
     ```
     _subscription?.cancel();
     _subscription = null;
     ```
   - Test multiple dispose calls (should be safe)

3. **Fix game lifecycle:**
   - Verify all components removed on game end
   - Clear animation controllers
   - Cancel all timers
   - Remove event listeners
   - Test: Play game → Exit → Check memory

4. **Add debug memory tracking:**
   - Create debug overlay showing:
     - Active game components count
     - Active streams count
     - Active timers count
   - Only in debug builds
   - Help detect leaks during testing

5. **Test for leaks:**
   - Use Flutter DevTools memory profiler
   - Play game, exit, play again 10x
   - Check memory growth
   - Should remain stable or minor growth
   - Fix any identified leaks

### Task 6.5: Price Loading Robustness

**Instructions:**

1. **Implement retry with backoff:**
   - In `_loadProductDetailsEnhanced()`:
     - Wrap in retry loop
     - Attempt 1: Immediate
     - Attempt 2: 1 second delay
     - Attempt 3: 2 second delay
     - Attempt 4: 4 second delay
     - After 4 attempts, use fallback
   - Log each attempt
   - Track retry analytics

2. **Add local price cache:**
   - Save last known price to SharedPreferences:
     - Key: `last_known_price_[product_id]`
     - Value: Price string with timestamp
   - On load failure:
     - Check cache
     - If cache < 24 hours old, use it
     - Show indicator: "Price from [date]"
   - Update cache on successful load

3. **Show loading states properly:**
   - In paywall: Show skeleton loading
   - Replace price with animated placeholder
   - Text: "Loading price from store..."
   - Don't block user interaction
   - Enable purchase after price loads

4. **Add manual refresh:**
   - In paywall, add refresh button
   - When tapped: Re-trigger price load
   - Show loading indicator
   - Update UI on success/failure

5. **Better error messages:**
   - "Connecting to App Store..." (initial)
   - "Taking longer than expected..." (after 5s)
   - "Unable to connect. Using last known price." (fallback)
   - Never show technical error codes to users

### Task 6.6: "Already Owned" Handling

**Instructions:**

1. **Detect "already owned" error:**
   - In `_handlePurchaseUpdates()`:
     - Check error code specifically
     - iOS: Error code 21100
     - Android: ITEM_ALREADY_OWNED
   - Don't treat as purchase error

2. **Auto-restore on already owned:**
   - When detected:
     - Show message: "Looks like you already own this!"
     - Automatically call `restorePurchases()`
     - Show loading: "Restoring your subscription..."
   - On restore success:
     - Show success message
     - Grant access immediately
   - On restore failure:
     - Show error with support contact

3. **Verify with server:**
   - Even after "already owned":
     - Still call server verification
     - Use server state as truth
     - Update local only after server confirms
   - Prevents fraud

4. **Test scenarios:**
   - Purchase on device A
   - Try to purchase again on device A
   - Try to purchase on device B (should work if restore used)
   - Verify proper messaging
   - Verify access granted correctly

---

## PHASE 7: LOCALIZATION IMPLEMENTATION (Week 7)

### Task 7.1: ARB File Structure

**Instructions:**

1. **Create base ARB file:**
   - File: `lib/l10n/app_en.arb`
   - Structure all strings by feature:
     - Onboarding section
     - Paywall section
     - Games section
     - Settings section
     - Errors section
   - Use descriptive keys: `onboarding_welcome_headline`
   - Add descriptions for translators:
     ```json
     {
       "onboarding_welcome_headline": "Help Your Child Learn & Grow",
       "@onboarding_welcome_headline": {
         "description": "Main headline on first onboarding screen. Should be warm and reassuring for parents."
       }
     }
     ```

2. **Key naming convention:**
   - Format: `{feature}_{screen}_{element}`
   - Examples:
     - `onboarding_welcome_headline`
     - `paywall_trial_offer_title`
     - `game_completion_celebration`
     - `error_purchase_failed_message`
   - Use consistent naming throughout

3. **Parameterized strings:**
   - For personalization:
     ```json
     {
       "onboarding_name_greeting": "Great, {name}!",
       "@onboarding_name_greeting": {
         "description": "Greeting after parent enters name",
         "placeholders": {
           "name": {
             "type": "String",
             "example": "Sarah"
           }
         }
       }
     }
     ```
   - For ages, prices, dates use parameters

4. **Scientific content strings:**
   - Create keys for each demo game:
     - `demo_game_1_science_title`
     - `demo_game_1_science_description`
     - `demo_game_1_research_citation`
   - Full text for each in English
   - Professional tone for translations

5. **Count all strings:**
   - Estimate: 200-300 strings total
   - Document count in `docs/LOCALIZATION.md`
   - Budget for translation costs

### Task 7.2: Translation Process

**Instructions:**

1. **Prepare for translation:**
   - Export English ARB to CSV format
   - Columns: Key, English Text, Context, Character Count
   - Add translation columns for each language
   - Include character limits where relevant (button text, etc.)

2. **Translation priorities:**
   - Tier 1 (launch-critical): English, Spanish (ES/MX), French (FR), German
   - Tier 2 (post-launch): Portuguese (BR/PT), Italian, Japanese, Korean, Chinese (CN/TW)
   - Tier 3 (expansion): Arabic, Hindi, Indonesian, Russian, Catalan

3. **Translation guidelines document:**
   - Create `docs/TRANSLATION_GUIDELINES.md`
   - Include:
     - Tone: Warm, reassuring, educational
     - Audience: Parents of toddlers
     - Avoid: Medical jargon, academic language
     - Keep: Simple, clear, benefits-focused
     - Educational content: Maintain accuracy
     - Research citations: Translate author names only if localized publications exist

4. **Professional translation:**
   - Use professional translation service (recommended)
   - Or: Crowdsource from native speaker parents
   - Require: Native speaker + parent (understands context)
   - Review: Educational content by local experts

5. **Import translations:**
   - Create ARB file for each language
   - Naming: `app_{locale}.arb`
   - Validate format
   - Run generation: `flutter gen-l10n`
   - Verify compilation

### Task 7.3: RTL Language Support

**Instructions:**

1. **Arabic RTL configuration:**
   - MaterialApp must detect RTL automatically
   - Test with Arabic locale
   - Verify layout mirrors correctly
   - Check icon directions (arrows should flip)

2. **Fix RTL issues:**
   - Image assets: Don't flip, keep as-is
   - Text alignment: Auto-adjust
   - Padding/margins: Use start/end instead of left/right
   - Button positions: Should mirror
   - Back button: Auto-mirrors to right side

3. **Test Arabic specifically:**
   - All onboarding screens
   - Paywall layout
   - Game instructions (if any)
   - Settings screen
   - Take screenshots for App Store

4. **Other RTL considerations:**
   - Hebrew (if added later)
   - Urdu (if added later)
   - Same principles apply

### Task 7.4: Locale-Specific Content

**Instructions:**

1. **Price formatting:**
   - Use `NumberFormat.currency()` with locale
   - Currency symbols: Auto-detected from store
   - Thousand separators: Locale-specific
   - Example: €44,99 vs €44.99 vs 44,99€

2. **Date formatting:**
   - Use `DateFormat` from intl package
   - Trial expiry dates: Locale-appropriate format
   - MM/DD/YYYY vs DD/MM/YYYY vs YYYY/MM/DD

3. **Name fields:**
   - Accept Unicode characters
   - Support non-Latin scripts (Arabic, Chinese, etc.)
   - Test with emoji (should work)
   - No length issues with wider characters

4. **Age selection:**
   - Translate age labels: "2 years old"
   - Some languages: Might need different phrasing
   - Chinese: Use 岁 (years old)
   - Arabic: Use سنوات (years)

5. **Testing per locale:**
   - Create test matrix: 18 languages × Key screens
   - Minimum: Test one screen per language
   - Full: Test complete flow for Tier 1 languages
   - Document issues in GitHub/tracking system

### Task 7.5: Store Listings Localization

**Instructions:**

1. **App Store Connect:**
   - For each locale:
     - App name (if localized)
     - Subtitle
     - Description (emphasize educational value)
     - Keywords (research per market)
     - Screenshots (add localized text overlays)
     - Preview video (add localized subtitles)

2. **Google Play Console:**
   - Same as above for each locale
   - Short description (80 chars)
   - Full description (4000 chars)
   - Use cultural relevance per market

3. **Cultural adaptation:**
   - Colors: Green = luck (China), White = mourning (some Asian cultures)
   - Images: Diverse children representation
   - Scientific citations: Use local research if available
   - Teacher approval: Get local equivalents if possible

4. **Priority markets:**
   - Research download volumes per country
   - Focus localization on top 10 markets
   - Don't spread thin - quality over quantity

---

## PHASE 8: TESTING & VALIDATION (Week 7-8)

### Task 8.1: Analytics Validation

**Instructions:**

1. **Create test device:**
   - Physical device preferred (vs simulator)
   - Install debug build
   - Enable analytics debug view (Adjust/Meta)

2. **Test event sequence:**
   - **Install flow:**
     - Fresh install
     - Verify `app_installed` in Adjust (wait 24h for attribution)
     - Check install recorded in dashboard

   - **Onboarding flow:**
     - Start onboarding → Verify `onboarding_started`
     - Complete each screen → Check progress tracking
     - Play each demo game → Verify 5× `game_played` with game_id
     - Complete onboarding → Verify `onboarding_completed`

   - **Paywall flow:**
     - View paywall → Verify `paywall_viewed`
     - Select yearly plan → Verify selection tracked
     - Start trial → Verify `trial_started` in Adjust, Meta, RevenueCat
     - Check all three platforms received event within 1 minute

   - **Subscription flow:**
     - Complete purchase → Verify `subscription_purchased`
     - Check RevenueCat customer page:
       - Should show purchase
       - Should show Adjust ID in attributes
       - Check webhook events: Should show Response 200 to Adjust
     - Check Adjust: Should show purchase event with revenue
     - Check Meta Event Manager: Should show Subscribe event

3. **Test with multiple scenarios:**
   - Different subscription tiers (weekly, monthly, yearly)
   - Purchase cancellation
   - Restore purchases
   - Failed payment (use test card)

4. **Document findings:**
   - Create `docs/ANALYTICS_VALIDATION_REPORT.md`
   - Include screenshots from dashboards
   - List any discrepancies
   - Create issues for fixes needed

### Task 8.2: Subscription Flow Testing

**Instructions:**

1. **iOS TestFlight setup:**
   - Create test group: "Subscription Testers"
   - Invite 5-10 testers
   - Provide test credentials for sandbox
   - Create testing guide document

2. **iOS test scenarios:**
   - **Trial flow:**
     - Start 7-day trial
     - Verify free access
     - Wait 24 hours (or use date manipulation)
     - Verify trial still active
     - Verify billing scheduled correctly

   - **Trial cancellation:**
     - Start trial
     - Cancel in Settings → Subscriptions
     - Verify access continues until end date
     - Verify billing doesn't occur

   - **Trial to paid:**
     - Start trial
     - Let trial expire naturally
     - Verify billing occurs
     - Verify subscription continues

   - **Restore purchases:**
     - Subscribe on device A
     - Install on device B (same Apple ID)
     - Tap "Restore Purchases"
     - Verify subscription activates

3. **Android Internal Testing:**
   - Create internal test track
   - Upload APK/AAB
   - Add test users
   - Same test scenarios as iOS

4. **Edge case testing:**
   - Airplane mode during purchase
   - App killed mid-purchase
   - Multiple rapid purchase attempts
   - Subscription on device A, restore on device B
   - Expired subscription, then renewal
   - Downgrade from yearly to monthly
   - Upgrade from monthly to yearly

5. **Payment testing:**
   - Test card: Use platform test cards
   - Declined card: Verify graceful error
   - Insufficient funds: Check error message
   - 3D Secure: Complete flow
   - Family sharing (iOS): If enabled, verify

### Task 8.3: Onboarding Flow Testing

**Instructions:**

1. **Test complete flow:**
   - Fresh install
   - Go through all 14 screens
   - Time how long it takes
   - Note any friction points
   - Test on various screen sizes

2. **Orientation testing:**
   - Portrait screens: Verify locked
   - Transition to landscape: Smooth?
   - Play 5 demo games: All landscape?
   - Return to portrait: Smooth?
   - Final paywall: Portrait locked?

3. **Input validation:**
   - Name field: Try edge cases
     - Single character (should reject)
     - Very long name (should truncate)
     - Special characters
     - Emoji
     - Non-Latin scripts (Arabic, Chinese)
   - Age selection: All options work?
   - Demo games: Touch targets adequate?

4. **Localization testing:**
   - Switch device language
   - Verify all text loads correctly
   - Check for:
     - Truncated text
     - Overflow issues
     - Missing translations (fallback to English)
     - Broken formatting
   - Test RTL (Arabic): Layout mirrors?

5. **Performance testing:**
   - Old device test (if available)
   - Measure FPS during animations
   - Check memory usage
   - Verify no lag or stuttering
   - Demo games run smoothly?

6. **Analytics verification:**
   - Each screen view tracked?
   - Demo game plays recorded?
   - Completion event fires?
   - Paywall view triggers?

### Task 8.4: Regression Testing

**Instructions:**

1. **Test existing functionality:**
   - All 15 games still work?
   - Game progression (rounds) correct?
   - Audio plays correctly?
   - Home screen layout correct?
   - Settings screen functional?

2. **Subscription access control:**
   - Free games (1-5): Always accessible?
   - Premium games (6-15): Locked without subscription?
   - After subscription: All games unlock?
   - After expiry: Games lock again?
   - Grandfathered users: Always unlocked?

3. **Purchase flow:**
   - Old one-time purchase removed?
   - New subscription flow works?
   - Parent gate still functions?
   - Age verification works?
   - Error handling graceful?

4. **Create test checklist:**
   - Document in `docs/TESTING_CHECKLIST.md`
   - Checkboxes for each scenario
   - Assign to testers
   - Track completion

### Task 8.5: Security Testing

**Instructions:**

1. **Receipt manipulation testing:**
   - Try to access premium without subscription
   - Modify local storage manually
   - Clear Firestore verified subscription
   - Should fail - server is source of truth

2. **Server verification:**
   - Make valid purchase
   - Verify Firestore document created
   - Modify expiry date locally
   - Restart app
   - Should sync from server, fix local state

3. **Test invalid receipts:**
   - Use fake receipt data
   - Submit to Cloud Function
   - Should reject with error
   - Should not grant access

4. **Test security measures:**
   - Rate limiting: Try 20 verifications in 1 minute
   - Should throttle after 10
   - Device ID: Multiple devices same user
   - Should each work independently

---

## PHASE 9: FINAL PREPARATION (Week 8)

### Task 9.1: App Store Submission Prep

**Instructions:**

1. **iOS Submission:**
   - Update version number: Increment to next version
   - Update build number: Must be higher than current
   - Prepare metadata:
     - Screenshots (all required sizes)
     - App preview video (optional but recommended)
     - Description emphasizing education
     - Keywords optimized
     - Age rating: 4+ (educational, no problematic content)
     - Export compliance: Set to NO (no encryption)
   - Submit for review

2. **Android Submission:**
   - Update version code and version name
   - Generate signed APK/AAB
   - Upload to Play Console
   - Internal testing → Production (staged rollout)
   - Store listing:
     - Screenshots with localized overlays
     - Feature graphic
     - Description
     - Content rating: EVERYONE
   - Submit for review

3. **Subscription metadata:**
   - Both platforms: Review subscription products
   - Ensure descriptions clear
   - Pricing correct
   - Trial terms explicit
   - Cancellation policy clear

4. **Privacy policy:**
   - Update if needed
   - Mention data collection:
     - Analytics (Adjust, Meta)
     - Purchase data (RevenueCat)
     - Anonymous authentication
   - Link in app and store listings

### Task 9.2: Documentation Completion

**Instructions:**
# Brainy Bunny: Complete Implementation Plan (Continued)

## PHASE 9: FINAL PREPARATION (Week 8) - Continued

### Task 9.2: Documentation Completion (Continued)

**Instructions:**

1. **Technical documentation:**
   - Create `docs/ARCHITECTURE.md`:
     - System overview diagram
     - Data flow: App → Analytics → Server
     - Subscription verification flow
     - Component relationships
   - Update `README.md`:
     - Project description
     - Setup instructions
     - Environment variables needed
     - How to run locally
   - Create `docs/API_INTEGRATION.md`:
     - Adjust configuration
     - RevenueCat setup
     - Meta SDK setup
     - Firebase Cloud Functions

2. **Operational documentation:**
   - Create `docs/MONITORING.md`:
     - How to check analytics dashboards
     - KPIs to track daily
     - Alert thresholds
     - Troubleshooting common issues
   - Create `docs/SUBSCRIPTION_MANAGEMENT.md`:
     - How to handle customer support
     - Refund process
     - Restore purchase issues
     - Expired subscription handling

3. **Development documentation:**
   - Create `docs/ADDING_GAMES.md`:
     - How to add game 16+
     - Asset requirements
     - Code structure
     - Testing checklist
   - Create `docs/LOCALIZATION_PROCESS.md`:
     - How to add new language
     - Translation workflow
     - Testing new translations
     - Updating existing translations

4. **Runbook creation:**
   - Create `docs/INCIDENT_RESPONSE.md`:
     - Purchase system down: Steps to resolve
     - Analytics not tracking: Debugging guide
     - App Store rejection: Common issues and fixes
     - High crash rate: Investigation process

### Task 9.3: Performance Optimization

**Instructions:**

1. **App startup optimization:**
   - Profile app launch time with Flutter DevTools
   - Target: < 3 seconds to first screen
   - Optimize:
     - Defer heavy initialization to background
     - Load only essential assets on startup
     - Use lazy loading for non-critical services
   - Test on low-end devices

2. **Asset optimization:**
   - Compress all images without quality loss:
     - Use tools like TinyPNG or ImageOptim
     - Target: Reduce total app size by 20%
   - Remove unused assets:
     - Search for unreferenced files
     - Delete safely
   - Implement asset caching strategy

3. **Network optimization:**
   - Implement request timeout: 10 seconds
   - Add request retry logic: 3 attempts
   - Cache API responses where appropriate
   - Compress requests/responses if large

4. **Memory optimization:**
   - Profile memory usage during gameplay
   - Target: < 200MB on devices with 2GB RAM
   - Optimize:
     - Clear unused textures after game
     - Dispose animations properly
     - Limit particle effects
   - Test memory on older devices

5. **Battery optimization:**
   - Reduce background processing
   - Pause game when app backgrounded
   - Lower frame rate to 30fps if on battery saver
   - Test battery drain over 30 min session

### Task 9.4: Crash Reporting Setup

**Instructions:**

1. **Install Firebase Crashlytics:**
   - Add `firebase_crashlytics` dependency
   - Initialize in main.dart before runApp
   - Configure for iOS and Android
   - Set up custom keys for context

2. **Implement error boundaries:**
   - Wrap app in error handler:
     ```dart
     FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterError;
     ```
   - Catch async errors
   - Log to Crashlytics
   - Show user-friendly error screen

3. **Add custom logging:**
   - Log critical events:
     - Purchase started
     - Purchase completed
     - Subscription verified
     - Game launched
   - Add user ID to crash reports
   - Add subscription status to context

4. **Set up alerts:**
   - Firebase Console: Configure crash alerts
   - Email notification on spike
   - Slack integration if available
   - Define "critical" crash threshold

5. **Test crash reporting:**
   - Force test crash in debug
   - Verify appears in dashboard
   - Check context data included
   - Verify alerts trigger

### Task 9.5: Analytics Dashboard Setup

**Instructions:**

1. **Adjust dashboard configuration:**
   - Create custom dashboard:
     - KPIs: Installs, Onboarding completion, Trial starts, Conversions
     - Charts: Funnel visualization
     - Filters: By country, platform, attribution source
   - Set up cohort reports:
     - Day 1, 7, 30 retention
     - LTV by cohort
   - Schedule weekly email reports

2. **RevenueCat dashboard:**
   - Configure overview:
     - MRR (Monthly Recurring Revenue)
     - Active subscriptions
     - Churn rate
     - Trial conversion rate
   - Set up charts:
     - Revenue over time
     - Subscriber growth
     - Platform breakdown
   - Create alerts:
     - Unusual churn spike
     - Failed payments increase

3. **Meta Events Manager:**
   - Verify all events flowing
   - Create custom conversions:
     - Trial start
     - Purchase
   - Set up attribution window: 7-day click
   - Configure data retention

4. **Create unified reporting:**
   - Spreadsheet or Data Studio:
     - Pull data from all sources
     - Calculate key metrics:
       - CAC (Customer Acquisition Cost)
       - LTV (Lifetime Value)
       - Payback period
       - Conversion funnel percentages
   - Update daily or weekly
   - Share with stakeholders

### Task 9.6: Pre-Launch Checklist

**Instructions:**

1. **Functional verification:**
   - [ ] All 15 games playable
   - [ ] Onboarding completes successfully
   - [ ] Demo games work in landscape
   - [ ] Paywall displays correctly
   - [ ] Subscriptions can be purchased
   - [ ] Trial starts correctly
   - [ ] Restore purchases works
   - [ ] Analytics events fire
   - [ ] All 18 languages load
   - [ ] No crashes in critical paths

2. **Content verification:**
   - [ ] All strings translated
   - [ ] No placeholder text visible
   - [ ] Images optimized
   - [ ] Scientific citations accurate
   - [ ] Teacher approval claim verified
   - [ ] Pricing correct in all regions
   - [ ] Privacy policy updated
   - [ ] Terms of service current

3. **Technical verification:**
   - [ ] Server verification working
   - [ ] Cloud Functions deployed
   - [ ] Firebase configured
   - [ ] Crashlytics enabled
   - [ ] Analytics SDKs initialized
   - [ ] SKAN endpoint configured
   - [ ] Orientation locks working
   - [ ] Audio system stable
   - [ ] Memory leaks fixed

4. **Store verification:**
   - [ ] iOS screenshots uploaded (all sizes)
   - [ ] Android screenshots uploaded
   - [ ] App preview video ready
   - [ ] Description optimized
   - [ ] Keywords researched
   - [ ] Age ratings set
   - [ ] Subscription products approved
   - [ ] Privacy manifests submitted

5. **Legal verification:**
   - [ ] COPPA compliance (parent gate)
   - [ ] Subscription terms clear
   - [ ] Cancellation policy visible
   - [ ] Auto-renewal disclosed
   - [ ] Privacy policy linked
   - [ ] Terms of service linked
   - [ ] Data collection disclosed

---

## IMPLEMENTATION PRIORITIES

### Critical Path (Must Complete)

**Week 1-2: Foundation**
- Analytics SDK integration (Adjust, RevenueCat, Meta)
- Constants reorganization
- Localization infrastructure

**Week 2-4: Core Features**
- Onboarding flow (14 screens)
- Demo games implementation
- Educational content

**Week 4-5: Monetization**
- Subscription system
- Server verification
- Payment flow

**Week 5: Polish**
- Home screen updates
- Bug fixes (orientation, audio, memory)

**Week 6-7: Quality**
- Testing all flows
- Localization completion
- Analytics validation

**Week 8: Launch**
- Final testing
- Store submission
- Documentation

### Optional Enhancements (Post-Launch)

- A/B testing framework
- Push notifications for trial reminders
- Parental dashboard
- Progress tracking per child
- Additional games (16-20)
- Referral system
- Social sharing

---

## RISK MANAGEMENT

### High-Risk Items

**1. Analytics Integration Failure**
- Risk: Events don't reach dashboards
- Mitigation: Thorough testing before launch
- Fallback: Local event logging, manual upload

**2. Subscription System Rejection**
- Risk: Apple/Google rejects subscription changes
- Mitigation: Follow guidelines exactly, clear terms
- Fallback: Delay launch, fix issues, resubmit

**3. Server Verification Issues**
- Risk: Cloud Function fails, grants fraudulent access
- Mitigation: Extensive testing, rate limiting, alerts
- Fallback: Disable verification temporarily, fix urgently

**4. Localization Quality**
- Risk: Poor translations harm user experience
- Mitigation: Professional translators, native speaker review
- Fallback: Launch with Tier 1 languages only

**5. Onboarding Too Long**
- Risk: Users drop off before paywall
- Mitigation: Track funnel metrics, optimize length
- Fallback: A/B test shorter version post-launch

### Medium-Risk Items

**1. Performance Issues**
- Risk: App slow on older devices
- Mitigation: Test on low-end devices, optimize
- Fallback: Add performance mode, reduce effects

**2. Price Loading Failures**
- Risk: Users see wrong price or can't purchase
- Mitigation: Retry logic, caching, fallback prices
- Fallback: Manual price entry option

**3. Orientation Bugs**
- Risk: Layout breaks on orientation change
- Mitigation: Comprehensive testing, manifest config
- Fallback: Force landscape only (remove portrait onboarding)

---

## SUCCESS METRICS

### Analytics KPIs

**Funnel Metrics:**
- Install → Onboarding Start: >80%
- Onboarding Start → Complete: >60%
- Onboarding Complete → Paywall View: >90%
- Paywall View → Trial Start: >25%
- Trial Start → Paid Conversion: >40%

**Retention Metrics:**
- Day 1 retention: >70%
- Day 7 retention: >50%
- Day 30 retention: >30%

**Revenue Metrics:**
- Average Revenue Per User (ARPU): >€2
- Customer Lifetime Value (LTV): >€30
- Payback period: <30 days
- Monthly Recurring Revenue (MRR): Track growth

**Engagement Metrics:**
- Average session length: >5 minutes
- Sessions per user per day: >1.5
- Games completed per session: >2

### Technical Metrics

**Performance:**
- App startup time: <3 seconds
- Crash-free rate: >99.5%
- ANR (Application Not Responding) rate: <0.1%
- Memory usage: <200MB average

**Quality:**
- Store rating: Maintain >4.5 stars
- Critical bugs: <5 open at any time
- Support tickets: <1% of active users

---

## POST-LAUNCH PLAN

### Week 1-2 Post-Launch

1. **Monitor closely:**
   - Check dashboards hourly first day
   - Daily checks for first week
   - Watch for crash spikes
   - Monitor conversion funnel

2. **Quick fixes:**
   - Address critical bugs immediately
   - Hot-fix if crash rate >1%
   - Optimize if funnel drop-off >expected

3. **Gather feedback:**
   - Read all reviews daily
   - Track support tickets
   - Identify common issues
   - Plan fixes for next release

### Week 3-4 Post-Launch

1. **Optimization iteration:**
   - A/B test paywall variations
   - Test onboarding length
   - Optimize demo game order
   - Refine educational messaging

2. **Feature additions:**
   - Add most-requested features
   - Improve existing games
   - Add progress tracking
   - Implement notifications

3. **Marketing optimization:**
   - Analyze attribution data
   - Identify best-performing channels
   - Optimize ad creative
   - Scale successful campaigns

---

## FINAL NOTES FOR CODING AGENT

### Development Approach

1. **Incremental commits:**
   - Commit after each task completion
   - Use descriptive commit messages
   - Format: `type(scope): description`
   - Examples: `feat(onboarding): add welcome screen`, `fix(audio): resolve memory leak`

2. **Testing strategy:**
   - Write tests for critical paths
   - Test on real devices frequently
   - Use Firebase Test Lab for Android
   - TestFlight for iOS testing

3. **Code quality:**
   - Follow Flutter/Dart style guide
   - Use linter, fix all warnings
   - Comment complex logic
   - Keep methods under 50 lines

4. **Error handling:**
   - Wrap risky operations in try-catch
   - Log errors with context
   - Show user-friendly messages
   - Never expose technical errors to users

5. **Configuration management:**
   - Use environment variables for secrets
   - Store API keys in Firebase Remote Config
   - Never commit secrets to git
   - Document all configuration steps

### Dependencies to Add

```yaml
# Add to pubspec.yaml
dependencies:
  # Analytics
  adjust_sdk: ^4.38.0
  purchases_flutter: ^6.29.0

  # Localization (already included in Flutter SDK)
  flutter_localizations:
    sdk: flutter
  intl: ^0.19.0

  # Monitoring
  firebase_crashlytics: ^4.1.3

  # Keep existing dependencies
```

### File Structure Created

```
lib/
├── l10n/
│   ├── app_en.arb
│   ├── app_es.arb
│   └── ... (16 more)
├── models/
│   ├── onboarding_state.dart
│   └── onboarding_progress.dart
├── onboarding/
│   ├── screens/
│   │   ├── welcome_screen.dart
│   │   ├── name_input_screen.dart
│   │   ├── child_age_screen.dart
│   │   ├── educational_philosophy_screen.dart
│   │   ├── orientation_transition_screen.dart
│   │   ├── demo_game_1_screen.dart
│   │   ├── ... (4 more demo games)
│   │   ├── summary_screen.dart
│   │   ├── trust_proof_screen.dart
│   │   ├── paywall_step1_screen.dart
│   │   ├── paywall_step2_screen.dart
│   │   └── paywall_step3_screen.dart
│   ├── widgets/
│   │   └── demo_game_wrapper.dart
│   └── onboarding_coordinator.dart
├── services/
│   ├── analytics/
│   │   ├── adjust_service.dart
│   │   ├── revenuecat_service.dart
│   │   ├── meta_service.dart
│   │   └── analytics_manager.dart
│   ├── subscription/
│   │   └── user_migration_service.dart
│   └── audio/
│       └── audio_state.dart
└── ui/
    ├── screens/
    │   └── subscription_details_screen.dart
    └── widgets/
        └── subscription_badge.dart

docs/
├── ARCHITECTURE.md
├── ANALYTICS_EVENTS.md
├── ANALYTICS_VALIDATION_REPORT.md
├── SUBSCRIPTION_PRODUCTS.md
├── SUBSCRIPTION_TESTING.md
├── LOCALIZATION.md
├── TRANSLATION_GUIDELINES.md
├── TESTING_CHECKLIST.md
├── MONITORING.md
├── SUBSCRIPTION_MANAGEMENT.md
├── ADDING_GAMES.md
├── LOCALIZATION_PROCESS.md
└── INCIDENT_RESPONSE.md
```

This implementation plan provides comprehensive instructions for transforming Brainy Bunny into a conversion-optimized educational app with proper analytics, subscriptions, and onboarding. Follow the phases sequentially for best results.
// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Spanish Castilian (`es`).
class AppLocalizationsEs extends AppLocalizations {
  AppLocalizationsEs([String locale = 'es']) : super(locale);

  @override
  String get onboarding_welcome_headline =>
      'Ayuda a tu hijo a aprender y crecer';

  @override
  String get onboarding_welcome_subheading =>
      'Juegos educativos diseñados para niños de 2 a 5 años';

  @override
  String get onboarding_welcome_cta => 'Comenzar';

  @override
  String get onboarding_name_headline => 'Personalicemos tu experiencia';

  @override
  String get onboarding_name_hint => 'Tu nombre';

  @override
  String get onboarding_name_mom => 'Mam<PERSON>';

  @override
  String get onboarding_name_dad => 'Papá';

  @override
  String get onboarding_name_parent => 'Padre/Madre';

  @override
  String onboarding_name_greeting(String name) {
    return '¡Genial, $name!';
  }

  @override
  String get onboarding_child_age_headline => '¿Cuántos años tiene tu hijo?';

  @override
  String get onboarding_child_age_subtext =>
      'Todos los juegos funcionan para edades de 2-5 años. Esto nos ayuda a proporcionar consejos apropiados para la edad.';

  @override
  String get onboarding_age_2 => '2 años';

  @override
  String get onboarding_age_3 => '3 años';

  @override
  String get onboarding_age_4 => '4 años';

  @override
  String get onboarding_age_5_plus => '5+ años';

  @override
  String get onboarding_philosophy_headline =>
      'Transforma el tiempo de pantalla en tiempo de aprendizaje';

  @override
  String get onboarding_philosophy_aap =>
      'Alineado con las recomendaciones de la AAP sobre tiempo de pantalla';

  @override
  String get onboarding_philosophy_learning =>
      'Convierte el tiempo de pantalla en experiencias de aprendizaje significativas';

  @override
  String get onboarding_philosophy_skills =>
      'Desarrolla habilidades cognitivas a través del aprendizaje basado en el juego';

  @override
  String get onboarding_transition_to_games =>
      '¡Vamos a jugar! Gira tu dispositivo →';

  @override
  String get onboarding_transition_from_games =>
      '¡Excelente aprendizaje! Terminemos →';

  @override
  String get onboarding_solution_headline => 'Learning through play';

  @override
  String get onboarding_solution_description =>
      'While they match shapes and colors, they\'re building real cognitive skills.';

  @override
  String get onboarding_solution_benefit_engagement => 'Active engagement';

  @override
  String get onboarding_solution_benefit_pattern_recognition =>
      'Pattern recognition';

  @override
  String get onboarding_solution_benefit_cause_effect =>
      'Cause-and-effect thinking';

  @override
  String get onboarding_solution_benefit_screen_time =>
      'Aligned with 1-hour screen time guidelines';

  @override
  String get onboarding_solution_research_text =>
      'Research shows: Matching activities improve spatial reasoning...';

  @override
  String get onboarding_solution_research_source =>
      'Based on developmental psychology studies';

  @override
  String get onboarding_solution_cta => 'See how it works';

  @override
  String get onboarding_problem_headline =>
      'We understand your\nscreen time concerns';

  @override
  String get onboarding_problem_point_1_title => 'Passive video consumption';

  @override
  String get onboarding_problem_point_1_description =>
      'Hours of mindless watching with zero interaction or learning';

  @override
  String get onboarding_problem_point_1_statistic =>
      '6x higher risk of language delays';

  @override
  String get onboarding_problem_point_2_title => 'Brain development damage';

  @override
  String get onboarding_problem_point_2_description =>
      'Excessive screen time alters white matter structure in developing brains';

  @override
  String get onboarding_problem_point_2_statistic =>
      'Reduced cognitive abilities';

  @override
  String get onboarding_problem_point_3_title =>
      'Inappropriate content exposure';

  @override
  String get onboarding_problem_point_3_description =>
      'Ads, violence, and age-inappropriate material in \'kids\' content';

  @override
  String get onboarding_problem_point_3_statistic =>
      '85% of kids\' apps contain ads';

  @override
  String get onboarding_problem_point_4_title => 'Attention & focus problems';

  @override
  String get onboarding_problem_point_4_description =>
      'Fast-paced content destroys ability to concentrate and learn';

  @override
  String get onboarding_problem_point_4_statistic =>
      '40% increase in ADHD symptoms';

  @override
  String get onboarding_problem_research_title => 'Scientific Evidence';

  @override
  String get onboarding_problem_research_text =>
      'Peer-reviewed studies confirm: excessive passive screen time causes brain changes, language delays, and attention problems in young children.';

  @override
  String get onboarding_problem_subtext =>
      'You\'re not alone. 89% of parents share these concerns.';

  @override
  String get onboarding_problem_cta => 'There\'s a better way';

  @override
  String get demo_game_1_skill =>
      'Emparejamiento visual y reconocimiento de formas';

  @override
  String get demo_game_1_science =>
      'Emparejar animales con sus siluetas desarrolla la discriminación visual, la capacidad de notar diferencias entre formas similares. Esta habilidad es fundamental para el reconocimiento de letras y la lectura.';

  @override
  String get demo_game_1_citation =>
      'Bornstein (1985) - Procesamiento visual y pensamiento categórico';

  @override
  String get demo_game_1_badge => 'Detective de formas';

  @override
  String get demo_game_2_skill => 'Memoria visual y atención';

  @override
  String get demo_game_2_science =>
      'Encontrar parejas fortalece la memoria de trabajo, la capacidad de tu hijo para retener y manipular información. Esto apoya directamente la resolución de problemas matemáticos y seguir instrucciones de varios pasos.';

  @override
  String get demo_game_2_citation =>
      'Gopnik & Meltzoff (1987) - Categorización y flexibilidad cognitiva';

  @override
  String get demo_game_2_badge => 'Maestro de la memoria';

  @override
  String get demo_game_3_skill => 'Asociación lógica y categorización';

  @override
  String get demo_game_3_science =>
      'Conectar objetos con sus usuarios enseña categorización y pensamiento lógico. Tu hijo aprende que las cosas van juntas por razones, un paso clave para comprender causa y efecto.';

  @override
  String get demo_game_3_citation =>
      'Piaget (1952) - Desarrollo cognitivo preoperacional';

  @override
  String get demo_game_3_badge => 'Estrella de la lógica';

  @override
  String get demo_game_4_skill => 'Reconocimiento de patrones y emparejamiento';

  @override
  String get demo_game_4_science =>
      'Emparejar patrones desarrolla el reconocimiento de patrones, la capacidad de ver relaciones entre las cosas. Esta habilidad predice fuertemente el éxito en matemáticas y ayuda a los niños a entender \'igual\' versus \'diferente\'.';

  @override
  String get demo_game_4_citation =>
      'Rittle-Johnson et al. (2019) - Habilidades de patrones y matemáticas';

  @override
  String get demo_game_4_badge => 'Profesional de patrones';

  @override
  String get demo_game_5_skill =>
      'Pensamiento simbólico y conexiones con el mundo real';

  @override
  String get demo_game_5_science =>
      'Conectar herramientas con carreras construye pensamiento simbólico, entender que una cosa puede representar otra. Este pensamiento abstracto es esencial para el lenguaje, las matemáticas y la imaginación.';

  @override
  String get demo_game_5_citation =>
      'Vygotsky (1978) - Representación simbólica en el desarrollo cognitivo';

  @override
  String get demo_game_5_badge => 'Explorador del mundo';

  @override
  String get demo_game_next => 'Siguiente juego';

  @override
  String onboarding_summary_headline(String name) {
    return 'Tu progreso hasta ahora, $name';
  }

  @override
  String onboarding_summary_age(int age) {
    return 'Perfecto para niños de $age años';
  }

  @override
  String get onboarding_summary_skills => '5 habilidades practicadas';

  @override
  String get onboarding_summary_screen_time =>
      'Enfoque de tiempo de pantalla saludable alineado con las pautas de la AAP';

  @override
  String get onboarding_summary_cta => 'Ver tu plan personalizado';

  @override
  String get trust_headline => 'Confianza de padres y educadores';

  @override
  String get trust_approved => 'Aprobado por profesores';

  @override
  String get trust_cta => 'Desbloquea la experiencia completa de aprendizaje';

  @override
  String get paywall_headline => 'Desbloquea los 15 juegos de aprendizaje';

  @override
  String get paywall_subheadline =>
      'Continúa el viaje de aprendizaje de tu hijo';

  @override
  String get paywall_feature_games =>
      '15 juegos educativos dirigidos a habilidades clave';

  @override
  String get paywall_feature_progress => 'Seguimiento del progreso de tu hijo';

  @override
  String get paywall_feature_ad_free => 'Entorno seguro y sin publicidad';

  @override
  String get paywall_feature_new_games => 'Nuevos juegos añadidos mensualmente';

  @override
  String get paywall_feature_ages => 'Diseñado para edades de 2 a 5 años';

  @override
  String get paywall_trial_headline =>
      'Prueba todas las funciones GRATIS durante 7 días';

  @override
  String get paywall_trial_price => 'Luego solo 0,87 €/semana';

  @override
  String get paywall_trial_today => 'Hoy: Acceso completo desbloqueado';

  @override
  String get paywall_trial_day5 => 'Día 5: Te enviaremos un recordatorio';

  @override
  String get paywall_trial_day7 =>
      'Día 7: Comienza la facturación (cancela en cualquier momento antes)';

  @override
  String get paywall_trial_no_payment => '✓ Sin pago inmediato';

  @override
  String get paywall_trial_cta => 'Comenzar prueba gratuita →';

  @override
  String get paywall_weekly => 'Semanal';

  @override
  String get paywall_monthly => 'Mensual';

  @override
  String get paywall_yearly => 'Anual';

  @override
  String get paywall_save_60 => 'Ahorra 60%';

  @override
  String get paywall_most_popular => 'Opción más popular';

  @override
  String get paywall_cancel_anytime =>
      'Cancela en cualquier momento desde la configuración de tu dispositivo';

  @override
  String get paywall_restore => 'Restaurar compras';

  @override
  String get subscription_premium => 'Premium';

  @override
  String get subscription_trial => 'Prueba de 7 días';

  @override
  String get subscription_lifetime => 'Acceso de por vida';

  @override
  String get subscription_expired => 'Suscripción finalizada';

  @override
  String get subscription_manage => 'Gestionar suscripción';

  @override
  String get subscription_cancel => 'Cancelar suscripción';

  @override
  String get subscription_change_plan => 'Cambiar plan';

  @override
  String get continue_button => 'Continuar';

  @override
  String get skip_button => 'Saltar';

  @override
  String get close_button => 'Cerrar';

  @override
  String get loading => 'Cargando...';

  @override
  String get error_purchase_failed => 'Compra fallida';

  @override
  String get error_purchase_failed_message =>
      'No pudimos completar tu compra. Por favor, inténtalo de nuevo.';

  @override
  String get error_restore_failed => 'No se encontraron compras';

  @override
  String get error_restore_failed_message =>
      'No pudimos encontrar compras anteriores. Si crees que esto es un error, por favor contacta con soporte.';

  @override
  String get error_network => 'Error de red';

  @override
  String get error_network_message =>
      'Por favor, verifica tu conexión a internet e inténtalo de nuevo.';

  @override
  String get summary_headline => '¡Progreso increíble!';

  @override
  String summary_message_age_2(Object name) {
    return '¡Buen trabajo, $name! Tu pequeño está construyendo habilidades importantes a través del juego. A los 2 años, cada emparejamiento que hacen fortalece su reconocimiento visual y habilidades de resolución de problemas.';
  }

  @override
  String summary_message_age_3(Object name) {
    return '¡Maravilloso, $name! Tu hijo está desarrollando habilidades cognitivas cruciales. A los 3 años, estas actividades mejoran su memoria, concentración y pensamiento lógico.';
  }

  @override
  String summary_message_age_4(Object name) {
    return '¡Excelente trabajo, $name! Tu hijo de 4 años está dominando la resolución avanzada de problemas. Estos juegos lo están preparando para el éxito en el jardín de infancia.';
  }

  @override
  String summary_message_age_5_plus(Object name) {
    return '¡Fantástico, $name! Tu hijo está sobresaliendo en el pensamiento complejo. Estas habilidades le darán una base sólida para la escuela.';
  }

  @override
  String get summary_badges_earned => 'Insignias obtenidas';

  @override
  String get summary_badges => 'Insignias';

  @override
  String get summary_games => 'Juegos';

  @override
  String get summary_skills => 'Habilidades';

  @override
  String get trust_aap_description =>
      'Nuestro enfoque educativo se alinea con las pautas de la AAP para el tiempo de pantalla saludable y el desarrollo infantil temprano.';

  @override
  String get trust_research_title => 'Currículum respaldado por investigación';

  @override
  String get trust_research_description =>
      'Cada juego está diseñado basándose en estudios revisados por pares sobre desarrollo cognitivo, con métodos probados para mejorar el aprendizaje en niños de 2 a 5 años.';

  @override
  String get trust_testimonial_1_name => 'Sarah M., Madre de niño de 3 años';

  @override
  String get trust_testimonial_1_quote =>
      '\"Mi hija pasó de tener dificultades con las formas a identificarlas con confianza en todas partes. ¡El progreso en solo 2 semanas me sorprendió!\"';

  @override
  String get trust_testimonial_2_name => 'Michael T., Padre de niño de 4 años';

  @override
  String get trust_testimonial_2_quote =>
      '\"¡Finalmente, tiempo de pantalla con el que me siento bien! Mi hijo está aprendiendo mientras se divierte, y puedo ver mejoras cognitivas reales.\"';

  @override
  String get trust_downloads_title => 'Únete a más de 100,000 familias';

  @override
  String get trust_downloads_description =>
      'Confiado por padres en más de 50 países para dar a sus hijos una ventaja en el aprendizaje.';

  @override
  String get trust_cta_headline =>
      '¿Listo para desbloquear el potencial completo de tu hijo?';

  @override
  String get trust_cta_button => 'Comenzar prueba gratuita';

  @override
  String get paywall_premium_badge => 'Acceso Premium';

  @override
  String get paywall_step1_headline =>
      'Desbloquea el potencial completo de tu hijo';

  @override
  String get paywall_value_1_title => '15 juegos premium';

  @override
  String get paywall_value_1_description =>
      'Accede a todos los juegos educativos diseñados para edades de 2 a 5 años, que cubren formas, colores, números, lógica y más';

  @override
  String get paywall_value_2_title => 'Seguimiento del progreso';

  @override
  String get paywall_value_2_description =>
      'Análisis detallados que muestran el desarrollo de tu hijo y la mejora de habilidades a lo largo del tiempo';

  @override
  String get paywall_value_3_title => 'Aprendizaje personalizado';

  @override
  String get paywall_value_3_description =>
      'Los juegos se adaptan a la edad y nivel de habilidad de tu hijo para un aprendizaje óptimo';

  @override
  String get paywall_value_4_title => 'Contenido nuevo mensualmente';

  @override
  String get paywall_value_4_description =>
      'Actualizaciones regulares con nuevos juegos y actividades para mantener el aprendizaje fresco y atractivo';

  @override
  String get paywall_step1_cta => 'Ver planes';

  @override
  String get paywall_secure_payment => 'Procesamiento de pago seguro';

  @override
  String get paywall_trial_badge => 'Prueba gratuita de 7 días';

  @override
  String get paywall_step2_headline => 'Elige tu plan';

  @override
  String get paywall_step2_subheadline =>
      'Comienza tu prueba gratuita de 7 días. Cancela en cualquier momento.';

  @override
  String get paywall_plan_best_value => 'Mejor valor';

  @override
  String get paywall_plan_yearly_title => 'Anual';

  @override
  String get paywall_plan_yearly_period => '/año';

  @override
  String get paywall_plan_yearly_per_month => 'Solo 3,33 €/mes';

  @override
  String get paywall_plan_yearly_savings =>
      'Ahorra 63% en comparación con mensual';

  @override
  String get paywall_plan_monthly_title => 'Mensual';

  @override
  String get paywall_plan_monthly_period => '/mes';

  @override
  String get paywall_plan_weekly_title => 'Semanal';

  @override
  String get paywall_plan_weekly_period => '/semana';

  @override
  String get paywall_plan_weekly_note => 'Para acceso a corto plazo';

  @override
  String get paywall_trial_reminder =>
      'Tu prueba gratuita comienza hoy. No se te cobrará hasta el día 8. Cancela en cualquier momento antes sin costo.';

  @override
  String get paywall_step2_cta => 'Continuar';

  @override
  String get paywall_terms =>
      'Al continuar, aceptas nuestros Términos de servicio y Política de privacidad';

  @override
  String get paywall_urgency_text => 'Oferta por tiempo limitado';

  @override
  String get paywall_step3_headline => '¡Estás a un paso!';

  @override
  String get paywall_step3_included_title => 'Todo lo que obtienes:';

  @override
  String get paywall_included_1 => 'Los 15 juegos educativos premium';

  @override
  String get paywall_included_2 =>
      'Rutas de aprendizaje personalizadas para tu hijo';

  @override
  String get paywall_included_3 =>
      'Seguimiento detallado del progreso e información';

  @override
  String get paywall_included_4 => 'Contenido y actividades nuevos mensuales';

  @override
  String get paywall_included_5 =>
      'Experiencia sin publicidad para un aprendizaje enfocado';

  @override
  String get paywall_included_6 =>
      'Modo sin conexión - aprende en cualquier lugar, en cualquier momento';

  @override
  String get paywall_guarantee_title => 'Garantía 100% sin riesgo';

  @override
  String get paywall_guarantee_text =>
      'Pruébalo gratis durante 7 días. Si no estás completamente satisfecho, cancela antes de que finalice la prueba y no pagues nada. Sin preguntas.';

  @override
  String get paywall_step3_cta => 'Comenzar mi prueba gratuita';

  @override
  String get pre_paywall_headline => 'Your Learning Journey is Ready!';

  @override
  String pre_paywall_subheadline_personalized(String name) {
    return 'Here\'s what we\'ve prepared for $name:';
  }

  @override
  String pre_paywall_subheadline_age(String age) {
    return 'Here\'s what we\'ve prepared for your $age-year-old:';
  }

  @override
  String get pre_paywall_subheadline_generic =>
      'Here\'s what we\'ve prepared for your child:';

  @override
  String get pre_paywall_card_1_title => 'Age-Appropriate Content';

  @override
  String pre_paywall_card_1_subtitle_age(String age) {
    return 'Perfect for $age years old';
  }

  @override
  String get pre_paywall_card_1_subtitle_generic =>
      'All 15 games work great for ages 2-5';

  @override
  String get pre_paywall_card_2_title => 'Learning Focus';

  @override
  String get pre_paywall_card_2_subtitle => 'Building skills through play';

  @override
  String get pre_paywall_card_3_title => '15 Educational Games';

  @override
  String get pre_paywall_card_3_subtitle =>
      'Shapes, colors, patterns, animals & more';

  @override
  String get pre_paywall_key_benefit =>
      'Transform screen time from guilt into growth—educational content you can feel good about.';

  @override
  String get pre_paywall_trust_1 => 'Teacher Approved';

  @override
  String get pre_paywall_trust_2 => 'Ad-Free';

  @override
  String get pre_paywall_trust_3 => 'Expert-Backed';

  @override
  String get pre_paywall_cta_primary => 'Start 7-Day FREE Trial';

  @override
  String get pre_paywall_cta_primary_subtext =>
      'Unlock all 15 games • No charge today';

  @override
  String get pre_paywall_cta_secondary => 'Continue with 5 free games';

  @override
  String get pre_paywall_important_note =>
      'Trial includes all 15 games. After trial: Keep 5 free games or subscribe for full access.';

  @override
  String get onboarding_rotate_to_landscape =>
      'Gira tu dispositivo a horizontal';

  @override
  String get onboarding_rotate_to_portrait => 'Gira tu dispositivo a vertical';

  @override
  String get demo_game_1_title => 'Formas de animales';

  @override
  String get demo_game_1_context =>
      '¡Ayuda a tu hijo a emparejar animales con sus formas! Esto desarrolla habilidades de reconocimiento visual esenciales para la lectura.';

  @override
  String get demo_game_2_title => 'Emparejamiento de memoria';

  @override
  String get demo_game_2_context =>
      'Encuentra parejas para fortalecer la memoria de trabajo, crucial para seguir instrucciones y resolver problemas.';

  @override
  String get demo_game_3_title => 'Rompecabezas de lógica';

  @override
  String get demo_game_3_context =>
      'Resuelve rompecabezas para desarrollar habilidades de pensamiento lógico y reconocimiento de patrones.';

  @override
  String get demo_game_4_title => 'Diversión con patrones';

  @override
  String get demo_game_4_context =>
      'Reconoce patrones para desarrollar preparación matemática y habilidades de secuenciación.';

  @override
  String get demo_game_5_title => 'Explorador del mundo';

  @override
  String get demo_game_5_context =>
      'Explora el mundo para ampliar el vocabulario y la conciencia cultural.';

  @override
  String get demo_game_congratulations => '¡Increíble! ¡Ganaste una insignia!';

  @override
  String paywall_benefit_all_games(int gameCount) {
    return 'Acceso a todos los $gameCount juegos';
  }

  @override
  String get paywall_benefit_age_appropriate =>
      'Contenido apropiado para la edad';

  @override
  String get paywall_benefit_progress_tracking => 'Seguimiento del progreso';

  @override
  String get paywall_benefit_offline_play => 'Modo sin conexión compatible';

  @override
  String get paywall_benefit_no_ads => 'Sin anuncios, seguro para niños';

  @override
  String get paywall_benefit_regular_updates => 'Actualizaciones regulares';

  @override
  String get paywall_start_trial => 'Comenzar prueba gratuita';

  @override
  String get paywall_step3_benefit_1 =>
      'Acceso completo a todos los juegos de aprendizaje';

  @override
  String get paywall_step3_benefit_2 =>
      'Entorno de aprendizaje seguro y sin publicidad';

  @override
  String get paywall_step3_benefit_3 => 'Perfecto para toda la familia';

  @override
  String get paywall_subscribe_button => 'Suscribirse ahora';

  @override
  String get trial_explanation_headline => 'Try All 15 Games Free for 7 Days';

  @override
  String get trial_explanation_feature_1 =>
      'Full access to all 15 educational games';

  @override
  String get trial_explanation_feature_2 => 'No charge for 7 days';

  @override
  String get trial_explanation_feature_3 =>
      'We\'ll remind you 2 days before trial ends';

  @override
  String get trial_explanation_feature_4 =>
      'Cancel anytime during trial - no cost';

  @override
  String get trial_explanation_subtext =>
      'You won\'t be charged until day 8 of your trial';

  @override
  String get trial_explanation_cta => 'See Plans';

  @override
  String get unified_paywall_headline => 'Choose Your Plan';

  @override
  String get unified_paywall_subheadline =>
      'All plans unlock 15 educational games';

  @override
  String get unified_paywall_yearly_badge_save => 'Save 60%';

  @override
  String get unified_paywall_yearly_badge_trial => '7-Day Free Trial';

  @override
  String get unified_paywall_yearly_title => 'Yearly';

  @override
  String unified_paywall_yearly_price_breakdown(String monthlyEquivalent) {
    return 'Just $monthlyEquivalent/month';
  }

  @override
  String get unified_paywall_yearly_savings => 'Save 63% • Try free for 7 days';

  @override
  String get unified_paywall_yearly_feature_1 => 'No charge for 7 days';

  @override
  String get unified_paywall_yearly_feature_2 => 'Cancel anytime during trial';

  @override
  String get unified_paywall_yearly_feature_3 => 'Full access to all 15 games';

  @override
  String get unified_paywall_yearly_button => 'Start Free Trial';

  @override
  String get unified_paywall_monthly_title => 'Monthly';

  @override
  String unified_paywall_monthly_per_week(String weeklyEquivalent) {
    return '$weeklyEquivalent/week';
  }

  @override
  String get unified_paywall_monthly_savings => 'Flexible monthly plan';

  @override
  String get unified_paywall_monthly_feature_1 => 'Cancel anytime';

  @override
  String get unified_paywall_monthly_feature_2 => 'Full access to all 15 games';

  @override
  String get unified_paywall_monthly_button => 'Subscribe Monthly';

  @override
  String get unified_paywall_weekly_title => 'Weekly';

  @override
  String get unified_paywall_weekly_savings => 'Try for just one week';

  @override
  String get unified_paywall_weekly_feature_1 => 'Cancel anytime';

  @override
  String get unified_paywall_weekly_feature_2 => 'Full access to all 15 games';

  @override
  String get unified_paywall_weekly_button => 'Subscribe Weekly';

  @override
  String get unified_paywall_trust_1 => 'Secure payment processing';

  @override
  String get unified_paywall_trust_2 => 'Manage in App/Play Store';

  @override
  String get unified_paywall_trust_3 => 'All plans include full access';

  @override
  String get unified_paywall_restore => 'Restore Purchases';

  @override
  String get unified_paywall_terms =>
      'By continuing, you agree to our Terms of Service and Privacy Policy';

  @override
  String get educational_value_headline =>
      'Aprendizaje a través del juego, no entretenimiento sin sentido';

  @override
  String get educational_value_point_1_title =>
      'Desarrolla el reconocimiento de patrones y el pensamiento lógico';

  @override
  String get educational_value_point_1_description =>
      'Habilidades esenciales para la preparación matemática y la resolución de problemas';

  @override
  String get educational_value_point_2_title =>
      'Fortalece las habilidades de discriminación visual';

  @override
  String get educational_value_point_2_description =>
      'Ayuda a los niños a identificar diferencias y similitudes';

  @override
  String get educational_value_point_3_title =>
      'Desarrolla habilidades de resolución de problemas';

  @override
  String get educational_value_point_3_description =>
      'El compromiso activo desarrolla el pensamiento crítico';

  @override
  String get educational_value_point_4_title =>
      'Diseñado para un tiempo de pantalla saludable';

  @override
  String get educational_value_point_4_description =>
      'Alineado con la recomendación pediátrica de 1 hora diaria';

  @override
  String get educational_value_research =>
      'La investigación muestra: Las actividades de emparejamiento mejoran el razonamiento espacial y el desarrollo cognitivo en niños de 2-5 años.';

  @override
  String get educational_value_research_source =>
      'Basado en estudios de psicología del desarrollo';

  @override
  String get value_carousel_1_headline =>
      'Tiempo de pantalla que desarrolla habilidades';

  @override
  String get value_carousel_1_description =>
      '15 juegos de emparejamiento diferentes que enseñan formas, colores, patrones, animales, profesiones y pensamiento de causa y efecto.';

  @override
  String get value_carousel_1_subtext =>
      'No solo entretenimiento: aprendizaje real en cada emparejamiento.';

  @override
  String get value_carousel_1_benefit_1_title => 'Desarrollo de la memoria';

  @override
  String get value_carousel_1_benefit_1_description =>
      'Juegos de emparejamiento que fortalecen el recuerdo y el reconocimiento';

  @override
  String get value_carousel_1_benefit_2_title => 'Resolución de problemas';

  @override
  String get value_carousel_1_benefit_2_description =>
      'Rompecabezas que fomentan el pensamiento lógico y la estrategia';

  @override
  String get value_carousel_1_benefit_3_title => 'Aprendizaje progresivo';

  @override
  String get value_carousel_1_benefit_3_description =>
      'La dificultad se adapta a medida que tu hijo domina nuevas habilidades';

  @override
  String get value_carousel_2_headline => '15 formas de aprender y crecer';

  @override
  String get value_carousel_2_description =>
      'Desde la simple coincidencia de formas hasta la comprensión de relaciones entre objetos y situaciones: cada juego se dirige a habilidades de desarrollo específicas.';

  @override
  String get value_carousel_2_category_1 => 'Formas y geometría';

  @override
  String get value_carousel_2_category_2 => 'Colores y patrones';

  @override
  String get value_carousel_2_category_3 => 'Animales y naturaleza';

  @override
  String get value_carousel_2_category_4 => 'Profesiones y roles';

  @override
  String get value_carousel_2_category_5 => 'Causa y efecto';

  @override
  String get value_carousel_2_category_6 => 'Y más...';

  @override
  String get value_carousel_2_feature_1_title => '100% sin anuncios';

  @override
  String get value_carousel_2_feature_1_description =>
      'Sin anuncios, sin distracciones, sin contenido inapropiado';

  @override
  String get value_carousel_2_feature_2_title => 'Apropiado para la edad';

  @override
  String get value_carousel_2_feature_2_description =>
      'Contenido diseñado específicamente para edades de 2-5 años';

  @override
  String get value_carousel_2_feature_3_title => 'Aprendizaje activo';

  @override
  String get value_carousel_2_feature_3_description =>
      'Actividades atractivas, no visualización pasiva';

  @override
  String get value_carousel_3_headline =>
      'Aprobado por maestros, confiable para padres';

  @override
  String get value_carousel_3_trust_element_1 =>
      'Entorno de aprendizaje sin anuncios';

  @override
  String get value_carousel_3_trust_element_2 => 'Sin recopilación de datos';

  @override
  String get value_carousel_3_trust_element_3 => 'Diseñado por educadores';

  @override
  String get value_carousel_3_trust_element_4 => 'Seguro para niños pequeños';

  @override
  String get value_carousel_3_feature_1_title => 'Seguimiento del progreso';

  @override
  String get value_carousel_3_feature_1_description =>
      'Vea qué habilidades está desarrollando su hijo';

  @override
  String get value_carousel_3_feature_2_title => 'Recompensas de logros';

  @override
  String get value_carousel_3_feature_2_description =>
      'Gane estrellas e insignias que motivan el aprendizaje continuo';

  @override
  String get value_carousel_3_feature_3_title => 'Experiencia personalizada';

  @override
  String get value_carousel_3_feature_3_description =>
      'Juegos que se adaptan al nivel de habilidad de su hijo';

  @override
  String get summary_headline_new =>
      '¡Perfecto! Aquí está tu ruta de aprendizaje personalizada';

  @override
  String get summary_learning_path_title => 'Lo que aprenderá tu hijo:';

  @override
  String get summary_skill_cognitive => 'Desarrollo cognitivo';

  @override
  String summary_skill_cognitive_desc(int age) {
    return 'Juegos de memoria y actividades de resolución de problemas perfectos para la edad de $age años';
  }

  @override
  String get summary_skill_visual => 'Percepción visual';

  @override
  String summary_skill_visual_desc(int age) {
    return 'Juegos de reconocimiento de formas y conciencia espacial para niños de $age años';
  }

  @override
  String get summary_skill_exploration => 'Exploración y descubrimiento';

  @override
  String summary_skill_exploration_desc(int age) {
    return 'Juegos interactivos que fomentan la curiosidad a los $age años';
  }

  @override
  String get summary_next_step =>
      'Siguiente: ¡Prueba Premium gratis durante 7 días para desbloquear todos los juegos!';

  @override
  String get trial_badge => 'Prueba gratuita de 7 días';

  @override
  String get trial_headline => 'Prueba Premium gratis durante 7 días';

  @override
  String get trial_description =>
      'Obtén acceso completo a todos los juegos y funciones premium. Cancela en cualquier momento durante tu prueba, sin cargos si cancelas antes de que finalice.';

  @override
  String get trial_feature_1_title => 'Todos los juegos premium';

  @override
  String get trial_feature_1_description =>
      'Accede a todos los juegos de aprendizaje de nuestra biblioteca';

  @override
  String get trial_feature_2_title => 'Experiencia sin publicidad';

  @override
  String get trial_feature_2_description =>
      'Entorno de aprendizaje seguro sin anuncios';

  @override
  String get trial_feature_3_title => 'Seguimiento del progreso';

  @override
  String get trial_feature_3_description =>
      'Ve el desarrollo y los logros de tu hijo';

  @override
  String get trial_feature_4_title => 'Actualizaciones regulares';

  @override
  String get trial_feature_4_description =>
      'Nuevos juegos y funciones añadidos regularmente';

  @override
  String get trial_how_it_works_title => 'Cómo funciona:';

  @override
  String get trial_step_1 => 'Comienza tu prueba gratuita de 7 días hoy';

  @override
  String get trial_step_2 =>
      'Disfruta de acceso completo a todas las funciones premium';

  @override
  String get trial_step_3 =>
      'Cancela en cualquier momento, sin cargos antes de que finalice la prueba';

  @override
  String get trial_cta => 'Comenzar mi prueba gratuita';

  @override
  String get trial_disclaimer =>
      'Gratis durante 7 días, luego la tarifa del plan seleccionado. Cancela en cualquier momento.';

  @override
  String get notification_permission_headline =>
      'Mantente conectado al aprendizaje de tu hijo';

  @override
  String get notification_permission_description =>
      'Recibe recordatorios útiles y celebra hitos con tu hijo. Te enviaremos notificaciones oportunas sobre logros y oportunidades de aprendizaje.';

  @override
  String get notification_benefit_1_title => 'Recordatorios de prueba';

  @override
  String get notification_benefit_1_description =>
      'Recibe notificaciones antes de que finalice tu prueba para que nunca pierdas el acceso';

  @override
  String get notification_benefit_2_title => 'Hitos de aprendizaje';

  @override
  String get notification_benefit_2_description =>
      'Celebra cuando tu hijo alcance nuevos logros';

  @override
  String get notification_benefit_3_title => 'Consejos de participación';

  @override
  String get notification_benefit_3_description =>
      'Obtén sugerencias para mantener el aprendizaje divertido y atractivo';

  @override
  String get notification_privacy_note =>
      'Respetamos tu privacidad. Puedes desactivar las notificaciones en cualquier momento en la configuración.';

  @override
  String get notification_enable_button => 'Activar notificaciones';

  @override
  String get notification_maybe_later => 'Quizás más tarde';

  @override
  String get subscription_management_title => 'Gestionar suscripción';

  @override
  String get subscription_status_active => 'Premium activo';

  @override
  String get subscription_status_active_description =>
      'Tienes acceso completo a todas las funciones premium';

  @override
  String get subscription_status_inactive => 'Versión gratuita';

  @override
  String get subscription_status_inactive_description =>
      'Actualiza a premium para acceso completo a todos los juegos';

  @override
  String get subscription_actions_title => 'Acciones';

  @override
  String get subscription_restore_title => 'Restaurar compras';

  @override
  String get subscription_restore_description =>
      '¿Ya estás suscrito en otro dispositivo? Restaura tus compras aquí.';

  @override
  String get subscription_restore_button => 'Restaurar';

  @override
  String get subscription_manage_title => 'Gestiona tu suscripción';

  @override
  String get subscription_manage_description =>
      'Ver, cambiar o cancelar tu suscripción a través de tu cuenta de la tienda de aplicaciones.';

  @override
  String get subscription_manage_button => 'Abrir configuración de suscripción';

  @override
  String get subscription_help_title => 'Ayuda e información';

  @override
  String get subscription_cancel_title => 'Cómo cancelar';

  @override
  String get subscription_cancel_description =>
      'Puedes cancelar tu suscripción en cualquier momento a través de la configuración de tu cuenta de App Store o Google Play. Mantendrás el acceso hasta el final de tu período de facturación.';

  @override
  String get subscription_payment_failure_title => 'Problemas de pago';

  @override
  String get subscription_payment_failure_description =>
      'Si tu pago falla, actualiza tu método de pago en tu cuenta de la tienda de aplicaciones. Reintentaremos el pago automáticamente.';

  @override
  String get next_button => 'Siguiente';

  @override
  String get back_button => 'Atrás';

  @override
  String get onboarding_priority_question =>
      '¿Qué es lo más importante para ti?';

  @override
  String get onboarding_priority_1 => 'Tiempo de pantalla educativo';

  @override
  String get onboarding_priority_1_sub => 'Aprender mientras juegan';

  @override
  String get onboarding_priority_2 => 'Preparación escolar';

  @override
  String get onboarding_priority_2_sub =>
      'Preparación para preescolar/jardín de infancia';

  @override
  String get onboarding_priority_3 => 'Mantenerlos comprometidos';

  @override
  String get onboarding_priority_3_sub =>
      'Tiempo de pantalla productivo y feliz';

  @override
  String get onboarding_priority_4 => 'Aprender a través del juego';

  @override
  String get onboarding_priority_4_sub =>
      'Diversión que desarrolla habilidades';

  @override
  String get onboarding_transition_message => '¡Gracias por compartir!';

  @override
  String onboarding_transition_submessage(int age) {
    return 'Estamos creando la experiencia perfecta para tu hijo de $age años...';
  }

  @override
  String summary_result_headline(int age) {
    return '¡Perfecto para tu hijo de $age años!';
  }

  @override
  String get summary_card_1_title => 'Lo que aprenderán:';

  @override
  String get summary_card_1_point_1 =>
      'Reconocimiento de patrones a través de 15 juegos diferentes';

  @override
  String get summary_card_1_point_2 => 'Habilidades de discriminación visual';

  @override
  String get summary_card_1_point_3 =>
      'Pensamiento lógico y resolución de problemas';

  @override
  String get summary_card_1_point_4 =>
      'Conexiones del mundo real (animales, profesiones, naturaleza)';

  @override
  String get summary_card_2_title => 'Por qué funciona:';

  @override
  String get summary_card_2_point_1 =>
      'El aprendizaje activo supera la visualización pasiva';

  @override
  String get summary_card_2_point_2 =>
      'La retroalimentación inmediata los mantiene comprometidos';

  @override
  String get summary_card_2_point_3 => 'La variedad previene el aburrimiento';

  @override
  String get summary_card_2_point_4 =>
      'Alineado con las pautas de tiempo de pantalla';

  @override
  String get summary_cta => 'Ver qué está incluido';

  @override
  String get free_trial_headline => 'Prueba 5 juegos gratis, desbloquea 10 más';

  @override
  String get free_trial_free_section =>
      'Comienza con 5 juegos de emparejamiento gratuitos';

  @override
  String get free_trial_free_point_1 => 'Formas, colores y más';

  @override
  String get free_trial_free_point_2 => 'Sin límite de tiempo';

  @override
  String get free_trial_free_point_3 => 'No se requiere tarjeta de crédito';

  @override
  String get free_trial_premium_section =>
      'Desbloquea los 15 juegos con una suscripción';

  @override
  String get free_trial_premium_point_1 => 'Biblioteca completa de juegos';

  @override
  String get free_trial_premium_point_2 => 'Nuevas categorías de aprendizaje';

  @override
  String get free_trial_premium_point_3 => 'Cancela en cualquier momento';

  @override
  String get free_trial_bottom_message =>
      '¡Prueba primero los juegos gratuitos, desbloquea más en cualquier momento!';

  @override
  String get free_trial_cta_primary => 'Comenzar con juegos gratuitos';

  @override
  String get free_trial_cta_secondary => 'Ver opciones de suscripción';

  @override
  String get paywall_section1_headline =>
      '¿Listo para desbloquear los 15 juegos de aprendizaje?';

  @override
  String get paywall_section1_feature_1 =>
      '15 juegos educativos de emparejamiento';

  @override
  String get paywall_section1_feature_2 =>
      'Juegos de formas, colores, patrones y lógica';

  @override
  String get paywall_section1_feature_3 =>
      'Temas de animales, profesiones y naturaleza';

  @override
  String get paywall_section1_feature_4 => 'Aprendizaje de causa y efecto';

  @override
  String get paywall_section1_feature_5 => 'Sin anuncios, sin distracciones';

  @override
  String get paywall_section1_feature_6 => 'Aprobado por maestros';

  @override
  String get paywall_section2_badge => 'Aprobado por maestros';

  @override
  String get paywall_section2_text =>
      'Los educadores reconocen a Brainy Bunny por su enfoque de desarrollo para el aprendizaje temprano.';

  @override
  String get paywall_section3_weekly_title => 'Semanal';

  @override
  String get paywall_section3_weekly_subtext => 'Pruébalo';

  @override
  String get paywall_section3_weekly_feature => 'Cancela en cualquier momento';

  @override
  String get paywall_section3_weekly_button => 'Suscribirse';

  @override
  String get paywall_section3_yearly_title => 'Anual';

  @override
  String get paywall_section3_yearly_badge => 'MEJOR VALOR - Ahorra 60%';

  @override
  String get paywall_section3_yearly_highlight => 'Prueba GRATIS de 7 días';

  @override
  String paywall_section3_yearly_breakdown(String monthlyEquivalent) {
    return 'Solo $monthlyEquivalent/mes';
  }

  @override
  String paywall_section3_yearly_fine_print(String yearlyPrice) {
    return 'Luego $yearlyPrice anualmente';
  }

  @override
  String get paywall_section3_yearly_feature =>
      'Cancela durante la prueba - sin cargo';

  @override
  String get paywall_section3_yearly_button => 'Iniciar prueba GRATIS';

  @override
  String get paywall_section3_monthly_title => 'Mensual';

  @override
  String get paywall_section3_monthly_subtext => 'Opción flexible';

  @override
  String get paywall_section3_monthly_feature => 'Cancela en cualquier momento';

  @override
  String get paywall_section3_monthly_button => 'Suscribirse';

  @override
  String get paywall_trust_element_1 => 'Pago seguro';

  @override
  String get paywall_trust_element_2 =>
      'Cancela en cualquier momento durante la prueba';

  @override
  String get paywall_trust_element_3 =>
      'Gestiona la suscripción en App Store/Play Store';

  @override
  String get paywall_trust_element_4 =>
      'Se cobra solo después del final de la prueba (para anual)';

  @override
  String get paywall_disclaimer =>
      'No se te cobrará durante tu prueba de 7 días. Cancela en cualquier momento en la configuración de tu dispositivo.';

  @override
  String get paywall_continue_free_link => 'Continuar con juegos gratuitos';

  @override
  String get parent_gate_title => 'Verificación de padres requerida';

  @override
  String get parent_gate_instruction =>
      'Esta compra requiere un adulto. Por favor resuelve este problema:';

  @override
  String get parent_gate_input_placeholder => 'Ingresa la respuesta';

  @override
  String get parent_gate_cancel => 'Cancelar';

  @override
  String get parent_gate_verify => 'Verificar';

  @override
  String get parent_gate_error =>
      'Respuesta incorrecta. Por favor, inténtalo de nuevo.';

  @override
  String get notification_type_1 => 'Nuevos desbloqueos de juegos';

  @override
  String get notification_type_2 => 'Hitos de racha de aprendizaje';

  @override
  String get notification_type_3 =>
      'Recordatorio de fin de prueba (si corresponde)';

  @override
  String get notification_type_4 => 'Aliento de aprendizaje diario';

  @override
  String get notification_trial_callout =>
      'Te recordaremos 2 días antes de que termine tu prueba, para que nunca te sorprendas.';

  @override
  String get notification_benefit_1 => 'Mantente constante con el aprendizaje';

  @override
  String get notification_benefit_2 =>
      'Nunca pierdas la fecha límite de la prueba';

  @override
  String get notification_benefit_3 => 'Celebra el progreso juntos';

  @override
  String get notification_cta_enable => 'Habilitar notificaciones';

  @override
  String get notification_cta_skip => 'Ahora no';

  @override
  String get subscription_error_loading_title => 'Cargando suscripciones';

  @override
  String get subscription_error_loading_description =>
      'Por favor espera mientras cargamos las opciones de suscripción...';

  @override
  String get subscription_error_offline_title => 'Sin conexión a Internet';

  @override
  String get subscription_error_offline_description =>
      'Por favor verifica tu conexión a Internet e inténtalo de nuevo. Necesitas estar en línea para suscribirte.';

  @override
  String get subscription_error_not_available_title =>
      'Suscripciones no disponibles';

  @override
  String get subscription_error_not_available_description =>
      'Las compras dentro de la aplicación no están disponibles en este dispositivo. Por favor inténtalo más tarde o contacta con soporte.';

  @override
  String get subscription_error_products_not_found_title =>
      'Productos no disponibles';

  @override
  String get subscription_error_products_not_found_description =>
      'No pudimos cargar productos de suscripción de la tienda. Por favor inténtalo más tarde.';

  @override
  String get subscription_error_unknown_title => 'Algo salió mal';

  @override
  String get subscription_error_unknown_description =>
      'Ocurrió un error inesperado. Por favor inténtalo de nuevo.';

  @override
  String get subscription_error_retry => 'Intentar de nuevo';

  @override
  String get subscription_error_continue_free =>
      'Continuar con juegos gratuitos';

  @override
  String get subscription_loading => 'Cargando...';

  @override
  String get goal_preschool_title => 'Prepare for preschool/kindergarten';

  @override
  String get goal_preschool_description => 'Building readiness skills';

  @override
  String get goal_cognitive_title => 'Develop cognitive abilities';

  @override
  String get goal_cognitive_description =>
      'Pattern recognition & problem-solving';

  @override
  String get goal_replace_screen_time_title => 'Replace passive screen time';

  @override
  String get goal_replace_screen_time_description =>
      'Active learning instead of videos';

  @override
  String get goal_keep_engaged_title => 'Keep them engaged & learning';

  @override
  String get goal_keep_engaged_description => 'Fun that actually builds skills';

  @override
  String get summary_age2_headline => 'Perfect for your 2-year-old explorer';

  @override
  String get summary_age2_card1_title => 'What they\'ll learn';

  @override
  String get summary_age2_card1_point1 =>
      'Basic shape recognition (circles, squares, triangles)';

  @override
  String get summary_age2_card1_point2 => 'Simple color matching';

  @override
  String get summary_age2_card1_point3 =>
      'Hand-eye coordination through drag-and-drop';

  @override
  String get summary_age2_card1_point4 => 'Cause and effect understanding';

  @override
  String get summary_age2_card2_title => 'Why it works for age 2';

  @override
  String get summary_age2_card2_point1 =>
      'Extra-large pieces perfect for tiny fingers';

  @override
  String get summary_age2_card2_point2 => 'Simple 1-2 pair matching to start';

  @override
  String get summary_age2_card2_point3 =>
      'Instant positive feedback builds confidence';

  @override
  String get summary_age2_card2_point4 =>
      'Sessions designed for 5-10 minute attention spans';

  @override
  String get summary_age3_headline => 'Designed for your curious 3-year-old';

  @override
  String get summary_age3_card1_title => 'What they\'ll learn';

  @override
  String get summary_age3_card1_point1 =>
      'Advanced shape recognition and sorting';

  @override
  String get summary_age3_card1_point2 => 'Pattern identification';

  @override
  String get summary_age3_card1_point3 => 'Color mixing and matching concepts';

  @override
  String get summary_age3_card1_point4 => 'Early problem-solving skills';

  @override
  String get summary_age3_card2_title => 'Why it works for age 3';

  @override
  String get summary_age3_card2_point1 =>
      'Progressive difficulty grows with their skills';

  @override
  String get summary_age3_card2_point2 =>
      'Builds on preschool learning concepts';

  @override
  String get summary_age3_card2_point3 =>
      'Celebrates small wins to boost motivation';

  @override
  String get summary_age3_card2_point4 => 'Perfect for emerging independence';

  @override
  String get summary_age4_headline => 'Tailored for your smart 4-year-old';

  @override
  String get summary_age4_card1_title => 'What they\'ll learn';

  @override
  String get summary_age4_card1_point1 => 'Complex pattern recognition';

  @override
  String get summary_age4_card1_point2 =>
      'Categorical thinking (animals, professions, objects)';

  @override
  String get summary_age4_card1_point3 => 'Spatial reasoning and relationships';

  @override
  String get summary_age4_card1_point4 =>
      'Pre-reading visual discrimination skills';

  @override
  String get summary_age4_card2_title => 'Why it works for age 4';

  @override
  String get summary_age4_card2_point1 =>
      'Challenges that match pre-K curriculum';

  @override
  String get summary_age4_card2_point2 =>
      'Multiple rounds build sustained focus';

  @override
  String get summary_age4_card2_point3 =>
      'Vocabulary expansion through themed games';

  @override
  String get summary_age4_card2_point4 => 'Prepares for kindergarten readiness';

  @override
  String get summary_age5_headline => 'Engaging games for your 5+ year-old';

  @override
  String get summary_age5_card1_title => 'What they\'ll learn';

  @override
  String get summary_age5_card1_point1 => 'Advanced categorization and sorting';

  @override
  String get summary_age5_card1_point2 => 'Abstract pattern completion';

  @override
  String get summary_age5_card1_point3 => 'Critical thinking and strategy';

  @override
  String get summary_age5_card1_point4 => 'Visual memory enhancement';

  @override
  String get summary_age5_card2_title => 'Why it works for age 5+';

  @override
  String get summary_age5_card2_point1 =>
      'Kindergarten-level cognitive challenges';

  @override
  String get summary_age5_card2_point2 =>
      'Builds confidence for school success';

  @override
  String get summary_age5_card2_point3 =>
      'Reinforces classroom learning at home';

  @override
  String get summary_age5_card2_point4 => 'Keeps advanced learners engaged';

  @override
  String get parental_gate_overlay_title => 'Parental Verification';

  @override
  String get parental_gate_overlay_instruction =>
      'Please solve this problem to continue:';

  @override
  String get error_purchase_verification_failed =>
      'Purchase verification failed. Please restart the app.';

  @override
  String get paywall_step2_badge_save => 'Save 60%';

  @override
  String get paywall_step2_badge_trial => '7-Day Trial';

  @override
  String get paywall_step2_yearly_title => 'Yearly';

  @override
  String paywall_step2_yearly_per_month(String price) {
    return 'Just $price/month';
  }

  @override
  String get paywall_step2_yearly_savings => 'Save 63% • Try free for 7 days';

  @override
  String get paywall_step2_yearly_feature1 => 'No charge for 7 days';

  @override
  String get paywall_step2_yearly_feature2 => 'Cancel anytime during trial';

  @override
  String get paywall_step2_yearly_feature3 =>
      'We\'ll remind you 2 days before trial ends';

  @override
  String get paywall_step2_yearly_feature4 => 'Full access to all 15 games';

  @override
  String get paywall_step2_yearly_button => 'Start FREE Trial';

  @override
  String get paywall_step2_monthly_title => 'Monthly';

  @override
  String paywall_step2_monthly_per_week(String price) {
    return '$price/week';
  }

  @override
  String get paywall_step2_monthly_savings => 'Flexible monthly plan';

  @override
  String get paywall_step2_monthly_feature1 => 'Cancel anytime';

  @override
  String get paywall_step2_monthly_feature2 => 'Full access to all 15 games';

  @override
  String get paywall_step2_monthly_button => 'Subscribe Monthly';

  @override
  String get paywall_step2_weekly_title => 'Weekly';

  @override
  String get paywall_step2_weekly_savings => 'Try for just one week';

  @override
  String get paywall_step2_weekly_feature1 => 'Cancel anytime';

  @override
  String get paywall_step2_weekly_feature2 => 'Full access to all 15 games';

  @override
  String get paywall_step2_weekly_button => 'Subscribe Weekly';

  @override
  String locked_game_headline_personalized(String childName) {
    return 'Unlock All Games for $childName!';
  }

  @override
  String get locked_game_headline_generic => 'Unlock All 15 Educational Games!';

  @override
  String locked_game_card1_title_age(int age) {
    return 'Perfect for Your $age-Year-Old';
  }

  @override
  String get locked_game_card1_title_generic => 'Age-Appropriate Learning';

  @override
  String get locked_game_card2_title => 'Building Cognitive Abilities';

  @override
  String locked_game_card3_title(int count) {
    return '$count More Games to Explore';
  }

  @override
  String get locked_game_card3_subtitle =>
      'Unlock the full collection of educational games';

  @override
  String get locked_game_age_skill_1_generic => 'Shape and color recognition';

  @override
  String get locked_game_age_skill_2_generic => 'Problem-solving abilities';

  @override
  String get locked_game_age_skill_3_generic => 'Hand-eye coordination';

  @override
  String get locked_game_age_skill_4_generic => 'Memory and focus';

  @override
  String get locked_game_age_skill_1_age2 => 'Basic shape recognition';

  @override
  String get locked_game_age_skill_2_age2 => 'Simple color matching';

  @override
  String get locked_game_age_skill_3_age2 => 'Hand-eye coordination';

  @override
  String get locked_game_age_skill_4_age2 => 'Cause and effect understanding';

  @override
  String get locked_game_age_skill_1_age3 => 'Advanced shape sorting';

  @override
  String get locked_game_age_skill_2_age3 => 'Pattern identification';

  @override
  String get locked_game_age_skill_3_age3 => 'Color mixing concepts';

  @override
  String get locked_game_age_skill_4_age3 => 'Early problem-solving';

  @override
  String get locked_game_age_skill_1_age4 => 'Complex pattern recognition';

  @override
  String get locked_game_age_skill_2_age4 => 'Categorical thinking';

  @override
  String get locked_game_age_skill_3_age4 => 'Spatial reasoning';

  @override
  String get locked_game_age_skill_4_age4 => 'Pre-reading visual skills';

  @override
  String get locked_game_age_skill_1_age5 => 'Advanced categorization';

  @override
  String get locked_game_age_skill_2_age5 => 'Abstract pattern completion';

  @override
  String get locked_game_age_skill_3_age5 => 'Critical thinking';

  @override
  String get locked_game_age_skill_4_age5 => 'Visual memory enhancement';

  @override
  String get locked_game_card2_content_default =>
      'Develop essential cognitive skills through play';

  @override
  String get locked_game_card2_content_school =>
      'Build skills for preschool and kindergarten success';

  @override
  String get locked_game_card2_content_cognitive =>
      'Enhance memory, focus, and problem-solving abilities';

  @override
  String get locked_game_card2_content_screentime =>
      'Quality educational content that parents can feel good about';

  @override
  String get locked_game_card2_content_engagement =>
      'Keep your child engaged with fun, educational activities';
}

/// The translations for Spanish Castilian, as used in Mexico (`es_MX`).
class AppLocalizationsEsMx extends AppLocalizationsEs {
  AppLocalizationsEsMx() : super('es_MX');

  @override
  String get onboarding_welcome_headline =>
      'Ayuda a tu hijo a aprender y crecer';

  @override
  String get onboarding_welcome_subheading =>
      'Juegos educativos diseñados para niños de 2 a 5 años';

  @override
  String get onboarding_welcome_cta => 'Comenzar';

  @override
  String get onboarding_name_headline => 'Personalicemos tu experiencia';

  @override
  String get onboarding_name_hint => 'Tu nombre';

  @override
  String get onboarding_name_mom => 'Mamá';

  @override
  String get onboarding_name_dad => 'Papá';

  @override
  String get onboarding_name_parent => 'Padre/Madre';

  @override
  String onboarding_name_greeting(String name) {
    return '¡Genial, $name!';
  }

  @override
  String get onboarding_child_age_headline => '¿Cuántos años tiene tu hijo?';

  @override
  String get onboarding_child_age_subtext =>
      'Todos los juegos funcionan para edades de 2-5 años. Esto nos ayuda a proporcionar consejos apropiados para la edad.';

  @override
  String get onboarding_age_2 => '2 años';

  @override
  String get onboarding_age_3 => '3 años';

  @override
  String get onboarding_age_4 => '4 años';

  @override
  String get onboarding_age_5_plus => '5+ años';

  @override
  String get onboarding_philosophy_headline =>
      'Transforma el tiempo de pantalla en tiempo de aprendizaje';

  @override
  String get onboarding_philosophy_aap =>
      'Alineado con las recomendaciones de la AAP sobre tiempo de pantalla';

  @override
  String get onboarding_philosophy_learning =>
      'Convierte el tiempo de pantalla en experiencias de aprendizaje significativas';

  @override
  String get onboarding_philosophy_skills =>
      'Desarrolla habilidades cognitivas a través del aprendizaje basado en el juego';

  @override
  String get onboarding_transition_to_games =>
      '¡Vamos a jugar! Gira tu dispositivo →';

  @override
  String get onboarding_transition_from_games =>
      '¡Excelente aprendizaje! Terminemos →';

  @override
  String get demo_game_1_skill =>
      'Emparejamiento visual y reconocimiento de formas';

  @override
  String get demo_game_1_science =>
      'Emparejar animales con sus siluetas desarrolla la discriminación visual, la capacidad de notar diferencias entre formas similares. Esta habilidad es fundamental para el reconocimiento de letras y la lectura.';

  @override
  String get demo_game_1_citation =>
      'Bornstein (1985) - Procesamiento visual y pensamiento categórico';

  @override
  String get demo_game_1_badge => 'Detective de formas';

  @override
  String get demo_game_2_skill => 'Memoria visual y atención';

  @override
  String get demo_game_2_science =>
      'Encontrar parejas fortalece la memoria de trabajo, la capacidad de tu hijo para retener y manipular información. Esto apoya directamente la resolución de problemas matemáticos y seguir instrucciones de varios pasos.';

  @override
  String get demo_game_2_citation =>
      'Gopnik & Meltzoff (1987) - Categorización y flexibilidad cognitiva';

  @override
  String get demo_game_2_badge => 'Maestro de la memoria';

  @override
  String get demo_game_3_skill => 'Asociación lógica y categorización';

  @override
  String get demo_game_3_science =>
      'Conectar objetos con sus usuarios enseña categorización y pensamiento lógico. Tu hijo aprende que las cosas van juntas por razones, un paso clave para comprender causa y efecto.';

  @override
  String get demo_game_3_citation =>
      'Piaget (1952) - Desarrollo cognitivo preoperacional';

  @override
  String get demo_game_3_badge => 'Estrella de la lógica';

  @override
  String get demo_game_4_skill => 'Reconocimiento de patrones y emparejamiento';

  @override
  String get demo_game_4_science =>
      'Emparejar patrones desarrolla el reconocimiento de patrones, la capacidad de ver relaciones entre las cosas. Esta habilidad predice fuertemente el éxito en matemáticas y ayuda a los niños a entender \'igual\' versus \'diferente\'.';

  @override
  String get demo_game_4_citation =>
      'Rittle-Johnson et al. (2019) - Habilidades de patrones y matemáticas';

  @override
  String get demo_game_4_badge => 'Profesional de patrones';

  @override
  String get demo_game_5_skill =>
      'Pensamiento simbólico y conexiones con el mundo real';

  @override
  String get demo_game_5_science =>
      'Conectar herramientas con carreras construye pensamiento simbólico, entender que una cosa puede representar otra. Este pensamiento abstracto es esencial para el lenguaje, las matemáticas y la imaginación.';

  @override
  String get demo_game_5_citation =>
      'Vygotsky (1978) - Representación simbólica en el desarrollo cognitivo';

  @override
  String get demo_game_5_badge => 'Explorador del mundo';

  @override
  String get demo_game_next => 'Siguiente juego';

  @override
  String onboarding_summary_headline(String name) {
    return 'Tu progreso hasta ahora, $name';
  }

  @override
  String onboarding_summary_age(int age) {
    return 'Perfecto para niños de $age años';
  }

  @override
  String get onboarding_summary_skills => '5 habilidades practicadas';

  @override
  String get onboarding_summary_screen_time =>
      'Enfoque de tiempo de pantalla saludable alineado con las pautas de la AAP';

  @override
  String get onboarding_summary_cta => 'Ver tu plan personalizado';

  @override
  String get trust_headline => 'Confianza de padres y educadores';

  @override
  String get trust_approved => 'Aprobado por profesores';

  @override
  String get trust_cta => 'Desbloquea la experiencia completa de aprendizaje';

  @override
  String get paywall_headline => 'Desbloquea los 15 juegos de aprendizaje';

  @override
  String get paywall_subheadline =>
      'Continúa el viaje de aprendizaje de tu hijo';

  @override
  String get paywall_feature_games =>
      '15 juegos educativos dirigidos a habilidades clave';

  @override
  String get paywall_feature_progress => 'Seguimiento del progreso de tu hijo';

  @override
  String get paywall_feature_ad_free => 'Entorno seguro y sin publicidad';

  @override
  String get paywall_feature_new_games => 'Nuevos juegos añadidos mensualmente';

  @override
  String get paywall_feature_ages => 'Diseñado para edades de 2 a 5 años';

  @override
  String get paywall_trial_headline =>
      'Prueba todas las funciones GRATIS durante 7 días';

  @override
  String get paywall_trial_price => 'Luego solo 0,87 €/semana';

  @override
  String get paywall_trial_today => 'Hoy: Acceso completo desbloqueado';

  @override
  String get paywall_trial_day5 => 'Día 5: Te enviaremos un recordatorio';

  @override
  String get paywall_trial_day7 =>
      'Día 7: Comienza la facturación (cancela en cualquier momento antes)';

  @override
  String get paywall_trial_no_payment => '✓ Sin pago inmediato';

  @override
  String get paywall_trial_cta => 'Comenzar prueba gratuita →';

  @override
  String get paywall_weekly => 'Semanal';

  @override
  String get paywall_monthly => 'Mensual';

  @override
  String get paywall_yearly => 'Anual';

  @override
  String get paywall_save_60 => 'Ahorra 60%';

  @override
  String get paywall_most_popular => 'Opción más popular';

  @override
  String get paywall_cancel_anytime =>
      'Cancela en cualquier momento desde la configuración de tu dispositivo';

  @override
  String get paywall_restore => 'Restaurar compras';

  @override
  String get subscription_premium => 'Premium';

  @override
  String get subscription_trial => 'Prueba de 7 días';

  @override
  String get subscription_lifetime => 'Acceso de por vida';

  @override
  String get subscription_expired => 'Suscripción finalizada';

  @override
  String get subscription_manage => 'Gestionar suscripción';

  @override
  String get subscription_cancel => 'Cancelar suscripción';

  @override
  String get subscription_change_plan => 'Cambiar plan';

  @override
  String get continue_button => 'Continuar';

  @override
  String get skip_button => 'Saltar';

  @override
  String get close_button => 'Cerrar';

  @override
  String get loading => 'Cargando...';

  @override
  String get error_purchase_failed => 'Compra fallida';

  @override
  String get error_purchase_failed_message =>
      'No pudimos completar tu compra. Por favor, inténtalo de nuevo.';

  @override
  String get error_restore_failed => 'No se encontraron compras';

  @override
  String get error_restore_failed_message =>
      'No pudimos encontrar compras anteriores. Si crees que esto es un error, por favor contacta con soporte.';

  @override
  String get error_network => 'Error de red';

  @override
  String get error_network_message =>
      'Por favor, verifica tu conexión a internet e inténtalo de nuevo.';

  @override
  String get summary_headline => '¡Progreso increíble!';

  @override
  String summary_message_age_2(Object name) {
    return '¡Buen trabajo, $name! Tu pequeño está construyendo habilidades importantes a través del juego. A los 2 años, cada emparejamiento que hacen fortalece su reconocimiento visual y habilidades de resolución de problemas.';
  }

  @override
  String summary_message_age_3(Object name) {
    return '¡Maravilloso, $name! Tu hijo está desarrollando habilidades cognitivas cruciales. A los 3 años, estas actividades mejoran su memoria, concentración y pensamiento lógico.';
  }

  @override
  String summary_message_age_4(Object name) {
    return '¡Excelente trabajo, $name! Tu hijo de 4 años está dominando la resolución avanzada de problemas. Estos juegos lo están preparando para el éxito en el jardín de niños.';
  }

  @override
  String summary_message_age_5_plus(Object name) {
    return '¡Fantástico, $name! Tu hijo está sobresaliendo en el pensamiento complejo. Estas habilidades le darán una base sólida para la escuela.';
  }

  @override
  String get summary_badges_earned => 'Insignias obtenidas';

  @override
  String get summary_badges => 'Insignias';

  @override
  String get summary_games => 'Juegos';

  @override
  String get summary_skills => 'Habilidades';

  @override
  String get trust_aap_description =>
      'Nuestro enfoque educativo se alinea con las pautas de la AAP para el tiempo de pantalla saludable y el desarrollo infantil temprano.';

  @override
  String get trust_research_title => 'Currículo respaldado por investigación';

  @override
  String get trust_research_description =>
      'Cada juego está diseñado basándose en estudios revisados por pares sobre desarrollo cognitivo, con métodos probados para mejorar el aprendizaje en niños de 2 a 5 años.';

  @override
  String get trust_testimonial_1_name => 'Sarah M., Madre de niño de 3 años';

  @override
  String get trust_testimonial_1_quote =>
      '\"Mi hija pasó de tener dificultades con las formas a identificarlas con confianza en todas partes. ¡El progreso en solo 2 semanas me sorprendió!\"';

  @override
  String get trust_testimonial_2_name => 'Michael T., Padre de niño de 4 años';

  @override
  String get trust_testimonial_2_quote =>
      '\"¡Finalmente, tiempo de pantalla con el que me siento bien! Mi hijo está aprendiendo mientras se divierte, y puedo ver mejoras cognitivas reales.\"';

  @override
  String get trust_downloads_title => 'Únete a más de 100,000 familias';

  @override
  String get trust_downloads_description =>
      'Confiado por padres en más de 50 países para dar a sus hijos una ventaja en el aprendizaje.';

  @override
  String get trust_cta_headline =>
      '¿Listo para desbloquear el potencial completo de tu hijo?';

  @override
  String get trust_cta_button => 'Comenzar prueba gratuita';

  @override
  String get paywall_premium_badge => 'Acceso Premium';

  @override
  String get paywall_step1_headline =>
      'Desbloquea el potencial completo de tu hijo';

  @override
  String get paywall_value_1_title => '15 juegos premium';

  @override
  String get paywall_value_1_description =>
      'Accede a todos los juegos educativos diseñados para edades de 2 a 5 años, que cubren formas, colores, números, lógica y más';

  @override
  String get paywall_value_2_title => 'Seguimiento del progreso';

  @override
  String get paywall_value_2_description =>
      'Análisis detallados que muestran el desarrollo de tu hijo y la mejora de habilidades a lo largo del tiempo';

  @override
  String get paywall_value_3_title => 'Aprendizaje personalizado';

  @override
  String get paywall_value_3_description =>
      'Los juegos se adaptan a la edad y nivel de habilidad de tu hijo para un aprendizaje óptimo';

  @override
  String get paywall_value_4_title => 'Contenido nuevo mensualmente';

  @override
  String get paywall_value_4_description =>
      'Actualizaciones regulares con nuevos juegos y actividades para mantener el aprendizaje fresco y atractivo';

  @override
  String get paywall_step1_cta => 'Ver planes';

  @override
  String get paywall_secure_payment => 'Procesamiento de pago seguro';

  @override
  String get paywall_trial_badge => 'Prueba gratuita de 7 días';

  @override
  String get paywall_step2_headline => 'Elige tu plan';

  @override
  String get paywall_step2_subheadline =>
      'Comienza tu prueba gratuita de 7 días. Cancela en cualquier momento.';

  @override
  String get paywall_plan_best_value => 'Mejor valor';

  @override
  String get paywall_plan_yearly_title => 'Anual';

  @override
  String get paywall_plan_yearly_period => '/año';

  @override
  String get paywall_plan_yearly_per_month => 'Solo 3,33 €/mes';

  @override
  String get paywall_plan_yearly_savings =>
      'Ahorra 63% en comparación con mensual';

  @override
  String get paywall_plan_monthly_title => 'Mensual';

  @override
  String get paywall_plan_monthly_period => '/mes';

  @override
  String get paywall_plan_weekly_title => 'Semanal';

  @override
  String get paywall_plan_weekly_period => '/semana';

  @override
  String get paywall_plan_weekly_note => 'Para acceso a corto plazo';

  @override
  String get paywall_trial_reminder =>
      'Tu prueba gratuita comienza hoy. No se te cobrará hasta el día 8. Cancela en cualquier momento antes sin costo.';

  @override
  String get paywall_step2_cta => 'Continuar';

  @override
  String get paywall_terms =>
      'Al continuar, aceptas nuestros Términos de servicio y Política de privacidad';

  @override
  String get paywall_urgency_text => 'Oferta por tiempo limitado';

  @override
  String get paywall_step3_headline => '¡Estás a un paso!';

  @override
  String get paywall_step3_included_title => 'Todo lo que obtienes:';

  @override
  String get paywall_included_1 => 'Los 15 juegos educativos premium';

  @override
  String get paywall_included_2 =>
      'Rutas de aprendizaje personalizadas para tu hijo';

  @override
  String get paywall_included_3 =>
      'Seguimiento detallado del progreso e información';

  @override
  String get paywall_included_4 => 'Contenido y actividades nuevos mensuales';

  @override
  String get paywall_included_5 =>
      'Experiencia sin publicidad para un aprendizaje enfocado';

  @override
  String get paywall_included_6 =>
      'Modo sin conexión - aprende en cualquier lugar, en cualquier momento';

  @override
  String get paywall_guarantee_title => 'Garantía 100% sin riesgo';

  @override
  String get paywall_guarantee_text =>
      'Pruébalo gratis durante 7 días. Si no estás completamente satisfecho, cancela antes de que finalice la prueba y no pagues nada. Sin preguntas.';

  @override
  String get paywall_step3_cta => 'Comenzar mi prueba gratuita';

  @override
  String get onboarding_rotate_to_landscape =>
      'Gira tu dispositivo a horizontal';

  @override
  String get onboarding_rotate_to_portrait => 'Gira tu dispositivo a vertical';

  @override
  String get demo_game_1_title => 'Formas de animales';

  @override
  String get demo_game_1_context =>
      '¡Ayuda a tu hijo a emparejar animales con sus formas! Esto desarrolla habilidades de reconocimiento visual esenciales para la lectura.';

  @override
  String get demo_game_2_title => 'Emparejamiento de memoria';

  @override
  String get demo_game_2_context =>
      'Encuentra parejas para fortalecer la memoria de trabajo, crucial para seguir instrucciones y resolver problemas.';

  @override
  String get demo_game_3_title => 'Rompecabezas de lógica';

  @override
  String get demo_game_3_context =>
      'Resuelve rompecabezas para desarrollar habilidades de pensamiento lógico y reconocimiento de patrones.';

  @override
  String get demo_game_4_title => 'Diversión con patrones';

  @override
  String get demo_game_4_context =>
      'Reconoce patrones para desarrollar preparación matemática y habilidades de secuenciación.';

  @override
  String get demo_game_5_title => 'Explorador del mundo';

  @override
  String get demo_game_5_context =>
      'Explora el mundo para ampliar el vocabulario y la conciencia cultural.';

  @override
  String get demo_game_congratulations => '¡Increíble! ¡Ganaste una insignia!';

  @override
  String paywall_benefit_all_games(int gameCount) {
    return 'Acceso a todos los $gameCount juegos';
  }

  @override
  String get paywall_benefit_age_appropriate =>
      'Contenido apropiado para la edad';

  @override
  String get paywall_benefit_progress_tracking => 'Seguimiento del progreso';

  @override
  String get paywall_benefit_offline_play => 'Modo sin conexión compatible';

  @override
  String get paywall_benefit_no_ads => 'Sin anuncios, seguro para niños';

  @override
  String get paywall_benefit_regular_updates => 'Actualizaciones regulares';

  @override
  String get paywall_start_trial => 'Comenzar prueba gratuita';

  @override
  String get paywall_step3_benefit_1 =>
      'Acceso completo a todos los juegos de aprendizaje';

  @override
  String get paywall_step3_benefit_2 =>
      'Entorno de aprendizaje seguro y sin publicidad';

  @override
  String get paywall_step3_benefit_3 => 'Perfecto para toda la familia';

  @override
  String get paywall_subscribe_button => 'Suscribirse ahora';

  @override
  String get educational_value_headline =>
      'Aprendizaje a través del juego, no entretenimiento sin sentido';

  @override
  String get educational_value_point_1_title =>
      'Desarrolla el reconocimiento de patrones y el pensamiento lógico';

  @override
  String get educational_value_point_1_description =>
      'Habilidades esenciales para la preparación matemática y la resolución de problemas';

  @override
  String get educational_value_point_2_title =>
      'Fortalece las habilidades de discriminación visual';

  @override
  String get educational_value_point_2_description =>
      'Ayuda a los niños a identificar diferencias y similitudes';

  @override
  String get educational_value_point_3_title =>
      'Desarrolla habilidades de resolución de problemas';

  @override
  String get educational_value_point_3_description =>
      'El compromiso activo desarrolla el pensamiento crítico';

  @override
  String get educational_value_point_4_title =>
      'Diseñado para un tiempo de pantalla saludable';

  @override
  String get educational_value_point_4_description =>
      'Alineado con la recomendación pediátrica de 1 hora diaria';

  @override
  String get educational_value_research =>
      'La investigación muestra: Las actividades de emparejamiento mejoran el razonamiento espacial y el desarrollo cognitivo en niños de 2-5 años.';

  @override
  String get educational_value_research_source =>
      'Basado en estudios de psicología del desarrollo';

  @override
  String get value_carousel_1_headline =>
      'Tiempo de pantalla que desarrolla habilidades';

  @override
  String get value_carousel_1_description =>
      '15 juegos de emparejamiento diferentes que enseñan formas, colores, patrones, animales, profesiones y pensamiento de causa y efecto.';

  @override
  String get value_carousel_1_subtext =>
      'No solo entretenimiento: aprendizaje real en cada emparejamiento.';

  @override
  String get value_carousel_1_benefit_1_title => 'Desarrollo de la memoria';

  @override
  String get value_carousel_1_benefit_1_description =>
      'Juegos de emparejamiento que fortalecen el recuerdo y el reconocimiento';

  @override
  String get value_carousel_1_benefit_2_title => 'Resolución de problemas';

  @override
  String get value_carousel_1_benefit_2_description =>
      'Rompecabezas que fomentan el pensamiento lógico y la estrategia';

  @override
  String get value_carousel_1_benefit_3_title => 'Aprendizaje progresivo';

  @override
  String get value_carousel_1_benefit_3_description =>
      'La dificultad se adapta a medida que tu hijo domina nuevas habilidades';

  @override
  String get value_carousel_2_headline => '15 formas de aprender y crecer';

  @override
  String get value_carousel_2_description =>
      'Desde la simple coincidencia de formas hasta la comprensión de relaciones entre objetos y situaciones: cada juego se dirige a habilidades de desarrollo específicas.';

  @override
  String get value_carousel_2_category_1 => 'Formas y geometría';

  @override
  String get value_carousel_2_category_2 => 'Colores y patrones';

  @override
  String get value_carousel_2_category_3 => 'Animales y naturaleza';

  @override
  String get value_carousel_2_category_4 => 'Profesiones y roles';

  @override
  String get value_carousel_2_category_5 => 'Causa y efecto';

  @override
  String get value_carousel_2_category_6 => 'Y más...';

  @override
  String get value_carousel_2_feature_1_title => '100% sin anuncios';

  @override
  String get value_carousel_2_feature_1_description =>
      'Sin anuncios, sin distracciones, sin contenido inapropiado';

  @override
  String get value_carousel_2_feature_2_title => 'Apropiado para la edad';

  @override
  String get value_carousel_2_feature_2_description =>
      'Contenido diseñado específicamente para edades de 2-5 años';

  @override
  String get value_carousel_2_feature_3_title => 'Aprendizaje activo';

  @override
  String get value_carousel_2_feature_3_description =>
      'Actividades atractivas, no visualización pasiva';

  @override
  String get value_carousel_3_headline =>
      'Aprobado por maestros, confiable para padres';

  @override
  String get value_carousel_3_trust_element_1 =>
      'Entorno de aprendizaje sin anuncios';

  @override
  String get value_carousel_3_trust_element_2 => 'Sin recopilación de datos';

  @override
  String get value_carousel_3_trust_element_3 => 'Diseñado por educadores';

  @override
  String get value_carousel_3_trust_element_4 => 'Seguro para niños pequeños';

  @override
  String get value_carousel_3_feature_1_title => 'Seguimiento del progreso';

  @override
  String get value_carousel_3_feature_1_description =>
      'Vea qué habilidades está desarrollando su hijo';

  @override
  String get value_carousel_3_feature_2_title => 'Recompensas de logros';

  @override
  String get value_carousel_3_feature_2_description =>
      'Gane estrellas e insignias que motivan el aprendizaje continuo';

  @override
  String get value_carousel_3_feature_3_title => 'Experiencia personalizada';

  @override
  String get value_carousel_3_feature_3_description =>
      'Juegos que se adaptan al nivel de habilidad de su hijo';

  @override
  String get summary_headline_new =>
      '¡Perfecto! Aquí está tu ruta de aprendizaje personalizada';

  @override
  String get summary_learning_path_title => 'Lo que aprenderá tu hijo:';

  @override
  String get summary_skill_cognitive => 'Desarrollo cognitivo';

  @override
  String summary_skill_cognitive_desc(int age) {
    return 'Juegos de memoria y actividades de resolución de problemas perfectos para la edad de $age años';
  }

  @override
  String get summary_skill_visual => 'Percepción visual';

  @override
  String summary_skill_visual_desc(int age) {
    return 'Juegos de reconocimiento de formas y conciencia espacial para niños de $age años';
  }

  @override
  String get summary_skill_exploration => 'Exploración y descubrimiento';

  @override
  String summary_skill_exploration_desc(int age) {
    return 'Juegos interactivos que fomentan la curiosidad a los $age años';
  }

  @override
  String get summary_next_step =>
      'Siguiente: ¡Prueba Premium gratis durante 7 días para desbloquear todos los juegos!';

  @override
  String get trial_badge => 'Prueba gratuita de 7 días';

  @override
  String get trial_headline => 'Prueba Premium gratis durante 7 días';

  @override
  String get trial_description =>
      'Obtén acceso completo a todos los juegos y funciones premium. Cancela en cualquier momento durante tu prueba, sin cargos si cancelas antes de que finalice.';

  @override
  String get trial_feature_1_title => 'Todos los juegos premium';

  @override
  String get trial_feature_1_description =>
      'Accede a todos los juegos de aprendizaje de nuestra biblioteca';

  @override
  String get trial_feature_2_title => 'Experiencia sin publicidad';

  @override
  String get trial_feature_2_description =>
      'Entorno de aprendizaje seguro sin anuncios';

  @override
  String get trial_feature_3_title => 'Seguimiento del progreso';

  @override
  String get trial_feature_3_description =>
      'Ve el desarrollo y los logros de tu hijo';

  @override
  String get trial_feature_4_title => 'Actualizaciones regulares';

  @override
  String get trial_feature_4_description =>
      'Nuevos juegos y funciones añadidos regularmente';

  @override
  String get trial_how_it_works_title => 'Cómo funciona:';

  @override
  String get trial_step_1 => 'Comienza tu prueba gratuita de 7 días hoy';

  @override
  String get trial_step_2 =>
      'Disfruta de acceso completo a todas las funciones premium';

  @override
  String get trial_step_3 =>
      'Cancela en cualquier momento, sin cargos antes de que finalice la prueba';

  @override
  String get trial_cta => 'Comenzar mi prueba gratuita';

  @override
  String get trial_disclaimer =>
      'Gratis durante 7 días, luego la tarifa del plan seleccionado. Cancela en cualquier momento.';

  @override
  String get notification_permission_headline =>
      'Mantente conectado al aprendizaje de tu hijo';

  @override
  String get notification_permission_description =>
      'Recibe recordatorios útiles y celebra hitos con tu hijo. Te enviaremos notificaciones oportunas sobre logros y oportunidades de aprendizaje.';

  @override
  String get notification_benefit_1_title => 'Recordatorios de prueba';

  @override
  String get notification_benefit_1_description =>
      'Recibe notificaciones antes de que finalice tu prueba para que nunca pierdas el acceso';

  @override
  String get notification_benefit_2_title => 'Hitos de aprendizaje';

  @override
  String get notification_benefit_2_description =>
      'Celebra cuando tu hijo alcance nuevos logros';

  @override
  String get notification_benefit_3_title => 'Consejos de participación';

  @override
  String get notification_benefit_3_description =>
      'Obtén sugerencias para mantener el aprendizaje divertido y atractivo';

  @override
  String get notification_privacy_note =>
      'Respetamos tu privacidad. Puedes desactivar las notificaciones en cualquier momento en la configuración.';

  @override
  String get notification_enable_button => 'Activar notificaciones';

  @override
  String get notification_maybe_later => 'Quizás más tarde';

  @override
  String get subscription_management_title => 'Gestionar suscripción';

  @override
  String get subscription_status_active => 'Premium activo';

  @override
  String get subscription_status_active_description =>
      'Tienes acceso completo a todas las funciones premium';

  @override
  String get subscription_status_inactive => 'Versión gratuita';

  @override
  String get subscription_status_inactive_description =>
      'Actualiza a premium para acceso completo a todos los juegos';

  @override
  String get subscription_actions_title => 'Acciones';

  @override
  String get subscription_restore_title => 'Restaurar compras';

  @override
  String get subscription_restore_description =>
      '¿Ya estás suscrito en otro dispositivo? Restaura tus compras aquí.';

  @override
  String get subscription_restore_button => 'Restaurar';

  @override
  String get subscription_manage_title => 'Gestiona tu suscripción';

  @override
  String get subscription_manage_description =>
      'Ver, cambiar o cancelar tu suscripción a través de tu cuenta de la tienda de aplicaciones.';

  @override
  String get subscription_manage_button => 'Abrir configuración de suscripción';

  @override
  String get subscription_help_title => 'Ayuda e información';

  @override
  String get subscription_cancel_title => 'Cómo cancelar';

  @override
  String get subscription_cancel_description =>
      'Puedes cancelar tu suscripción en cualquier momento a través de la configuración de tu cuenta de App Store o Google Play. Mantendrás el acceso hasta el final de tu período de facturación.';

  @override
  String get subscription_payment_failure_title => 'Problemas de pago';

  @override
  String get subscription_payment_failure_description =>
      'Si tu pago falla, actualiza tu método de pago en tu cuenta de la tienda de aplicaciones. Reintentaremos el pago automáticamente.';

  @override
  String get next_button => 'Siguiente';

  @override
  String get back_button => 'Atrás';

  @override
  String get onboarding_priority_question =>
      '¿Qué es lo más importante para ti?';

  @override
  String get onboarding_priority_1 => 'Tiempo de pantalla educativo';

  @override
  String get onboarding_priority_1_sub => 'Aprender mientras juegan';

  @override
  String get onboarding_priority_2 => 'Preparación escolar';

  @override
  String get onboarding_priority_2_sub =>
      'Preparación para preescolar/jardín de infancia';

  @override
  String get onboarding_priority_3 => 'Mantenerlos comprometidos';

  @override
  String get onboarding_priority_3_sub =>
      'Tiempo de pantalla productivo y feliz';

  @override
  String get onboarding_priority_4 => 'Aprender a través del juego';

  @override
  String get onboarding_priority_4_sub =>
      'Diversión que desarrolla habilidades';

  @override
  String get onboarding_transition_message => '¡Gracias por compartir!';

  @override
  String onboarding_transition_submessage(int age) {
    return 'Estamos creando la experiencia perfecta para tu hijo de $age años...';
  }

  @override
  String summary_result_headline(int age) {
    return '¡Perfecto para tu hijo de $age años!';
  }

  @override
  String get summary_card_1_title => 'Lo que aprenderán:';

  @override
  String get summary_card_1_point_1 =>
      'Reconocimiento de patrones a través de 15 juegos diferentes';

  @override
  String get summary_card_1_point_2 => 'Habilidades de discriminación visual';

  @override
  String get summary_card_1_point_3 =>
      'Pensamiento lógico y resolución de problemas';

  @override
  String get summary_card_1_point_4 =>
      'Conexiones del mundo real (animales, profesiones, naturaleza)';

  @override
  String get summary_card_2_title => 'Por qué funciona:';

  @override
  String get summary_card_2_point_1 =>
      'El aprendizaje activo supera la visualización pasiva';

  @override
  String get summary_card_2_point_2 =>
      'La retroalimentación inmediata los mantiene comprometidos';

  @override
  String get summary_card_2_point_3 => 'La variedad previene el aburrimiento';

  @override
  String get summary_card_2_point_4 =>
      'Alineado con las pautas de tiempo de pantalla';

  @override
  String get summary_cta => 'Ver qué está incluido';

  @override
  String get free_trial_headline => 'Prueba 5 juegos gratis, desbloquea 10 más';

  @override
  String get free_trial_free_section =>
      'Comienza con 5 juegos de emparejamiento gratuitos';

  @override
  String get free_trial_free_point_1 => 'Formas, colores y más';

  @override
  String get free_trial_free_point_2 => 'Sin límite de tiempo';

  @override
  String get free_trial_free_point_3 => 'No se requiere tarjeta de crédito';

  @override
  String get free_trial_premium_section =>
      'Desbloquea los 15 juegos con una suscripción';

  @override
  String get free_trial_premium_point_1 => 'Biblioteca completa de juegos';

  @override
  String get free_trial_premium_point_2 => 'Nuevas categorías de aprendizaje';

  @override
  String get free_trial_premium_point_3 => 'Cancela en cualquier momento';

  @override
  String get free_trial_bottom_message =>
      '¡Prueba primero los juegos gratuitos, desbloquea más en cualquier momento!';

  @override
  String get free_trial_cta_primary => 'Comenzar con juegos gratuitos';

  @override
  String get free_trial_cta_secondary => 'Ver opciones de suscripción';

  @override
  String get paywall_section1_headline =>
      '¿Listo para desbloquear los 15 juegos de aprendizaje?';

  @override
  String get paywall_section1_feature_1 =>
      '15 juegos educativos de emparejamiento';

  @override
  String get paywall_section1_feature_2 =>
      'Juegos de formas, colores, patrones y lógica';

  @override
  String get paywall_section1_feature_3 =>
      'Temas de animales, profesiones y naturaleza';

  @override
  String get paywall_section1_feature_4 => 'Aprendizaje de causa y efecto';

  @override
  String get paywall_section1_feature_5 => 'Sin anuncios, sin distracciones';

  @override
  String get paywall_section1_feature_6 => 'Aprobado por maestros';

  @override
  String get paywall_section2_badge => 'Aprobado por maestros';

  @override
  String get paywall_section2_text =>
      'Los educadores reconocen a Brainy Bunny por su enfoque de desarrollo para el aprendizaje temprano.';

  @override
  String get paywall_section3_weekly_title => 'Semanal';

  @override
  String get paywall_section3_weekly_subtext => 'Pruébalo';

  @override
  String get paywall_section3_weekly_feature => 'Cancela en cualquier momento';

  @override
  String get paywall_section3_weekly_button => 'Suscribirse';

  @override
  String get paywall_section3_yearly_title => 'Anual';

  @override
  String get paywall_section3_yearly_badge => 'MEJOR VALOR - Ahorra 60%';

  @override
  String get paywall_section3_yearly_highlight => 'Prueba GRATIS de 7 días';

  @override
  String paywall_section3_yearly_breakdown(String monthlyEquivalent) {
    return 'Solo $monthlyEquivalent/mes';
  }

  @override
  String paywall_section3_yearly_fine_print(String yearlyPrice) {
    return 'Luego $yearlyPrice anualmente';
  }

  @override
  String get paywall_section3_yearly_feature =>
      'Cancela durante la prueba - sin cargo';

  @override
  String get paywall_section3_yearly_button => 'Iniciar prueba GRATIS';

  @override
  String get paywall_section3_monthly_title => 'Mensual';

  @override
  String get paywall_section3_monthly_subtext => 'Opción flexible';

  @override
  String get paywall_section3_monthly_feature => 'Cancela en cualquier momento';

  @override
  String get paywall_section3_monthly_button => 'Suscribirse';

  @override
  String get paywall_trust_element_1 => 'Pago seguro';

  @override
  String get paywall_trust_element_2 =>
      'Cancela en cualquier momento durante la prueba';

  @override
  String get paywall_trust_element_3 =>
      'Gestiona la suscripción en App Store/Play Store';

  @override
  String get paywall_trust_element_4 =>
      'Se cobra solo después del final de la prueba (para anual)';

  @override
  String get paywall_disclaimer =>
      'No se te cobrará durante tu prueba de 7 días. Cancela en cualquier momento en la configuración de tu dispositivo.';

  @override
  String get paywall_continue_free_link => 'Continuar con juegos gratuitos';

  @override
  String get parent_gate_title => 'Verificación de padres requerida';

  @override
  String get parent_gate_instruction =>
      'Esta compra requiere un adulto. Por favor resuelve este problema:';

  @override
  String get parent_gate_input_placeholder => 'Ingresa la respuesta';

  @override
  String get parent_gate_cancel => 'Cancelar';

  @override
  String get parent_gate_verify => 'Verificar';

  @override
  String get parent_gate_error =>
      'Respuesta incorrecta. Por favor, inténtalo de nuevo.';

  @override
  String get notification_type_1 => 'Nuevos desbloqueos de juegos';

  @override
  String get notification_type_2 => 'Hitos de racha de aprendizaje';

  @override
  String get notification_type_3 =>
      'Recordatorio de fin de prueba (si corresponde)';

  @override
  String get notification_type_4 => 'Aliento de aprendizaje diario';

  @override
  String get notification_trial_callout =>
      'Te recordaremos 2 días antes de que termine tu prueba, para que nunca te sorprendas.';

  @override
  String get notification_benefit_1 => 'Mantente constante con el aprendizaje';

  @override
  String get notification_benefit_2 =>
      'Nunca pierdas la fecha límite de la prueba';

  @override
  String get notification_benefit_3 => 'Celebra el progreso juntos';

  @override
  String get notification_cta_enable => 'Habilitar notificaciones';

  @override
  String get notification_cta_skip => 'Ahora no';

  @override
  String get subscription_error_loading_title => 'Cargando suscripciones';

  @override
  String get subscription_error_loading_description =>
      'Por favor espera mientras cargamos las opciones de suscripción...';

  @override
  String get subscription_error_offline_title => 'Sin conexión a Internet';

  @override
  String get subscription_error_offline_description =>
      'Por favor verifica tu conexión a Internet e inténtalo de nuevo. Necesitas estar en línea para suscribirte.';

  @override
  String get subscription_error_not_available_title =>
      'Suscripciones no disponibles';

  @override
  String get subscription_error_not_available_description =>
      'Las compras dentro de la aplicación no están disponibles en este dispositivo. Por favor inténtalo más tarde o contacta con soporte.';

  @override
  String get subscription_error_products_not_found_title =>
      'Productos no disponibles';

  @override
  String get subscription_error_products_not_found_description =>
      'No pudimos cargar productos de suscripción de la tienda. Por favor inténtalo más tarde.';

  @override
  String get subscription_error_unknown_title => 'Algo salió mal';

  @override
  String get subscription_error_unknown_description =>
      'Ocurrió un error inesperado. Por favor inténtalo de nuevo.';

  @override
  String get subscription_error_retry => 'Intentar de nuevo';

  @override
  String get subscription_error_continue_free =>
      'Continuar con juegos gratuitos';

  @override
  String get subscription_loading => 'Cargando...';
}

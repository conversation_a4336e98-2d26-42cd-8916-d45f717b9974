// lib/onboarding/screens/paywall_step_2_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:brainy_bunny/constants/subscription_constants.dart';
import 'package:brainy_bunny/onboarding/widgets/parental_gate_dialog.dart';
import 'package:brainy_bunny/onboarding/widgets/purchase_error_dialog.dart';
import 'package:brainy_bunny/services/purchase_manager.dart';
import 'package:brainy_bunny/services/subscription_manager.dart';

/// Paywall Step 2: Plan Selection with Trial
/// Shows subscription options with 7-day free trial
class PaywallStep2Screen extends StatefulWidget {
  final VoidCallback onContinue;
  final VoidCallback onBack;
  final VoidCallback onSkip;

  const PaywallStep2Screen({
    super.key,
    required this.onContinue,
    required this.onBack,
    required this.onSkip,
  });

  @override
  State<PaywallStep2Screen> createState() => _PaywallStep2ScreenState();
}

class _PaywallStep2ScreenState extends State<PaywallStep2Screen> {
  String _selectedPlan = SubscriptionConstants.SUBSCRIPTION_YEARLY; // Default to yearly for best conversion
  final SubscriptionManager _subscriptionManager = SubscriptionManager();

  // Calculate monthly equivalent for yearly plan
  String _getYearlyMonthlyEquivalent(AppLocalizations l10n) {
    final yearlyPrice = _subscriptionManager.yearlyProduct?.price ?? SubscriptionConstants.FALLBACK_YEARLY_PRICE;
    final numericValue = yearlyPrice.replaceAll(RegExp(r'[^\d.]'), '');
    final yearlyAmount = double.tryParse(numericValue) ?? 39.99;
    final monthlyEquivalent = (yearlyAmount / 12).toStringAsFixed(2);
    final currencySymbol = yearlyPrice.replaceAll(RegExp(r'[\d.]'), '').trim();
    return l10n.paywall_step2_yearly_per_month('$currencySymbol$monthlyEquivalent');
  }

  // Calculate weekly equivalent for monthly plan
  String _getMonthlyWeeklyEquivalent(AppLocalizations l10n) {
    final monthlyPrice = _subscriptionManager.monthlyProduct?.price ?? SubscriptionConstants.FALLBACK_MONTHLY_PRICE;
    final numericValue = monthlyPrice.replaceAll(RegExp(r'[^\d.]'), '');
    final monthlyAmount = double.tryParse(numericValue) ?? 8.99;
    final weeklyEquivalent = (monthlyAmount / 4.33).toStringAsFixed(2);
    final currencySymbol = monthlyPrice.replaceAll(RegExp(r'[\d.]'), '').trim();
    return l10n.paywall_step2_monthly_per_week('$currencySymbol$weeklyEquivalent');
  }

  void _showParentalGate() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => ParentalGateDialog(
        onVerified: () async {
          // Age verification passed, attempt to initialize purchase
          await _attemptPurchaseInitialization();
        },
        onCancel: () {
          // User cancelled verification, stay on paywall
        },
      ),
    );
  }

  Future<void> _attemptPurchaseInitialization() async {
    try {
      final purchaseManager = PurchaseManager();

      // Show loading indicator
      if (mounted) {
        setState(() {
          // Could add a loading state here if needed
        });
      }

      // Try to initialize purchase manager
      if (!purchaseManager.isInitialized) {
        await purchaseManager.initialize();
      }

      // Check if we successfully initialized
      if (!purchaseManager.isInitialized) {
        _showPurchaseError();
        return;
      }

      // Now attempt the actual purchase with the selected plan
      if (kDebugMode) {
        print('✅ Purchase manager initialized, attempting purchase of $_selectedPlan');
      }

      // Import SubscriptionManager to access the purchaseSubscription method
      final subscriptionManager = SubscriptionManager();
      final success = await subscriptionManager.purchaseSubscription(_selectedPlan);

      if (mounted) {
        if (success) {
          // Purchase successful - proceed to next screen
          if (kDebugMode) {
            print('✅ Purchase completed successfully');
          }
          widget.onContinue();
        } else {
          // Purchase failed or was cancelled
          if (kDebugMode) {
            print('❌ Purchase was not completed');
          }
          // Don't show error - user may have just cancelled
          // The purchase manager will handle error dialogs if needed
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error during purchase flow: $e');
      }
      if (mounted) {
        _showPurchaseError();
      }
    }
  }

  void _showPurchaseError() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => PurchaseErrorDialog(
        onContinueWithFree: () {
          // Skip paywall and proceed with free games
          widget.onSkip();
        },
      ),
    );
  }

  Future<void> _restorePurchases() async {
    try {
      if (kDebugMode) {
        print('🔄 Restore purchases initiated from paywall');
      }

      final purchaseManager = PurchaseManager();

      // Show loading
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Restoring purchases...'),
            duration: Duration(seconds: 2),
          ),
        );
      }

      await purchaseManager.restorePurchases();

      // Check if purchase was restored
      if (purchaseManager.isPurchased) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Purchases restored successfully!'),
              backgroundColor: Colors.green,
            ),
          );
        }
        // Skip paywall since purchase is now active
        widget.onSkip();
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('No previous purchases found'),
            ),
          );
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error restoring purchases: $e');
      }
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Restore failed. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final size = MediaQuery.of(context).size;

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Back and Skip buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  IconButton(
                    onPressed: widget.onBack,
                    icon: const Icon(Icons.arrow_back),
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                  TextButton(
                    onPressed: widget.onSkip,
                    child: Text(
                      l10n.skip_button,
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ),
                ],
              ),

              SizedBox(height: size.height * 0.02),

              // Free trial badge
              Center(
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.green.shade400,
                        Colors.green.shade600,
                      ],
                    ),
                    borderRadius: BorderRadius.circular(24),
                  ),
                  child: Text(
                    l10n.paywall_trial_badge,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),

              SizedBox(height: size.height * 0.03),

              // Headline
              Center(
                child: Text(
                  'Choose Your Plan',
                  style: const TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1E3A5F),
                    height: 1.3,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

              const SizedBox(height: 12),

              // Subheadline
              Center(
                child: Text(
                  'All plans unlock 15 educational games',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey.shade600,
                    height: 1.4,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

              SizedBox(height: size.height * 0.04),

              // Plan options
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      // Yearly plan (Most Popular)
                      _PlanCard(
                        isSelected: _selectedPlan == SubscriptionConstants.SUBSCRIPTION_YEARLY,
                        onTap: () => setState(() => _selectedPlan = SubscriptionConstants.SUBSCRIPTION_YEARLY),
                        badge: l10n.paywall_step2_badge_save,
                        secondBadge: l10n.paywall_step2_badge_trial,
                        badgeColor: Colors.amber.shade600,
                        secondBadgeColor: Colors.green.shade600,
                        title: l10n.paywall_step2_yearly_title,
                        price: _subscriptionManager.yearlyProduct?.price ?? SubscriptionConstants.FALLBACK_YEARLY_PRICE,
                        perMonth: _getYearlyMonthlyEquivalent(l10n),
                        savings: l10n.paywall_step2_yearly_savings,
                        features: [
                          l10n.paywall_step2_yearly_feature1,
                          l10n.paywall_step2_yearly_feature2,
                          l10n.paywall_step2_yearly_feature3,
                          l10n.paywall_step2_yearly_feature4,
                        ],
                        buttonText: l10n.paywall_step2_yearly_button,
                        isHighlighted: true,
                      ),

                      const SizedBox(height: 16),

                      // Monthly plan
                      _PlanCard(
                        isSelected: _selectedPlan == SubscriptionConstants.SUBSCRIPTION_MONTHLY,
                        onTap: () => setState(() => _selectedPlan = SubscriptionConstants.SUBSCRIPTION_MONTHLY),
                        title: l10n.paywall_step2_monthly_title,
                        price: _subscriptionManager.monthlyProduct?.price ?? SubscriptionConstants.FALLBACK_MONTHLY_PRICE,
                        perMonth: _getMonthlyWeeklyEquivalent(l10n),
                        savings: l10n.paywall_step2_monthly_savings,
                        features: [
                          l10n.paywall_step2_monthly_feature1,
                          l10n.paywall_step2_monthly_feature2,
                        ],
                        buttonText: l10n.paywall_step2_monthly_button,
                        isHighlighted: false,
                      ),

                      const SizedBox(height: 16),

                      // Weekly plan
                      _PlanCard(
                        isSelected: _selectedPlan == SubscriptionConstants.SUBSCRIPTION_WEEKLY,
                        onTap: () => setState(() => _selectedPlan = SubscriptionConstants.SUBSCRIPTION_WEEKLY),
                        title: l10n.paywall_step2_weekly_title,
                        price: _subscriptionManager.weeklyProduct?.price ?? SubscriptionConstants.FALLBACK_WEEKLY_PRICE,
                        savings: l10n.paywall_step2_weekly_savings,
                        features: [
                          l10n.paywall_step2_weekly_feature1,
                          l10n.paywall_step2_weekly_feature2,
                        ],
                        buttonText: l10n.paywall_step2_weekly_button,
                        isHighlighted: false,
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Trial & Reminders info box
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.blue.shade200,
                    width: 1.5,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: Colors.blue.shade700,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Trial & Reminders:',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                            color: Colors.blue.shade900,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Text(
                      'Your 7-day free trial starts today with the yearly plan. You won\'t be charged until day 8.',
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.blue.shade900,
                        height: 1.4,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'We\'ll send you a reminder 2 days before your trial ends, so you can decide to continue or cancel at no cost.',
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.blue.shade900,
                        height: 1.4,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Weekly and monthly plans start immediately with no trial.',
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.blue.shade900,
                        height: 1.4,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Security/Trust elements
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.check_circle_outline, size: 16, color: Colors.grey.shade700),
                  const SizedBox(width: 6),
                  Text(
                    'Secure payment processing',
                    style: TextStyle(fontSize: 12, color: Colors.grey.shade700),
                  ),
                ],
              ),
              const SizedBox(height: 6),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.check_circle_outline, size: 16, color: Colors.grey.shade700),
                  const SizedBox(width: 6),
                  Text(
                    'Manage in App/Play Store',
                    style: TextStyle(fontSize: 12, color: Colors.grey.shade700),
                  ),
                ],
              ),

              const SizedBox(height: 20),

              // Restore Purchase Link
              Center(
                child: TextButton(
                  onPressed: _restorePurchases,
                  child: Text(
                    l10n.paywall_restore,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.blue.shade700,
                      fontWeight: FontWeight.w500,
                      decoration: TextDecoration.underline,
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 8),

              // Terms
              Center(
                child: Text(
                  l10n.paywall_terms,
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.grey.shade500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _PlanCard extends StatelessWidget {
  final bool isSelected;
  final VoidCallback onTap;
  final String? badge;
  final String? secondBadge;
  final Color? badgeColor;
  final Color? secondBadgeColor;
  final String title;
  final String price;
  final String? perMonth;
  final String? savings;
  final List<String>? features;
  final String? buttonText;
  final bool isHighlighted;

  const _PlanCard({
    required this.isSelected,
    required this.onTap,
    this.badge,
    this.secondBadge,
    this.badgeColor,
    this.secondBadgeColor,
    required this.title,
    required this.price,
    this.perMonth,
    this.savings,
    this.features,
    this.buttonText,
    this.isHighlighted = false,
  });

  @override
  Widget build(BuildContext context) {
    final borderColor = isHighlighted
        ? (isSelected ? Colors.blue.shade600 : Colors.green.shade600)
        : (isSelected ? Colors.blue.shade600 : Colors.grey.shade300);

    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isHighlighted
            ? Colors.green.shade50
            : (isSelected ? Colors.blue.shade50 : Colors.grey.shade50),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: borderColor,
          width: isHighlighted ? 3 : (isSelected ? 3 : 2),
        ),
        boxShadow: (isHighlighted || isSelected)
            ? [
                BoxShadow(
                  color: isHighlighted ? Colors.green.shade200 : Colors.blue.shade200,
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ]
            : [],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Badges row
          if (badge != null || secondBadge != null)
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (badge != null) ...[
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: badgeColor ?? Colors.amber.shade600,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      badge!,
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
                if (badge != null && secondBadge != null)
                  const SizedBox(width: 8),
                if (secondBadge != null) ...[
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: secondBadgeColor ?? Colors.green.shade600,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      secondBadge!,
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ],
              ],
            ),

          if (badge != null || secondBadge != null)
            const SizedBox(height: 12),

          // Title
          Text(
            title,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2C3E50),
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 12),

          // Price
          Text(
            price,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Color(0xFF2C3E50),
            ),
            textAlign: TextAlign.center,
          ),

          if (perMonth != null) ...[
            const SizedBox(height: 4),
            Text(
              perMonth!,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
          ],

          if (savings != null) ...[
            const SizedBox(height: 12),
            Text(
              savings!,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade700,
              ),
              textAlign: TextAlign.center,
            ),
          ],

          // Features list
          if (features != null && features!.isNotEmpty) ...[
            const SizedBox(height: 16),
            ...features!.map((feature) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  Icon(
                    Icons.check_circle,
                    size: 18,
                    color: Colors.green.shade600,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      feature,
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.grey.shade800,
                        height: 1.3,
                      ),
                    ),
                  ),
                ],
              ),
            )),
          ],

          // Button
          if (buttonText != null) ...[
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: onTap,
                style: ElevatedButton.styleFrom(
                  backgroundColor: isHighlighted
                      ? Colors.green.shade600
                      : Colors.blue.shade600,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 14),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 2,
                ),
                child: Text(
                  buttonText!,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}

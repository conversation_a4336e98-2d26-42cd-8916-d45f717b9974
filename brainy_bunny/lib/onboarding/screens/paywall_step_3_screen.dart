// lib/onboarding/screens/paywall_step_3_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:brainy_bunny/onboarding/widgets/parental_gate_dialog.dart';
import 'package:brainy_bunny/onboarding/widgets/purchase_error_dialog.dart';
import 'package:brainy_bunny/services/purchase_manager.dart';

/// Paywall Step 3: Final Confirmation with Urgency
/// Last chance to convert with urgency elements and final benefits summary
class PaywallStep3Screen extends StatefulWidget {
  final VoidCallback onSubscribe;
  final VoidCallback onBack;
  final VoidCallback onSkip;

  const PaywallStep3Screen({
    super.key,
    required this.onSubscribe,
    required this.onBack,
    required this.onSkip,
  });

  @override
  State<PaywallStep3Screen> createState() => _PaywallStep3ScreenState();
}

class _PaywallStep3ScreenState extends State<PaywallStep3Screen>
    with SingleTickerProviderStateMixin {
  late AnimationController _pulseController;

  @override
  void initState() {
    super.initState();

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat(reverse: true);
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  void _showParentalGate() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => ParentalGateDialog(
        onVerified: () async {
          // Age verification passed, attempt to initialize purchase
          await _attemptPurchaseInitialization();
        },
        onCancel: () {
          // User cancelled verification, stay on paywall
        },
      ),
    );
  }

  Future<void> _attemptPurchaseInitialization() async {
    try {
      final purchaseManager = PurchaseManager();

      // Try to initialize purchase manager
      if (!purchaseManager.isInitialized) {
        await purchaseManager.initialize();
      }

      // Check if we successfully initialized and can proceed
      if (purchaseManager.isInitialized) {
        if (kDebugMode) {
          print('✅ Purchase manager initialized, proceeding with subscription');
        }
        widget.onSubscribe();
      } else {
        // Initialization failed, show error
        _showPurchaseError();
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error initializing purchase: $e');
      }
      _showPurchaseError();
    }
  }

  void _showPurchaseError() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => PurchaseErrorDialog(
        onContinueWithFree: () {
          // Skip paywall and proceed with free games
          widget.onSkip();
        },
      ),
    );
  }

  Future<void> _restorePurchases() async {
    try {
      if (kDebugMode) {
        print('🔄 Restore purchases initiated from paywall step 3');
      }

      final purchaseManager = PurchaseManager();

      // Show loading
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Restoring purchases...'),
            duration: Duration(seconds: 2),
          ),
        );
      }

      await purchaseManager.restorePurchases();

      // Check if purchase was restored
      if (purchaseManager.isPurchased) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Purchases restored successfully!'),
              backgroundColor: Colors.green,
            ),
          );
        }
        // Skip paywall since purchase is now active
        widget.onSkip();
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('No previous purchases found'),
            ),
          );
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error restoring purchases: $e');
      }
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Restore failed. Please try again.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;
    final size = MediaQuery.of(context).size;

    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Back and Skip buttons
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  IconButton(
                    onPressed: widget.onBack,
                    icon: const Icon(Icons.arrow_back),
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                  TextButton(
                    onPressed: widget.onSkip,
                    child: Text(
                      l10n.skip_button,
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ),
                ],
              ),

              SizedBox(height: size.height * 0.02),

              // Urgency timer (visual only)
              Center(
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 12,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.red.shade50,
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: Colors.red.shade300,
                      width: 2,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      ScaleTransition(
                        scale: Tween<double>(begin: 1.0, end: 1.15).animate(
                          CurvedAnimation(
                            parent: _pulseController,
                            curve: Curves.easeInOut,
                          ),
                        ),
                        child: Icon(
                          Icons.schedule,
                          color: Colors.red.shade700,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        l10n.paywall_urgency_text,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.red.shade700,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              SizedBox(height: size.height * 0.03),

              // Headline
              Center(
                child: Text(
                  l10n.paywall_step3_headline,
                  style: const TextStyle(
                    fontSize: 26,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1E3A5F),
                    height: 1.3,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),

              SizedBox(height: size.height * 0.04),

              // What's included section
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        l10n.paywall_step3_included_title,
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF1E3A5F),
                        ),
                      ),
                      const SizedBox(height: 20),
                      _IncludedFeature(
                        icon: Icons.check_circle,
                        text: l10n.paywall_included_1,
                      ),
                      const SizedBox(height: 12),
                      _IncludedFeature(
                        icon: Icons.check_circle,
                        text: l10n.paywall_included_2,
                      ),
                      const SizedBox(height: 12),
                      _IncludedFeature(
                        icon: Icons.check_circle,
                        text: l10n.paywall_included_3,
                      ),
                      const SizedBox(height: 12),
                      _IncludedFeature(
                        icon: Icons.check_circle,
                        text: l10n.paywall_included_4,
                      ),
                      const SizedBox(height: 12),
                      _IncludedFeature(
                        icon: Icons.check_circle,
                        text: l10n.paywall_included_5,
                      ),
                      const SizedBox(height: 12),
                      _IncludedFeature(
                        icon: Icons.check_circle,
                        text: l10n.paywall_included_6,
                      ),

                      SizedBox(height: size.height * 0.03),

                      // Risk-free guarantee
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: Colors.green.shade50,
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: Colors.green.shade200,
                            width: 2,
                          ),
                        ),
                        child: Column(
                          children: [
                            Icon(
                              Icons.verified_user,
                              color: Colors.green.shade700,
                              size: 48,
                            ),
                            const SizedBox(height: 12),
                            Text(
                              l10n.paywall_guarantee_title,
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.green.shade900,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              l10n.paywall_guarantee_text,
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.green.shade800,
                                height: 1.4,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Final CTA with pulsing effect
              ScaleTransition(
                scale: Tween<double>(begin: 1.0, end: 1.02).animate(
                  CurvedAnimation(
                    parent: _pulseController,
                    curve: Curves.easeInOut,
                  ),
                ),
                child: SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _showParentalGate,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green.shade600,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 20),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      elevation: 4,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          l10n.paywall_step3_cta,
                          style: const TextStyle(
                            fontSize: 19,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(width: 8),
                        const Icon(Icons.arrow_forward, size: 24),
                      ],
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 16),

              // Restore Purchase Link
              Center(
                child: TextButton(
                  onPressed: _restorePurchases,
                  child: Text(
                    l10n.paywall_restore,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.blue.shade700,
                      fontWeight: FontWeight.w500,
                      decoration: TextDecoration.underline,
                    ),
                  ),
                ),
              ),

              const SizedBox(height: 8),

              // Cancel anytime
              Center(
                child: Text(
                  l10n.paywall_cancel_anytime,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _IncludedFeature extends StatelessWidget {
  final IconData icon;
  final String text;

  const _IncludedFeature({
    required this.icon,
    required this.text,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          color: Colors.green.shade600,
          size: 24,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            text,
            style: TextStyle(
              fontSize: 15,
              color: Colors.grey.shade800,
              height: 1.4,
            ),
          ),
        ),
      ],
    );
  }
}

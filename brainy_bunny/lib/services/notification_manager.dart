// lib/services/notification_manager.dart
import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/data/latest_all.dart' as tz;
import 'package:timezone/timezone.dart' as tz;

/// Manages local notifications for trial reminders and engagement
class NotificationManager {
  static final NotificationManager _instance = NotificationManager._internal();
  factory NotificationManager() => _instance;
  NotificationManager._internal();

  static NotificationManager get instance => _instance;

  final FlutterLocalNotificationsPlugin _notificationsPlugin =
      FlutterLocalNotificationsPlugin();
  bool _isInitialized = false;

  /// Initialize notification system
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize timezone database
      tz.initializeTimeZones();

      // Initialize plugin
      const initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/ic_launcher');
      const initializationSettingsIOS = DarwinInitializationSettings(
        requestAlertPermission: false,
        requestBadgePermission: false,
        requestSoundPermission: false,
      );
      const initializationSettings = InitializationSettings(
        android: initializationSettingsAndroid,
        iOS: initializationSettingsIOS,
      );

      await _notificationsPlugin.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      _isInitialized = true;
      if (kDebugMode) {
        print('✅ NotificationManager initialized');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error initializing NotificationManager: $e');
      }
    }
  }

  /// Handle notification tap
  void _onNotificationTapped(NotificationResponse response) {
    if (kDebugMode) {
      print('Notification tapped: ${response.payload}');
    }
    // TODO: Handle navigation based on payload
  }

  /// Request notification permissions (iOS)
  Future<bool> requestPermissions() async {
    try {
      if (!_isInitialized) {
        await initialize();
      }

      final bool? result = await _notificationsPlugin
          .resolvePlatformSpecificImplementation<
              IOSFlutterLocalNotificationsPlugin>()
          ?.requestPermissions(
            alert: true,
            badge: true,
            sound: true,
          );

      // Android 13+ also needs runtime permission
      final androidImplementation = _notificationsPlugin
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>();
      if (androidImplementation != null) {
        await androidImplementation.requestNotificationsPermission();
      }

      if (kDebugMode) {
        print('✅ Notification permissions: ${result ?? "granted"}');
      }
      return result ?? true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error requesting notification permissions: $e');
      }
      return false;
    }
  }

  /// Schedule trial notifications
  /// Day 1: Welcome & Tips
  /// Day 5: Trial ending soon
  /// Day 6: Last day reminder
  /// Day 7: Trial ending today
  Future<void> scheduleTrialNotifications({String? yearlyPrice}) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      final now = DateTime.now();
      final displayPrice = yearlyPrice ?? '€39.99';

      // Day 1: Welcome notification
      await _scheduleNotification(
        id: 1,
        title: 'Welcome to Brainy Bunny!',
        body: 'Your 7-day trial has begun. Explore all 15 games!',
        scheduledDate: now.add(const Duration(minutes: 5)), // Shortly after start
      );

      // Day 5: First Warning (PRIMARY WARNING - most important)
      await _scheduleNotification(
        id: 5,
        title: 'Trial ending in 2 days!',
        body: 'You\'ll be charged $displayPrice/year unless you cancel.',
        scheduledDate: now.add(const Duration(days: 5, hours: 17)), // 5:00 PM local time
      );

      // Day 6: Final Warning
      await _scheduleNotification(
        id: 6,
        title: 'Tomorrow your trial ends',
        body: 'Loved the games? Keep learning! Or cancel anytime.',
        scheduledDate: now.add(const Duration(days: 6, hours: 17)), // 5:00 PM local time
      );

      // Day 7: Conversion Confirmation
      await _scheduleNotification(
        id: 7,
        title: 'Your subscription is now active',
        body: 'Thank you for supporting your child\'s learning!',
        scheduledDate: now.add(const Duration(days: 7, hours: 1)), // ~24 hours after start
      );

      if (kDebugMode) {
        print('✅ Trial notifications scheduled (publisher guidelines compliant)');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error scheduling trial notifications: $e');
      }
    }
  }

  /// Get pending notifications for debugging
  Future<List<PendingNotificationRequest>> getPendingNotifications() async {
    return await _notificationsPlugin.pendingNotificationRequests();
  }

  /// Schedule engagement notifications (optional, after trial)
  Future<void> scheduleEngagementNotifications() async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      // Weekly reminder to play (only if not played in 3+ days)
      // This would require checking last play date

      if (kDebugMode) {
        print('✅ Engagement notifications scheduled');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error scheduling engagement notifications: $e');
      }
    }
  }

  /// Schedule renewal reminder per publisher guidelines
  /// Monthly: 3 days before renewal
  /// Yearly: 7 days before renewal
  Future<void> scheduleRenewalReminder({
    required DateTime renewalDate,
    required String tier,
    required String price,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      // Different lead time based on tier
      final daysBeforeRenewal = tier == 'yearly' ? 7 : 3;
      final reminderDate = renewalDate.subtract(Duration(days: daysBeforeRenewal));

      await _scheduleNotification(
        id: 100,
        title: tier == 'monthly'
            ? 'Your monthly subscription renews in 3 days ($price)'
            : 'Your annual subscription renews in 7 days ($price)',
        body: 'Want to continue learning? Or manage your subscription in settings.',
        scheduledDate: reminderDate,
      );

      // Renewal confirmation
      await _scheduleNotification(
        id: 101,
        title: 'Subscription renewed!',
        body: tier == 'yearly'
            ? 'Another year of learning ahead!'
            : 'Keep learning all month!',
        scheduledDate: renewalDate.add(const Duration(hours: 1)),
      );

      if (kDebugMode) {
        print('✅ Renewal reminder scheduled for $reminderDate ($tier)');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error scheduling renewal reminder: $e');
      }
    }
  }

  /// Cancel all notifications
  Future<void> cancelAllNotifications() async {
    try {
      await _notificationsPlugin.cancelAll();

      if (kDebugMode) {
        print('✅ All notifications canceled');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error canceling notifications: $e');
      }
    }
  }

  /// Cancel specific notification
  Future<void> cancelNotification(int id) async {
    try {
      await _notificationsPlugin.cancel(id);

      if (kDebugMode) {
        print('✅ Notification $id canceled');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error canceling notification $id: $e');
      }
    }
  }

  /// Internal helper to schedule a notification
  Future<void> _scheduleNotification({
    required int id,
    required String title,
    required String body,
    required DateTime scheduledDate,
  }) async {
    try {
      await _notificationsPlugin.zonedSchedule(
        id,
        title,
        body,
        tz.TZDateTime.from(scheduledDate, tz.local),
        const NotificationDetails(
          android: AndroidNotificationDetails(
            'brainy_bunny_trial',
            'Trial Notifications',
            channelDescription: 'Notifications about your free trial',
            importance: Importance.high,
            priority: Priority.high,
          ),
          iOS: DarwinNotificationDetails(),
        ),
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        uiLocalNotificationDateInterpretation:
            UILocalNotificationDateInterpretation.absoluteTime,
      );

      if (kDebugMode) {
        print('📅 Scheduled notification $id for $scheduledDate: $title');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error scheduling notification $id: $e');
      }
    }
  }
}

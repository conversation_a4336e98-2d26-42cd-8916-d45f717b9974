// lib/services/analytics/revenuecat_service.dart
import 'package:flutter/foundation.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:brainy_bunny/services/analytics/adjust_service.dart';

/// Service for RevenueCat integration
/// Used ONLY for purchase event tracking, not for paywall UI
/// Maps Adjust ID to RevenueCat for cross-platform attribution
class RevenueCatService {
  // Singleton pattern
  static final RevenueCatService _instance = RevenueCatService._internal();
  static RevenueCatService get instance => _instance;
  RevenueCatService._internal();

  bool _isInitialized = false;
  String? _apiKey;

  bool get isInitialized => _isInitialized;

  /// Initialize RevenueCat SDK
  /// [apiKey] - RevenueCat API key (obtain from dashboard)
  Future<void> initialize({required String apiKey}) async {
    if (_isInitialized) {
      if (kDebugMode) {
        print('⚠️ RevenueCat already initialized');
      }
      return;
    }

    _apiKey = apiKey;

    try {
      if (kDebugMode) {
        print('💰 Initializing RevenueCat...');
        print('   API Key: ${apiKey.substring(0, 8)}...');
      }

      // Configure RevenueCat
      PurchasesConfiguration configuration = PurchasesConfiguration(apiKey);

      // Initialize SDK
      await Purchases.configure(configuration);

      // Enable debug logging in debug mode
      if (kDebugMode) {
        await Purchases.setLogLevel(LogLevel.debug);
      }

      // Map Adjust ID to RevenueCat
      await _mapAdjustId();

      _isInitialized = true;

      if (kDebugMode) {
        print('✅ RevenueCat initialized successfully');
      }
    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('❌ Failed to initialize RevenueCat: $e');
        print('Stack trace: $stackTrace');
      }
      // Don't rethrow - app should work without analytics
    }
  }

  /// Map Adjust ID to RevenueCat for cross-platform attribution
  Future<void> _mapAdjustId() async {
    try {
      // Retrieve Adjust ID
      String? adjustId = await AdjustService.instance.getAdid();

      if (adjustId != null && adjustId.isNotEmpty) {
        // Set as RevenueCat subscriber attribute
        await Purchases.setAdjustID(adjustId);

        if (kDebugMode) {
          print('📊 Mapped Adjust ID to RevenueCat: $adjustId');
        }
      } else {
        if (kDebugMode) {
          print('⚠️ Adjust ID not available yet, will retry...');
        }

        // Retry after a delay
        await Future.delayed(const Duration(seconds: 2));
        adjustId = await AdjustService.instance.getAdid();

        if (adjustId != null && adjustId.isNotEmpty) {
          await Purchases.setAdjustID(adjustId);
          if (kDebugMode) {
            print('📊 Mapped Adjust ID to RevenueCat (retry): $adjustId');
          }
        } else {
          if (kDebugMode) {
            print('⚠️ Adjust ID still not available after retry');
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to map Adjust ID: $e');
      }
    }
  }

  /// Set user ID
  Future<void> setUserId(String userId) async {
    if (!_isInitialized) {
      if (kDebugMode) {
        print('⚠️ RevenueCat not initialized, skipping setUserId');
      }
      return;
    }

    try {
      await Purchases.logIn(userId);
      if (kDebugMode) {
        print('👤 RevenueCat user ID set: $userId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to set RevenueCat user ID: $e');
      }
    }
  }

  /// Set custom attribute
  Future<void> setAttribute(String key, String value) async {
    if (!_isInitialized) return;

    try {
      await Purchases.setAttributes({key: value});
      if (kDebugMode) {
        print('📝 RevenueCat attribute set: $key = $value');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to set RevenueCat attribute: $e');
      }
    }
  }

  /// Get customer info (for subscription status)
  Future<CustomerInfo?> getCustomerInfo() async {
    if (!_isInitialized) return null;

    try {
      CustomerInfo customerInfo = await Purchases.getCustomerInfo();
      return customerInfo;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to get RevenueCat customer info: $e');
      }
      return null;
    }
  }

  /// Check if user has active subscription
  Future<bool> hasActiveSubscription() async {
    try {
      CustomerInfo? customerInfo = await getCustomerInfo();
      if (customerInfo == null) return false;

      // Check if any entitlements are active
      bool hasActive = customerInfo.entitlements.active.isNotEmpty;

      if (kDebugMode) {
        print('🔍 RevenueCat subscription check: ${hasActive ? "Active" : "Inactive"}');
        if (hasActive) {
          print('   Active entitlements: ${customerInfo.entitlements.active.keys.join(", ")}');
        }
      }

      return hasActive;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to check subscription status: $e');
      }
      return false;
    }
  }

  /// Sync attribution data
  Future<void> syncAttributionData(Map<String, dynamic> data, String source) async {
    if (!_isInitialized) return;

    try {
      // RevenueCat automatically syncs with Adjust via webhook
      // This is just for additional custom attribution data if needed
      if (kDebugMode) {
        print('📊 Syncing attribution data from $source');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to sync attribution data: $e');
      }
    }
  }

  /// Log out current user
  Future<void> logOut() async {
    if (!_isInitialized) return;

    try {
      await Purchases.logOut();
      if (kDebugMode) {
        print('👋 RevenueCat user logged out');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to log out RevenueCat user: $e');
      }
    }
  }
}

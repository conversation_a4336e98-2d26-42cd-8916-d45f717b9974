// lib/constants/subscription_constants.dart

/// Constants for subscription management
class SubscriptionConstants {
  // Subscription Product IDs
  // NOTE: Must match product IDs in AppConstants and store configuration
  static const String SUBSCRIPTION_WEEKLY = 'brainy_bunny_weekly';
  static const String SUBSCRIPTION_MONTHLY = 'brainy_bunny_monthly';
  static const String SUBSCRIPTION_YEARLY = 'brainy_bunny_yearly';

  // All subscription product IDs
  static const Set<String> SUBSCRIPTION_IDS = {
    SUBSCRIPTION_WEEKLY,
    SUBSCRIPTION_MONTHLY,
    SUBSCRIPTION_YEARLY,
  };

  // Trial configuration
  static const int TRIAL_DURATION_DAYS = 7;
  static const int TRIAL_REMINDER_DAY_5 = 5;
  static const int TRIAL_REMINDER_DAY_6 = 6;

  // Pricing (for display only - actual prices come from store)
  static const String FALLBACK_WEEKLY_PRICE = '€4.99';
  static const String FALLBACK_MONTHLY_PRICE = '€8.99';
  static const String FALLBACK_YEARLY_PRICE = '€39.99';
  static const String FALLBACK_YEARLY_WEEKLY_EQUIVALENT = '€0.77';

  // Discount percentages
  static const int YEARLY_DISCOUNT_PERCENT = 63; // 62.9% rounded to 63%

  // Grace period (days)
  static const int GRACE_PERIOD_DAYS_IOS = 16;
  static const int GRACE_PERIOD_DAYS_ANDROID = 3;

  // Subscription tiers
  static const String TIER_WEEKLY = 'weekly';
  static const String TIER_MONTHLY = 'monthly';
  static const String TIER_YEARLY = 'yearly';
  static const String TIER_LIFETIME = 'lifetime'; // For grandfathered users

  // Subscription status
  static const String STATUS_ACTIVE = 'active';
  static const String STATUS_CANCELLED = 'cancelled';
  static const String STATUS_EXPIRED = 'expired';
  static const String STATUS_IN_GRACE_PERIOD = 'grace_period';
  static const String STATUS_ON_HOLD = 'on_hold';

  // Storage keys
  static const String KEY_SUBSCRIPTION_TIER = 'subscription_tier';
  static const String KEY_SUBSCRIPTION_EXPIRY = 'subscription_expiry';
  static const String KEY_SUBSCRIPTION_STATUS = 'subscription_status';
  static const String KEY_IS_TRIAL_ACTIVE = 'is_trial_active';
  static const String KEY_TRIAL_START_DATE = 'trial_start_date';
  static const String KEY_ORIGINAL_PURCHASE_DATE = 'original_purchase_date';
  static const String KEY_IS_GRANDFATHERED = 'is_grandfathered';
  static const String KEY_AUTO_RENEW_ENABLED = 'auto_renew_enabled';

  // Verification
  static const int VERIFICATION_TIMEOUT_SECONDS = 10;
  static const int VERIFICATION_RETRY_ATTEMPTS = 3;
  static const int VERIFICATION_RATE_LIMIT_PER_MINUTE = 10;

  // Price loading
  static const int PRICE_LOAD_TIMEOUT_SECONDS = 10;
  static const int PRICE_LOAD_RETRY_ATTEMPTS = 4;
  static const List<int> PRICE_LOAD_RETRY_DELAYS_MS = [0, 1000, 2000, 4000];
  static const int PRICE_CACHE_VALIDITY_HOURS = 24;
}

// lib/services/analytics/meta_service.dart
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

/// Service for Meta (Facebook) SDK integration via platform channels
/// Handles campaign tracking and conversion events
class MetaService {
  // Singleton pattern
  static final MetaService _instance = MetaService._internal();
  static MetaService get instance => _instance;
  MetaService._internal();

  static const MethodChannel _channel = MethodChannel('com.goodkarmalab.brainy_bunny/meta');

  bool _isInitialized = false;

  bool get isInitialized => _isInitialized;

  /// Initialize Meta SDK
  /// Note: Actual initialization happens in native code (AppDelegate.swift / MainActivity.kt)
  /// This just marks the service as ready
  Future<void> initialize() async {
    if (_isInitialized) {
      if (kDebugMode) {
        print('⚠️ Meta SDK already initialized');
      }
      return;
    }

    try {
      if (kDebugMode) {
        print('📘 Initializing Meta SDK via platform channel...');
      }

      // Verify platform channel is available
      try {
        await _channel.invokeMethod('isInitialized');
      } catch (e) {
        if (kDebugMode) {
          print('⚠️ Meta SDK platform channel not available: $e');
          print('   Native implementation may not be configured yet');
          print('   App will continue without Meta tracking');
        }
        return;
      }

      _isInitialized = true;

      if (kDebugMode) {
        print('✅ Meta SDK initialized successfully');
      }
    } catch (e, stackTrace) {
      if (kDebugMode) {
        print('❌ Failed to initialize Meta SDK: $e');
        print('Stack trace: $stackTrace');
      }
      // Don't rethrow - app should work without analytics
    }
  }

  /// Log custom event
  /// [eventName] - The event name
  /// [parameters] - Optional event parameters
  Future<void> logEvent(String eventName, {Map<String, dynamic>? parameters}) async {
    if (!_isInitialized) {
      if (kDebugMode) {
        print('⚠️ Meta SDK not initialized, skipping event: $eventName');
      }
      return;
    }

    try {
      await _channel.invokeMethod('logEvent', {
        'eventName': eventName,
        'parameters': parameters ?? {},
      });

      if (kDebugMode) {
        print('📘 Meta Event Logged: $eventName');
        if (parameters != null && parameters.isNotEmpty) {
          print('   Parameters: $parameters');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Failed to log Meta event: $e');
      }
    }
  }

  /// Log CompleteRegistration event
  /// Standard Meta event for onboarding completion
  Future<void> logCompleteRegistration() async {
    await logEvent('CompleteRegistration');
  }

  /// Log StartTrial event
  /// [subscriptionId] - The subscription product ID
  /// [price] - Optional price value
  /// [currency] - Optional currency code
  Future<void> logStartTrial({
    required String subscriptionId,
    double? price,
    String? currency,
  }) async {
    Map<String, dynamic> parameters = {
      'subscription_id': subscriptionId,
    };

    if (price != null) {
      parameters['value'] = price;
    }

    if (currency != null) {
      parameters['currency'] = currency;
    }

    await logEvent('StartTrial', parameters: parameters);
  }

  /// Log Subscribe event
  /// Standard Meta event for subscription purchase
  /// [value] - The purchase value
  /// [currency] - The currency code (e.g., 'EUR', 'USD')
  /// [subscriptionId] - Optional subscription product ID
  Future<void> logSubscribe({
    required double value,
    required String currency,
    String? subscriptionId,
  }) async {
    Map<String, dynamic> parameters = {
      'value': value,
      'currency': currency,
    };

    if (subscriptionId != null) {
      parameters['subscription_id'] = subscriptionId;
    }

    await logEvent('Subscribe', parameters: parameters);
  }

  /// Log Purchase event
  /// Standard Meta event for any purchase
  /// [value] - The purchase value
  /// [currency] - The currency code
  /// [contentId] - Optional content/product ID
  Future<void> logPurchase({
    required double value,
    required String currency,
    String? contentId,
  }) async {
    Map<String, dynamic> parameters = {
      'value': value,
      'currency': currency,
    };

    if (contentId != null) {
      parameters['content_id'] = contentId;
    }

    await logEvent('Purchase', parameters: parameters);
  }

  /// Log ViewContent event
  /// [contentType] - Type of content viewed
  /// [contentId] - ID of content viewed
  Future<void> logViewContent({
    required String contentType,
    required String contentId,
  }) async {
    await logEvent('ViewContent', parameters: {
      'content_type': contentType,
      'content_id': contentId,
    });
  }

  /// Log AddToCart event (for paywall views)
  /// [contentId] - The subscription product ID
  /// [value] - Optional price value
  /// [currency] - Optional currency
  Future<void> logAddToCart({
    required String contentId,
    double? value,
    String? currency,
  }) async {
    Map<String, dynamic> parameters = {
      'content_id': contentId,
    };

    if (value != null) {
      parameters['value'] = value;
    }

    if (currency != null) {
      parameters['currency'] = currency;
    }

    await logEvent('AddToCart', parameters: parameters);
  }
}

{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98665d4af6d8c1b65dcf7bf753072688b0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFunctions/FirebaseFunctions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFunctions/FirebaseFunctions.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseFunctions", "PRODUCT_NAME": "FirebaseFunctions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987ed58fb019024710146eabfca1f03939", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985ca38621b8d5aef9a86cb8ce3e339c41", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFunctions/FirebaseFunctions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFunctions/FirebaseFunctions.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFunctions", "PRODUCT_NAME": "FirebaseFunctions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985aeeca1f1da1fe0fa4f8b26fe8a3e7a1", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985ca38621b8d5aef9a86cb8ce3e339c41", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFunctions/FirebaseFunctions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFunctions/FirebaseFunctions.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFunctions", "PRODUCT_NAME": "FirebaseFunctions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98613ac1ff9005585802509e4401ca0436", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e73471ca30f206b800069c303b30e841", "guid": "bfdfe7dc352907fc980b868725387e98a182088013ad2017b8f472e330b802ff", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98328eefb8885df668ed9411efe5f13049", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9829e61caf6a806f380597f4d6bcc35a87", "guid": "bfdfe7dc352907fc980b868725387e982a7a8f73e8a852b0ca4bb86c1e1ca0fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840b495d2a7844b218da01230fdd85e86", "guid": "bfdfe7dc352907fc980b868725387e98cc887caa67992119883ef3d934b67afc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f7f613457dfd03c0d414a54252b80c8", "guid": "bfdfe7dc352907fc980b868725387e9827271e9bd4ec9197072f8de37d359e51"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d6200de6a7386345ffa3eda2c9c4985", "guid": "bfdfe7dc352907fc980b868725387e983b971f46e8acc53a4ec4ed5752feb45a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9861ae2fcaa9965654ba5eff8709496896", "guid": "bfdfe7dc352907fc980b868725387e98e47edd582c9cfaf08458d6818b9ab468"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aab3fdc76eb7f3bf357e7d40c06ace55", "guid": "bfdfe7dc352907fc980b868725387e983bba3e15370b24bb55cceb2d8136daac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851e658db5ba375dbb693dde279624e76", "guid": "bfdfe7dc352907fc980b868725387e98110e6cd02c9ee4c318b8d389e0cb6ad0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2793d2acd47c696113a5a373b279f2e", "guid": "bfdfe7dc352907fc980b868725387e98e0afb700bbc84d1d54200ea7c922e732"}], "guid": "bfdfe7dc352907fc980b868725387e9845c4f66a2171d1091f5df591c9e9b545", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986d33a47f1e1031e7e6a671aab8d386e4", "guid": "bfdfe7dc352907fc980b868725387e98cd232c1e26450bbdc11e5b7180a7fd06"}], "guid": "bfdfe7dc352907fc980b868725387e988fa10fb298a4756cbbdad632d3530b79", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e985bc62ba05b050c4aa58a6988afbb3b30", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981f0a8508efd61386103314ddbb82a530", "name": "FirebaseAppCheckInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e988e935c81efc4686179f554b8fe37864a", "name": "FirebaseAuthInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e9863397a73468de8eb61b23cd9f8f91a11", "name": "FirebaseMessagingInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift"}, {"guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher"}], "guid": "bfdfe7dc352907fc980b868725387e98141d7db37e1ba5fe325a4a19cf71e38c", "name": "FirebaseFunctions", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98422c2b3f76d0bdf719933c892e25e1fa", "name": "FirebaseFunctions.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}
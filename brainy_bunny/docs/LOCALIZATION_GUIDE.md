# Brainy Bunny - Localization Guide

## Overview

This guide provides complete instructions for translating Brainy Bunny into 17 languages beyond English. The app uses <PERSON><PERSON>ter's internationalization (l10n) system with ARB (Application Resource Bundle) files.

**Total Strings to Translate:** ~150 unique strings
**Target Languages:** 17
**Total Translation Work:** ~2,550 strings
**Estimated Professional Translation Cost:** €2,000-€4,000

**Last Updated:** January 2025
**Version:** 1.0

---

## Table of Contents

1. [Languages & Priorities](#languages--priorities)
2. [Translation Workflow](#translation-workflow)
3. [String Categories](#string-categories)
4. [Translation Guidelines](#translation-guidelines)
5. [Technical Instructions](#technical-instructions)
6. [Quality Assurance](#quality-assurance)
7. [Professional Translation Services](#professional-translation-services)

---

## Languages & Priorities

### Tier 1: High Priority (Launch Critical)

These languages represent the largest markets for children's educational apps:

| Language | Code | Market Size | Priority |
|----------|------|-------------|----------|
| **Spanish (Spain)** | `es` | 47M speakers | 🔴 High |
| **Spanish (Mexico)** | `es_MX` | 130M speakers | 🔴 High |
| **French (France)** | `fr` | 67M speakers | 🔴 High |
| **German** | `de` | 95M speakers | 🔴 High |
| **Portuguese (Brazil)** | `pt_BR` | 215M speakers | 🔴 High |
| **Italian** | `it` | 60M speakers | 🔴 High |

**Recommendation:** Translate these 6 languages first for initial launch.

### Tier 2: Medium Priority (Post-Launch)

| Language | Code | Market Size | Priority |
|----------|------|-------------|----------|
| **Chinese (Simplified)** | `zh_Hans` | 1.1B speakers | 🟡 Medium |
| **Japanese** | `ja` | 125M speakers | 🟡 Medium |
| **Korean** | `ko` | 77M speakers | 🟡 Medium |
| **Russian** | `ru` | 150M speakers | 🟡 Medium |
| **Arabic** | `ar` | 420M speakers | 🟡 Medium |
| **Hindi** | `hi` | 600M speakers | 🟡 Medium |

**Recommendation:** Add 3-6 months post-launch based on user demand.

### Tier 3: Lower Priority

| Language | Code | Market Size | Priority |
|----------|------|-------------|----------|
| **Portuguese (Portugal)** | `pt` | 10M speakers | 🟢 Low |
| **French (Canada)** | `fr_CA` | 7M speakers | 🟢 Low |
| **Chinese (Traditional)** | `zh_Hant` | 23M speakers | 🟢 Low |
| **Catalan** | `ca` | 10M speakers | 🟢 Low |
| **Indonesian** | `id` | 275M speakers | 🟢 Low |

**Recommendation:** Add only if specific market opportunity identified.

---

## Translation Workflow

### Option 1: Professional Translation Service (Recommended)

**Best for:** High-quality, launch-critical translations

**Process:**
1. Export English ARB file (`app_en.arb`)
2. Send to professional translation service (see recommendations below)
3. Request translation for Tier 1 languages (6 languages)
4. Receive translated ARB files
5. Review and test translations
6. Generate Flutter localization code
7. Test in-app

**Timeline:** 2-3 weeks
**Cost:** €300-€700 per language = €1,800-€4,200 total (Tier 1)

### Option 2: Community Translation

**Best for:** Tier 2/3 languages, post-launch additions

**Process:**
1. Set up Crowdin or Lokalise project
2. Invite community translators
3. Review submissions
4. Import to ARB files
5. Generate and test

**Timeline:** 4-8 weeks
**Cost:** Free to €500 (for review/QA)

### Option 3: Machine Translation + Human Review

**Best for:** Quick draft, then professional refinement

**Process:**
1. Use Google Translate API or DeepL for initial translation
2. Export to ARB files
3. Hire native speaker for review and corrections
4. Test and refine

**Timeline:** 1-2 weeks
**Cost:** €100-€300 per language

---

## String Categories

### 1. Onboarding Strings (54 strings)

**Context:** First-time user experience, critical for conversion

**Categories:**
- Welcome screen (3 strings)
- Name/age input (6 strings)
- Educational philosophy (4 strings)
- Demo games (25 strings - titles, context, science)
- Badges (5 strings)
- Summary screen (6 strings)
- Trust/social proof (5 strings)

**Translation Notes:**
- Warm, welcoming tone
- Parent-focused (not child-focused)
- Emphasize AAP backing and scientific research
- Keep badge names short and exciting

**Example:**
```json
{
  "onboarding_welcome_headline": "Help Your Child Learn & Grow",
  "demo_game_1_title": "Animal Shapes",
  "badge_shape_detective": "Shape Detective"
}
```

### 2. Paywall Strings (24 strings)

**Context:** Conversion-critical, must be compelling

**Categories:**
- Value propositions (8 strings)
- Plan details (9 strings)
- Trial messaging (4 strings)
- Guarantee (3 strings)

**Translation Notes:**
- Preserve urgency and emotional appeal
- Currency symbols must be localized (€ vs $ vs £)
- Savings percentages stay the same
- Trial duration (7 days) is fixed

**Example:**
```json
{
  "paywall_value_1_title": "15 Premium Games",
  "paywall_trial_badge": "7-Day FREE Trial",
  "paywall_plan_yearly_savings": "Save 58% compared to monthly"
}
```

### 3. Game UI Strings (18 strings)

**Context:** In-game interface, child-friendly

**Categories:**
- Game titles (15 games)
- Instructions (3 strings)

**Translation Notes:**
- Simple, child-appropriate language
- Action-oriented
- Keep very short (2-4 words)

**Example:**
```json
{
  "game_animal_title": "Animal Friends",
  "game_instruction_drag": "Drag to match!"
}
```

### 4. Settings & UI Strings (22 strings)

**Context:** Parent controls, account management

**Categories:**
- Settings menu (8 strings)
- Subscription management (6 strings)
- About/legal (4 strings)
- Buttons/actions (4 strings)

**Translation Notes:**
- Formal, clear tone
- Consistent button text across app
- Legal terms may need legal review

**Example:**
```json
{
  "settings_restore_purchases": "Restore Purchases",
  "settings_privacy_policy": "Privacy Policy",
  "button_continue": "Continue"
}
```

### 5. Error Messages (14 strings)

**Context:** Technical errors, must be clear

**Translation Notes:**
- Clear, non-technical language
- Provide actionable guidance
- Empathetic tone

**Example:**
```json
{
  "error_network": "Connection error. Please check your internet.",
  "error_purchase_failed": "Purchase could not be completed. Please try again."
}
```

### 6. Accessibility Strings (18 strings)

**Context:** Screen reader support, accessibility labels

**Translation Notes:**
- Descriptive, not conversational
- Explain what element does
- Must be tested with VoiceOver/TalkBack

**Example:**
```json
{
  "accessibility_back_button": "Go back to previous screen",
  "accessibility_game_locked": "Premium game, requires subscription to unlock"
}
```

---

## Translation Guidelines

### General Principles

1. **Context is Key**
   - Review where string appears in app (use screenshots)
   - Understand target audience (parents of 2-5 year olds)
   - Consider cultural context

2. **Tone & Voice**
   - Onboarding: Warm, supportive, encouraging
   - Paywall: Confident, value-focused, urgent
   - Games: Fun, exciting, child-appropriate
   - Settings: Clear, professional, helpful
   - Errors: Empathetic, actionable, non-blaming

3. **Length Constraints**
   - Button text: Max 15-20 characters
   - Headlines: Max 50 characters
   - Badge names: Max 20 characters
   - Body text: Can be longer, but avoid walls of text

4. **Gender Neutrality**
   - Use gender-neutral language where possible
   - Child should be gender-neutral ("your child" not "your son/daughter")
   - Parent should be gender-neutral

5. **Cultural Adaptation**
   - Adjust examples if culturally inappropriate
   - Change currency symbols (€ → $ → £ → ¥)
   - Adapt measurement units if needed
   - Keep AAP (American Academy of Pediatrics) reference but add local equivalent if exists

### Specific Translation Challenges

#### 1. Placeholders

Some strings contain placeholders like `{name}` or `{age}`:

```json
"summary_message_age_3": "Great job, {parentName}! Your {age}-year-old is ready to explore!"
```

**Rules:**
- Keep placeholders exactly as-is: `{parentName}`, `{age}`
- Reorder sentence structure as needed for grammar
- Don't translate the placeholder names

**Example (Spanish):**
```json
"summary_message_age_3": "¡Excelente trabajo, {parentName}! ¡Tu hijo/a de {age} años está listo/a para explorar!"
```

#### 2. Pluralization

Some strings have plural forms:

```json
"games_unlocked": "{count, plural, =0{No games} =1{1 game} other{{count} games}} unlocked"
```

**Rules:**
- Follow Flutter's ICU message format
- Adjust plural rules for target language
- Some languages have more than 2 forms (Arabic: 0, 1, 2, 3-10, 11+)

#### 3. Markdown Formatting

Some strings contain markdown:

```json
"paywall_guarantee_text": "Try **risk-free** for 7 days. Cancel anytime."
```

**Rules:**
- Keep `**bold**` markers in place
- Keep `_italic_` markers in place
- Don't translate markdown syntax

#### 4. Brand Names

These should NOT be translated:
- Brainy Bunny (app name)
- AAP (American Academy of Pediatrics) - keep acronym
- RevenueCat, Adjust, Firebase (technical terms)

#### 5. Currency & Pricing

**For paywall strings:**
- Replace € with local currency symbol
- Adjust prices to local equivalents (consult pricing sheet)
- Keep discount percentages the same (58% stays 58%)

**Example pricing conversions:**
- €4.99 = $5.49 (US) = £4.49 (UK) = ¥700 (Japan)
- €8.99 = $9.99 (US) = £7.99 (UK) = ¥1,200 (Japan)
- €44.99 = $49.99 (US) = £39.99 (UK) = ¥6,000 (Japan)

---

## Technical Instructions

### File Structure

All localization files are in `/lib/l10n/` directory:

```
lib/l10n/
├── app_en.arb          # English (source)
├── app_es.arb          # Spanish (Spain)
├── app_es_MX.arb       # Spanish (Mexico)
├── app_fr.arb          # French (France)
├── app_fr_CA.arb       # French (Canada)
├── app_de.arb          # German
├── app_pt.arb          # Portuguese (Portugal)
├── app_pt_BR.arb       # Portuguese (Brazil)
├── app_it.arb          # Italian
├── app_ar.arb          # Arabic
├── app_zh_Hans.arb     # Chinese (Simplified)
├── app_zh_Hant.arb     # Chinese (Traditional)
├── app_zh.arb          # Chinese (Generic)
├── app_hi.arb          # Hindi
├── app_ja.arb          # Japanese
├── app_ko.arb          # Korean
├── app_ru.arb          # Russian
├── app_id.arb          # Indonesian
└── app_ca.arb          # Catalan
```

### ARB File Format

ARB files are JSON with special metadata fields:

```json
{
  "@@locale": "es",

  "onboarding_welcome_headline": "Ayuda a tu hijo a aprender y crecer",
  "@onboarding_welcome_headline": {
    "description": "Main headline on welcome screen",
    "context": "Shown to parents on first app launch",
    "maxLength": 50
  },

  "summary_message_age_3": "¡Excelente trabajo, {parentName}! ¡Tu hijo/a de {age} años está listo/a para explorar!",
  "@summary_message_age_3": {
    "description": "Personalized message for 3-year-olds",
    "placeholders": {
      "parentName": {
        "type": "String"
      },
      "age": {
        "type": "int"
      }
    }
  }
}
```

**Key Points:**
- First line: `"@@locale": "xx"` (language code)
- Regular strings: `"key": "translated value"`
- Metadata: `"@key": { ... }` (optional but helpful)

### Adding a New Translation

**Step 1: Copy English file**
```bash
cp lib/l10n/app_en.arb lib/l10n/app_es.arb
```

**Step 2: Update locale**
```json
{
  "@@locale": "es",
  ...
}
```

**Step 3: Translate all values**
- Keep all keys the same (English)
- Translate all values to target language
- Keep metadata (@key sections) in English

**Step 4: Generate localization code**
```bash
flutter gen-l10n
```

**Step 5: Test in app**
```bash
# Change device language to Spanish in iOS Settings
flutter run
```

### Testing Translations

**1. Visual Testing:**
- Change device language
- Run app
- Go through entire onboarding flow
- Check for:
  - Text overflow (too long)
  - Line breaks in wrong places
  - Missing translations (shows English)
  - Formatting errors

**2. String Length Testing:**

Create a test file to check lengths:

```dart
void testStringLengths() {
  final l10n = AppLocalizations.of(context)!;

  // Button text should be short
  assert(l10n.button_continue.length <= 20);
  assert(l10n.button_back.length <= 15);

  // Headlines should fit on screen
  assert(l10n.onboarding_welcome_headline.length <= 50);
}
```

**3. Placeholder Testing:**

Verify placeholders work:

```dart
final message = l10n.summary_message_age_3
    .replaceAll('{parentName}', 'Maria')
    .replaceAll('{age}', '3');
// Should output: "¡Excelente trabajo, Maria! ..."
```

**4. RTL Testing (Arabic, Hebrew):**

For right-to-left languages:
- Check text alignment (right-aligned)
- Check icon placement (flipped)
- Check navigation (back button on right)

### Common Technical Issues

**Issue 1: Syntax error in ARB file**
```
Error: FormatException: Unexpected character
```
**Fix:** Check for:
- Missing commas
- Unescaped quotes (use `\"` inside strings)
- Missing curly braces

**Issue 2: Placeholder not working**
```
Text shows: "Welcome {parentName}" instead of "Welcome Maria"
```
**Fix:** Ensure placeholder metadata is correct:
```json
"@key": {
  "placeholders": {
    "parentName": {
      "type": "String"
    }
  }
}
```

**Issue 3: Translation not showing**
```
App still shows English despite translation
```
**Fix:**
1. Regenerate localization: `flutter gen-l10n`
2. Restart app
3. Verify device language is correct
4. Check `l10n.yaml` includes your language

---

## Quality Assurance

### Translation Review Checklist

For each language, verify:

- [ ] All 150 strings translated (no English left)
- [ ] Placeholders preserved: `{name}`, `{age}`, etc.
- [ ] Markdown formatting kept: `**bold**`, `_italic_`
- [ ] Currency symbols localized
- [ ] Tone appropriate for context
- [ ] Grammar correct
- [ ] Spelling correct (use spell checker)
- [ ] Cultural sensitivity reviewed
- [ ] String lengths appropriate (no overflow)
- [ ] Tested in-app on device

### Native Speaker Review

**Highly Recommended:** Have a native speaker review translations before launch.

**What to ask reviewer:**
1. Is the tone appropriate for parents?
2. Is the language natural (not "Google Translate" awkward)?
3. Are there any cultural issues?
4. Is the child-focused language age-appropriate?
5. Do prices/currency make sense?
6. Are there any spelling/grammar errors?

**Compensation:** €50-€100 per language for 1-2 hour review

---

## Professional Translation Services

### Recommended Services

**1. Lokalise**
- Website: lokalise.com
- Specialization: Mobile app localization
- Features: Translation memory, context screenshots, collaboration
- Pricing: ~€0.10-€0.20 per word
- Turnaround: 5-7 days per language
- Quality: High (native speakers, reviewed)

**2. Crowdin**
- Website: crowdin.com
- Specialization: Software localization
- Features: Machine translation + human review, context
- Pricing: ~€0.08-€0.15 per word
- Turnaround: 7-10 days per language
- Quality: Good (community + professional)

**3. Gengo**
- Website: gengo.com
- Specialization: Fast, affordable translation
- Pricing: ~€0.05-€0.12 per word
- Turnaround: 2-4 days per language
- Quality: Good (tested translators)

**4. TextMaster**
- Website: textmaster.com
- Specialization: Marketing content
- Pricing: ~€0.068-€0.10 per word
- Turnaround: 3-5 days per language
- Quality: High (marketing-focused)

### Budget Estimate

**Per Language:**
- 150 strings × ~8 words/string = 1,200 words
- 1,200 words × €0.10/word = €120 per language

**Tier 1 (6 languages):**
- €120 × 6 = €720 + review (€300) = **€1,020 total**

**All Languages (17 languages):**
- €120 × 17 = €2,040 + review (€800) = **€2,840 total**

### DIY Translation (Machine + Review)

**Tools:**
- DeepL API: €20/month for 1M characters
- Google Translate API: Free tier sufficient
- Grammarly: For English source quality check

**Process:**
1. Clean up English source (fix typos, improve clarity)
2. Use DeepL for initial translation (best quality)
3. Export to ARB format
4. Hire native speaker on Upwork for review (€50-€100)
5. Implement corrections
6. Test in-app

**Total Cost per Language:** €50-€120
**Total Time per Language:** 3-5 days

---

## Launch Strategy

### Phased Rollout

**Phase 1: English-Only Launch (Month 1)**
- Launch in English-speaking markets
- Gather user feedback
- Validate conversion funnel
- Monitor for string errors/issues

**Phase 2: Tier 1 Languages (Month 2-3)**
- Add 6 high-priority languages
- Soft launch in those markets
- A/B test localized vs English onboarding
- Measure conversion impact

**Phase 3: Tier 2 Languages (Month 4-6)**
- Add based on user demand
- Priority: Chinese, Japanese, Russian
- Focus on markets with highest organic traffic

**Phase 4: Tier 3 Languages (Month 7+)**
- Only if specific opportunity identified
- Low investment, opportunistic

---

## Maintenance & Updates

### When to Update Translations

- **New features added:** Translate new strings immediately
- **String changes:** Update all languages
- **User reports errors:** Fix within 1 week
- **Seasonal content:** Translate 1 month before launch

### Version Control

Track translation versions:

```json
{
  "@@locale": "es",
  "@@version": "1.2",
  "@@last_modified": "2025-01-15"
}
```

### Translation Memory

Build translation memory to ensure consistency:

| English | Spanish | French | German |
|---------|---------|--------|--------|
| Continue | Continuar | Continuer | Weiter |
| Back | Atrás | Retour | Zurück |
| Premium | Premium | Premium | Premium |
| Free Trial | Prueba Gratuita | Essai Gratuit | Kostenlose Testversion |

---

## Contact & Resources

- **Localization Lead:** [Your Name]
- **Translation Issues:** GitHub Issues with `localization` tag
- **Flutter l10n Docs:** https://docs.flutter.dev/ui/accessibility-and-internationalization/internationalization
- **ARB Format Spec:** https://github.com/google/app-resource-bundle

---

**End of Localization Guide**

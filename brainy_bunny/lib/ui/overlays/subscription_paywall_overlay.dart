// lib/ui/overlays/subscription_paywall_overlay.dart
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flame/game.dart';
import '../../l10n/app_localizations.dart';
import 'package:brainy_bunny/constants/app_constants.dart';
import 'package:brainy_bunny/constants/subscription_constants.dart';
import 'package:brainy_bunny/services/purchase_manager.dart';
import 'package:brainy_bunny/services/subscription_manager.dart';

/// Multi-step subscription paywall overlay for in-game purchases
/// Shows the same offering as onboarding (3 steps with trial)
class SubscriptionPaywallOverlay extends StatefulWidget {
  final GameWidget gameWidget;

  const SubscriptionPaywallOverlay({
    super.key,
    required this.gameWidget,
  });

  @override
  State<SubscriptionPaywallOverlay> createState() => _SubscriptionPaywallOverlayState();
}

class _SubscriptionPaywallOverlayState extends State<SubscriptionPaywallOverlay> {
  int _currentStep = 1; // 1, 2, or 3
  String _selectedPlan = SubscriptionConstants.SUBSCRIPTION_YEARLY;
  final SubscriptionManager _subscriptionManager = SubscriptionManager();

  void _closeOverlay() {
    final game = widget.gameWidget.game;
    if (game != null && game.overlays.isActive(AppConstants.PURCHASE_OFFER_OVERLAY)) {
      game.overlays.remove(AppConstants.PURCHASE_OFFER_OVERLAY);
    }
  }

  void _goToNextStep() {
    if (_currentStep < 3) {
      setState(() {
        _currentStep++;
      });
    }
  }

  void _goToPreviousStep() {
    if (_currentStep > 1) {
      setState(() {
        _currentStep--;
      });
    }
  }

  void _showParentalGate() {
    final game = widget.gameWidget.game;
    if (game != null) {
      // Close paywall first
      _closeOverlay();

      // Show parental gate
      Future.delayed(const Duration(milliseconds: 300), () {
        game.overlays.add(AppConstants.AGE_VERIFICATION_OVERLAY);
      });
    }
  }

  Future<void> _restorePurchases() async {
    try {
      if (kDebugMode) {
        print('🔄 Restore purchases initiated from in-game paywall');
      }

      final purchaseManager = PurchaseManager();
      await purchaseManager.restorePurchases();

      // Check if purchase was restored
      if (purchaseManager.isPurchased) {
        if (kDebugMode) {
          print('✅ Purchases restored successfully');
        }
        // Close paywall since purchase is now active
        _closeOverlay();
      } else {
        if (kDebugMode) {
          print('ℹ️ No previous purchases found');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error restoring purchases: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.black.withValues(alpha: 0.8),
      child: Center(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.95,
          height: MediaQuery.of(context).size.height * 0.95,
          margin: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(24),
            child: _buildCurrentStep(),
          ),
        ),
      ),
    );
  }

  Widget _buildCurrentStep() {
    switch (_currentStep) {
      case 1:
        return _buildStep1();
      case 2:
        return _buildStep2();
      case 3:
        return _buildStep3();
      default:
        return _buildStep1();
    }
  }

  // Step 1: Value Proposition
  Widget _buildStep1() {
    final l10n = AppLocalizations.of(context)!;

    return Column(
      children: [
        // Header with close button
        _buildHeader(showSkip: true),

        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Column(
              children: [
                // Headline
                Text(
                  l10n.paywall_step1_headline,
                  style: const TextStyle(
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1E3A5F),
                    height: 1.2,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 32),

                // Benefits grid (2 columns for landscape)
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Column(
                        children: [
                          _buildBenefit(
                            Icons.games,
                            l10n.paywall_benefit_all_games(AppConstants.TOTAL_GAMES_COUNT),
                            Colors.blue.shade600,
                          ),
                          const SizedBox(height: 20),
                          _buildBenefit(
                            Icons.child_care,
                            l10n.paywall_benefit_age_appropriate,
                            Colors.green.shade600,
                          ),
                          const SizedBox(height: 20),
                          _buildBenefit(
                            Icons.insights,
                            l10n.paywall_benefit_progress_tracking,
                            Colors.purple.shade600,
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 20),
                    Expanded(
                      child: Column(
                        children: [
                          _buildBenefit(
                            Icons.devices,
                            l10n.paywall_benefit_offline_play,
                            Colors.orange.shade600,
                          ),
                          const SizedBox(height: 20),
                          _buildBenefit(
                            Icons.block,
                            l10n.paywall_benefit_no_ads,
                            Colors.red.shade600,
                          ),
                          const SizedBox(height: 20),
                          _buildBenefit(
                            Icons.update,
                            l10n.paywall_benefit_regular_updates,
                            Colors.teal.shade600,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 32),

                // Continue button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _goToNextStep,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue.shade600,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 20),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      elevation: 4,
                    ),
                    child: Text(
                      l10n.continue_button,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // Step 2: Plan Selection with Trial
  Widget _buildStep2() {
    final l10n = AppLocalizations.of(context)!;

    return Column(
      children: [
        _buildHeader(showBack: true, showSkip: true),

        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Column(
              children: [
                // Trial badge
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.green.shade400,
                        Colors.green.shade600,
                      ],
                    ),
                    borderRadius: BorderRadius.circular(24),
                  ),
                  child: Text(
                    l10n.paywall_trial_badge,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),

                const SizedBox(height: 24),

                // Headline
                Text(
                  l10n.paywall_step2_headline,
                  style: const TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1E3A5F),
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 24),

                // Plan options (horizontal for landscape)
                Row(
                  children: [
                    Expanded(
                      child: _buildPlanCard(
                        isSelected: _selectedPlan == SubscriptionConstants.SUBSCRIPTION_YEARLY,
                        onTap: () => setState(() => _selectedPlan = SubscriptionConstants.SUBSCRIPTION_YEARLY),
                        badge: l10n.paywall_plan_best_value,
                        badgeColor: Colors.amber.shade600,
                        title: l10n.paywall_plan_yearly_title,
                        price: _subscriptionManager.yearlyProduct?.price ?? SubscriptionConstants.FALLBACK_YEARLY_PRICE,
                        period: l10n.paywall_plan_yearly_period,
                        perMonth: l10n.paywall_plan_yearly_per_month,
                        savings: l10n.paywall_plan_yearly_savings,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildPlanCard(
                        isSelected: _selectedPlan == SubscriptionConstants.SUBSCRIPTION_MONTHLY,
                        onTap: () => setState(() => _selectedPlan = SubscriptionConstants.SUBSCRIPTION_MONTHLY),
                        title: l10n.paywall_plan_monthly_title,
                        price: _subscriptionManager.monthlyProduct?.price ?? SubscriptionConstants.FALLBACK_MONTHLY_PRICE,
                        period: l10n.paywall_plan_monthly_period,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildPlanCard(
                        isSelected: _selectedPlan == SubscriptionConstants.SUBSCRIPTION_WEEKLY,
                        onTap: () => setState(() => _selectedPlan = SubscriptionConstants.SUBSCRIPTION_WEEKLY),
                        title: l10n.paywall_plan_weekly_title,
                        price: _subscriptionManager.weeklyProduct?.price ?? SubscriptionConstants.FALLBACK_WEEKLY_PRICE,
                        period: l10n.paywall_plan_weekly_period,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 24),

                // Start Trial button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _showParentalGate,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue.shade600,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 20),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      elevation: 4,
                    ),
                    child: Text(
                      l10n.paywall_start_trial,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Restore Purchase Link
                Center(
                  child: TextButton(
                    onPressed: _restorePurchases,
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    ),
                    child: Text(
                      AppLocalizations.of(context)!.paywall_restore,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.blue.shade700,
                        fontWeight: FontWeight.w500,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // Step 3: Final Confirmation
  Widget _buildStep3() {
    final l10n = AppLocalizations.of(context)!;

    return Column(
      children: [
        _buildHeader(showBack: true, showSkip: true),

        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Column(
              children: [
                // Urgency indicator
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 12,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.red.shade50,
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: Colors.red.shade300,
                      width: 2,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.schedule,
                        color: Colors.red.shade700,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        l10n.paywall_urgency_text,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.red.shade700,
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 24),

                Text(
                  l10n.paywall_step3_headline,
                  style: const TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF1E3A5F),
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 32),

                // Final benefits summary
                _buildFinalBenefit(Icons.star, l10n.paywall_step3_benefit_1),
                const SizedBox(height: 16),
                _buildFinalBenefit(Icons.workspace_premium, l10n.paywall_step3_benefit_2),
                const SizedBox(height: 16),
                _buildFinalBenefit(Icons.family_restroom, l10n.paywall_step3_benefit_3),

                const SizedBox(height: 32),

                // Subscribe button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _showParentalGate,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green.shade600,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 20),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      elevation: 4,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.lock_open, size: 24),
                        const SizedBox(width: 12),
                        Text(
                          l10n.paywall_subscribe_button,
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Restore Purchase Link
                Center(
                  child: TextButton(
                    onPressed: _restorePurchases,
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    ),
                    child: Text(
                      l10n.paywall_restore,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.blue.shade700,
                        fontWeight: FontWeight.w500,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildHeader({bool showBack = false, bool showSkip = false}) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.shade200,
            width: 1,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          if (showBack)
            IconButton(
              onPressed: _goToPreviousStep,
              icon: const Icon(Icons.arrow_back),
            )
          else
            const SizedBox(width: 48),

          Text(
            'Step $_currentStep of 3', // TODO: Add to localization if needed
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade700,
            ),
          ),

          if (showSkip)
            TextButton(
              onPressed: _closeOverlay,
              child: Text(
                AppLocalizations.of(context)!.skip_button,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey.shade600,
                ),
              ),
            )
          else
            const SizedBox(width: 48),
        ],
      ),
    );
  }

  Widget _buildBenefit(IconData icon, String text, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 2,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Colors.grey.shade800,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPlanCard({
    required bool isSelected,
    required VoidCallback onTap,
    String? badge,
    Color? badgeColor,
    required String title,
    required String price,
    required String period,
    String? perMonth,
    String? savings,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? Colors.blue.shade50 : Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? Colors.blue.shade600 : Colors.grey.shade300,
            width: isSelected ? 3 : 2,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (badge != null) ...[
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: badgeColor ?? Colors.blue,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  badge,
                  style: const TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
              const SizedBox(height: 8),
            ],
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Color(0xFF1E3A5F),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              price,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.blue.shade600,
              ),
            ),
            Text(
              period,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
              ),
            ),
            if (perMonth != null) ...[
              const SizedBox(height: 4),
              Text(
                perMonth,
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
            if (savings != null) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.green.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  savings,
                  style: TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.w600,
                    color: Colors.green.shade700,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildFinalBenefit(IconData icon, String text) {
    return Row(
      children: [
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: Colors.green.shade100,
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: Colors.green.shade700,
            size: 24,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Text(
            text,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF1E3A5F),
            ),
          ),
        ),
      ],
    );
  }
}

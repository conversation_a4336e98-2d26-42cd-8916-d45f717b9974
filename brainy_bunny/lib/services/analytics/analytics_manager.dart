// lib/services/analytics/analytics_manager.dart
import 'package:flutter/foundation.dart';
import 'package:brainy_bunny/services/analytics/adjust_service.dart';
import 'package:brainy_bunny/services/analytics/revenuecat_service.dart';
import 'package:brainy_bunny/services/analytics/meta_service.dart';
import 'package:brainy_bunny/constants/app_constants.dart';

/// Unified analytics manager that aggregates all analytics services
/// Provides single methods for each business event that call appropriate services
class AnalyticsManager {
  // Singleton pattern
  static final AnalyticsManager _instance = AnalyticsManager._internal();
  static AnalyticsManager get instance => _instance;
  AnalyticsManager._internal();

  bool _isInitialized = false;
  bool _debugMode = kDebugMode;

  // Event history for debug mode
  final List<Map<String, dynamic>> _eventHistory = [];
  static const int MAX_HISTORY = 50;

  bool get isInitialized => _isInitialized;
  List<Map<String, dynamic>> get eventHistory => List.unmodifiable(_eventHistory);

  /// Initialize all analytics services
  /// [revenueCatApiKey] - RevenueCat API key (required)
  Future<void> initialize({required String revenueCatApiKey}) async {
    if (_isInitialized) {
      if (_debugMode) {
        print('⚠️ AnalyticsManager already initialized');
      }
      return;
    }

    try {
      if (_debugMode) {
        print('📊 === INITIALIZING ANALYTICS MANAGER ===');
      }

      // Initialize all analytics services
      await Future.wait([
        AdjustService.instance.initialize(),
        RevenueCatService.instance.initialize(apiKey: revenueCatApiKey),
        MetaService.instance.initialize(),
      ]);

      _isInitialized = true;

      if (_debugMode) {
        print('✅ AnalyticsManager initialized successfully');
        print('   - Adjust: ${AdjustService.instance.isInitialized}');
        print('   - RevenueCat: ${RevenueCatService.instance.isInitialized}');
        print('   - Meta: ${MetaService.instance.isInitialized}');
      }
    } catch (e, stackTrace) {
      if (_debugMode) {
        print('❌ AnalyticsManager initialization failed: $e');
        print('Stack trace: $stackTrace');
      }
      // Don't rethrow - app should work without analytics
    }
  }

  // ========== App Lifecycle Events ==========

  /// Track app installation
  Future<void> trackAppInstall() async {
    await _trackEvent(
      eventName: AppConstants.EVENT_APP_INSTALLED,
      adjustToken: 'app_install', // TODO: Add actual Adjust token
      metaEvent: null, // Meta tracks this automatically
    );
  }

  // ========== Onboarding Events ==========

  /// Track onboarding start
  Future<void> trackOnboardingStarted() async {
    await _trackEvent(
      eventName: AppConstants.EVENT_ONBOARDING_STARTED,
      adjustToken: 'onboarding_start', // TODO: Add actual Adjust token
      metaEvent: null,
    );
  }

  /// Track onboarding screen view
  Future<void> trackOnboardingScreenView(String screenName) async {
    await _trackEvent(
      eventName: AppConstants.EVENT_ONBOARDING_SCREEN_VIEW,
      adjustToken: 'onboarding_screen', // TODO: Add actual Adjust token
      parameters: {AppConstants.PARAM_SCREEN_NAME: screenName},
    );
  }

  /// Track onboarding completion
  Future<void> trackOnboardingCompleted() async {
    await _trackEvent(
      eventName: AppConstants.EVENT_ONBOARDING_COMPLETED,
      adjustToken: 'onboarding_complete', // TODO: Add actual Adjust token
      metaEvent: () => MetaService.instance.logCompleteRegistration(),
    );
  }

  // ========== Game Events ==========

  /// Track game played
  Future<void> trackGamePlayed({required int gameId, required String gameName}) async {
    await _trackEvent(
      eventName: AppConstants.EVENT_GAME_PLAYED,
      adjustToken: 'game_played', // TODO: Add actual Adjust token
      parameters: {
        AppConstants.PARAM_GAME_ID: gameId.toString(),
        AppConstants.PARAM_GAME_NAME: gameName,
      },
    );
  }

  /// Track demo game played (during onboarding)
  Future<void> trackDemoGamePlayed({required int gameIndex}) async {
    await _trackEvent(
      eventName: AppConstants.EVENT_DEMO_GAME_PLAYED,
      adjustToken: 'demo_game_played', // TODO: Add actual Adjust token
      parameters: {
        AppConstants.PARAM_DEMO_GAME_INDEX: gameIndex.toString(),
      },
    );
  }

  /// Track game completion
  Future<void> trackGameCompleted({required int gameId, required String gameName}) async {
    await _trackEvent(
      eventName: AppConstants.EVENT_GAME_COMPLETED,
      adjustToken: 'game_completed', // TODO: Add actual Adjust token
      parameters: {
        AppConstants.PARAM_GAME_ID: gameId.toString(),
        AppConstants.PARAM_GAME_NAME: gameName,
      },
    );
  }

  // ========== Paywall and Purchase Events ==========

  /// Track paywall viewed
  Future<void> trackPaywallViewed() async {
    await _trackEvent(
      eventName: AppConstants.EVENT_PAYWALL_VIEWED,
      adjustToken: 'paywall_viewed', // TODO: Add actual Adjust token
      metaEvent: null,
    );
  }

  /// Track subscription selected
  Future<void> trackSubscriptionSelected({
    required String subscriptionId,
    required String tier,
  }) async {
    await _trackEvent(
      eventName: AppConstants.EVENT_SUBSCRIPTION_SELECTED,
      adjustToken: 'subscription_selected', // TODO: Add actual Adjust token
      parameters: {
        AppConstants.PARAM_SUBSCRIPTION_ID: subscriptionId,
        AppConstants.PARAM_SUBSCRIPTION_TIER: tier,
      },
    );
  }

  /// Track trial started
  Future<void> trackTrialStarted({
    required String subscriptionId,
    double? price,
    String? currency,
  }) async {
    Map<String, String> parameters = {
      AppConstants.PARAM_SUBSCRIPTION_ID: subscriptionId,
    };

    if (price != null && currency != null) {
      parameters[AppConstants.PARAM_PRICE] = price.toString();
      parameters[AppConstants.PARAM_CURRENCY] = currency;
    }

    await _trackEvent(
      eventName: AppConstants.EVENT_TRIAL_STARTED,
      adjustToken: 'trial_started', // TODO: Add actual Adjust token
      parameters: parameters,
      metaEvent: () => MetaService.instance.logStartTrial(
        subscriptionId: subscriptionId,
        price: price,
        currency: currency,
      ),
    );
  }

  /// Track subscription purchased
  Future<void> trackSubscriptionPurchased({
    required String productId,
    required double price,
    required String currency,
    bool isTrialConversion = false,
  }) async {
    await _trackEvent(
      eventName: AppConstants.EVENT_SUBSCRIPTION_PURCHASED,
      adjustToken: 'subscription_purchased', // TODO: Add actual Adjust token
      parameters: {
        AppConstants.PARAM_SUBSCRIPTION_ID: productId,
        AppConstants.PARAM_PRICE: price.toString(),
        AppConstants.PARAM_CURRENCY: currency,
        'is_trial_conversion': isTrialConversion.toString(),
      },
      revenueEvent: () => AdjustService.instance.trackRevenue(
        'subscription_purchased',
        revenue: price,
        currency: currency,
        parameters: {
          AppConstants.PARAM_SUBSCRIPTION_ID: productId,
        },
      ),
      metaEvent: () => MetaService.instance.logSubscribe(
        value: price,
        currency: currency,
        subscriptionId: productId,
      ),
    );
  }

  /// Track restore purchases
  Future<void> trackRestorePurchases({bool success = true}) async {
    await _trackEvent(
      eventName: AppConstants.EVENT_RESTORE_PURCHASES,
      adjustToken: 'restore_purchases', // TODO: Add actual Adjust token
      parameters: {'success': success.toString()},
    );
  }

  // ========== Error Events ==========

  /// Track purchase failed
  Future<void> trackPurchaseFailed({
    required String errorCode,
    required String errorMessage,
  }) async {
    await _trackEvent(
      eventName: AppConstants.EVENT_PURCHASE_FAILED,
      adjustToken: 'purchase_failed', // TODO: Add actual Adjust token
      parameters: {
        AppConstants.PARAM_ERROR_CODE: errorCode,
        AppConstants.PARAM_ERROR_MESSAGE: errorMessage,
      },
    );
  }

  // ========== Public Generic Event Tracking ==========

  /// Track a custom event with optional parameters
  Future<void> trackEvent({
    required String eventName,
    Map<String, String>? parameters,
  }) async {
    await _trackEvent(
      eventName: eventName,
      parameters: parameters,
    );
  }

  // ========== Internal Helper Methods ==========

  /// Internal method to track event across all platforms
  Future<void> _trackEvent({
    required String eventName,
    String? adjustToken,
    Map<String, String>? parameters,
    Future<void> Function()? revenueEvent,
    Future<void> Function()? metaEvent,
  }) async {
    if (!_isInitialized) {
      if (_debugMode) {
        print('⚠️ AnalyticsManager not initialized, event will be logged locally only: $eventName');
      }
    }

    // Add to debug history
    if (_debugMode) {
      _addToHistory(eventName, parameters);
    }

    try {
      // Track with Adjust (if token provided)
      if (adjustToken != null && AdjustService.instance.isInitialized) {
        if (revenueEvent != null) {
          await revenueEvent();
        } else {
          await AdjustService.instance.trackEvent(adjustToken, parameters: parameters);
        }
      }

      // Track with Meta (if custom event provided)
      if (metaEvent != null && MetaService.instance.isInitialized) {
        await metaEvent();
      }

      if (_debugMode) {
        print('📊 Analytics Event: $eventName');
        if (parameters != null && parameters.isNotEmpty) {
          print('   Parameters: $parameters');
        }
      }
    } catch (e) {
      if (_debugMode) {
        print('❌ Failed to track event $eventName: $e');
      }
    }
  }

  /// Add event to debug history
  void _addToHistory(String eventName, Map<String, String>? parameters) {
    _eventHistory.add({
      'timestamp': DateTime.now().toIso8601String(),
      'event': eventName,
      'parameters': parameters ?? {},
    });

    // Keep only last MAX_HISTORY events
    if (_eventHistory.length > MAX_HISTORY) {
      _eventHistory.removeAt(0);
    }
  }

  /// Set user ID across all platforms
  Future<void> setUserId(String userId) async {
    try {
      await RevenueCatService.instance.setUserId(userId);
      if (_debugMode) {
        print('👤 User ID set: $userId');
      }
    } catch (e) {
      if (_debugMode) {
        print('❌ Failed to set user ID: $e');
      }
    }
  }

  /// Clear event history (for debug mode)
  void clearHistory() {
    _eventHistory.clear();
  }
}

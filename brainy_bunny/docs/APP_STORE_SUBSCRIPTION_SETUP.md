# App Store Connect Subscription Setup Guide

This guide provides step-by-step instructions for creating subscription products in App Store Connect for Brainy Bunny.

## Prerequisites

- Active Apple Developer account
- App created in App Store Connect (Bundle ID: `com.goodkarmalab.brainyBunny`)
- Tax and banking information completed

## Subscription Products

Create the following three auto-renewable subscription products:

### 1. Weekly Subscription

**Product ID:** `brainy_weekly`

**Configuration:**
- Subscription Duration: 1 Week
- Free Trial: 7 Days
- Price: €4.99/week
- Subscription Group: `brainy_premium` (create new group)

**Localized Information (English - United States):**
- Display Name: `Brainy Bunny Premium - Weekly`
- Description: `Full access to all 15 premium educational games with weekly subscription. Cancel anytime.`

**Review Information:**
- Screenshot: Provide screenshot showing premium game access
- Review Notes: `This is a weekly subscription with 7-day free trial. Users can cancel anytime from device settings.`

### 2. Monthly Subscription

**Product ID:** `brainy_monthly`

**Configuration:**
- Subscription Duration: 1 Month
- Free Trial: 7 Days
- Price: €8.99/month
- Subscription Group: `brainy_premium` (same as above)

**Localized Information (English - United States):**
- Display Name: `Brainy Bunny Premium - Monthly`
- Description: `Full access to all 15 premium educational games with monthly subscription. Cancel anytime.`

**Review Information:**
- Screenshot: Provide screenshot showing premium game access
- Review Notes: `This is a monthly subscription with 7-day free trial. Users can cancel anytime from device settings.`

### 3. Yearly Subscription (Recommended)

**Product ID:** `brainy_yearly`

**Configuration:**
- Subscription Duration: 1 Year
- Free Trial: 7 Days
- Price: €44.99/year
- Subscription Group: `brainy_premium` (same as above)

**Localized Information (English - United States):**
- Display Name: `Brainy Bunny Premium - Yearly`
- Description: `Full access to all 15 premium educational games with yearly subscription. Best value - save 58% vs monthly! Cancel anytime.`

**Review Information:**
- Screenshot: Provide screenshot showing premium game access with "Best Value" badge
- Review Notes: `This is our recommended yearly subscription with 7-day free trial. Users save 58% compared to monthly pricing. Cancel anytime from device settings.`

## Subscription Group Settings

**Group Name:** `brainy_premium`

**Group Level Configuration:**
- Ranking: Set yearly as level 1 (highest), monthly as level 2, weekly as level 3
- This ensures proper upgrade/downgrade behavior

## Trial Offer Eligibility

For all three subscriptions:
- Eligible for Introductory Offer: ✅ Yes
- Introductory Offer Type: Free Trial
- Introductory Offer Duration: 7 Days
- Introductory Offer Eligibility: New Subscribers

## Localization (Additional Languages)

Add localized product information for all 18 supported languages:
- Arabic (ar)
- Spanish (es, es-MX)
- Portuguese (pt, pt-BR)
- Korean (ko)
- Chinese Simplified (zh-Hans)
- Chinese Traditional (zh-Hant)
- Hindi (hi)
- Indonesian (id)
- Russian (ru)
- French (fr, fr-CA)
- German (de)
- Japanese (ja)
- Italian (it)
- Catalan (ca)

**Translation Notes:**
- Use professional translation service
- Ensure "cancel anytime" and "7-day free trial" are clearly communicated
- Maintain emotional tone (reassuring, educational focus)

## Subscription Benefits

All three subscriptions provide identical access:
- All 15 premium educational games
- Personalized learning paths
- Detailed progress tracking
- Monthly new content updates
- Ad-free experience
- Offline mode

## RevenueCat Integration

### Step 1: Create RevenueCat Project

1. Go to https://app.revenuecat.com
2. Create new project: "Brainy Bunny"
3. Select platform: iOS
4. Enter Bundle ID: `com.goodkarmalab.brainyBunny`

### Step 2: Configure App Store Connect

1. In RevenueCat dashboard, go to Project Settings → Apple App Store
2. Generate In-App Purchase Key in App Store Connect:
   - Go to Users and Access → Keys → In-App Purchase
   - Click "+" to generate new key
   - Download the .p8 file
   - Copy the Key ID
3. Upload the key to RevenueCat
4. Enter Issuer ID (from App Store Connect Keys page)

### Step 3: Configure Products in RevenueCat

1. Go to Products section in RevenueCat
2. Create three products matching the App Store product IDs:
   - `brainy_weekly`
   - `brainy_monthly`
   - `brainy_yearly`
3. Create entitlement: `premium`
4. Attach all three products to the `premium` entitlement

### Step 4: Get API Keys

1. Go to Project Settings → API Keys
2. Copy the **Public SDK Key** for iOS
3. Add to your app code:
   ```dart
   // In lib/main.dart or initialization file
   static const REVENUECAT_API_KEY = 'appl_xxxxxxxxxxxxx';
   ```

### Step 5: Configure Webhooks (for server verification)

1. Go to Integrations → Webhooks
2. Set up webhook URL for your backend server
3. Configure events to listen for:
   - Initial Purchase
   - Renewal
   - Cancellation
   - Billing Issue
   - Product Change

## Testing

### Sandbox Testing

1. Create sandbox tester account in App Store Connect
2. Sign out of App Store on test device
3. When prompted during purchase, sign in with sandbox account
4. Verify:
   - 7-day trial is offered
   - Trial countdown is accurate
   - Can cancel subscription
   - Can restore purchases
   - Proper product display with localized strings

### Subscription States to Test

- ✅ New subscription with trial
- ✅ Active subscription (post-trial)
- ✅ Cancelled subscription (access until period ends)
- ✅ Expired subscription
- ✅ Billing retry state
- ✅ Upgrade (weekly → monthly → yearly)
- ✅ Downgrade (yearly → monthly → weekly)
- ✅ Restore purchases on new device

## Important Notes

### Trial Period
- Users get ONE 7-day free trial per Apple ID lifetime
- After trial, automatic billing begins
- Users must cancel before trial ends to avoid charge

### Cancellation Policy
- Users can cancel anytime from device Settings → Apple ID → Subscriptions
- Access continues until end of current billing period
- No refunds for partial periods (per App Store policy)

### Price Changes
- Must notify users 30 days before price increase
- Users can opt out before increase takes effect
- Handle via App Store Connect's price change notification system

### Refunds
- Handled through App Store's refund system
- RevenueCat automatically updates entitlement status on refund
- Monitor refund rate in App Store Connect analytics

## Compliance

### GDPR/Privacy
- Include subscription management in Privacy Policy
- Allow users to export subscription data
- Honor deletion requests (cancel subscription + delete account)

### Children's Privacy (COPPA)
- App is targeted at children ages 2-5
- Ensure parental consent for purchases (handled by iOS parental controls)
- Do not collect personal data from children
- All purchase decisions made by parents

### App Store Review Guidelines
- Clearly communicate subscription terms
- Easy cancellation process
- Restore purchases button in app
- No misleading trial offers
- Proper handling of interrupted transactions

## Monitoring

### Key Metrics to Track

1. **Conversion Funnel:**
   - Onboarding start → completion rate
   - Paywall view → trial start rate
   - Trial start → paid conversion rate

2. **Revenue Metrics:**
   - Monthly Recurring Revenue (MRR)
   - Annual Recurring Revenue (ARR)
   - Average Revenue Per User (ARPU)
   - Customer Lifetime Value (LTV)

3. **Retention Metrics:**
   - Trial → paid conversion (target: >40%)
   - Month 1 retention (target: >60%)
   - Month 3 retention (target: >40%)
   - Yearly renewal rate (target: >70%)

4. **Churn Metrics:**
   - Voluntary churn rate
   - Involuntary churn (billing failures)
   - Churn reasons (when available)

### Tools
- RevenueCat Dashboard: Real-time subscription analytics
- App Store Connect: Financial reports, reviews
- Adjust: Attribution and user acquisition
- Meta Events Manager: Campaign performance

## Rollout Strategy

### Phase 1: Soft Launch (Week 1-2)
- Enable subscriptions for 10% of users
- Monitor conversion rates and technical issues
- Collect user feedback
- Fix any critical bugs

### Phase 2: Expanded Rollout (Week 3-4)
- Increase to 50% of users
- A/B test pricing (if needed)
- Optimize paywall copy based on data
- Monitor support requests

### Phase 3: Full Launch (Week 5+)
- Enable for 100% of users
- Launch marketing campaigns
- Monitor all metrics closely
- Iterate based on performance

## Support

### Common User Questions

**Q: How do I cancel my subscription?**
A: Go to Settings → [Your Name] → Subscriptions → Brainy Bunny Premium → Cancel Subscription

**Q: I was charged but my trial should be free?**
A: You won't be charged during your 7-day trial. The charge you see is a pre-authorization that will be billed on day 8 if you don't cancel.

**Q: Can I use my subscription on multiple devices?**
A: Yes, your subscription is tied to your Apple ID and works on all devices signed in with that Apple ID.

**Q: I already paid for the lifetime version. Do I need to subscribe?**
A: No! Existing lifetime users are automatically grandfathered in with permanent premium access.

### Contact Information

- Technical issues: Set up in-app support
- Billing questions: Directed to Apple Support
- General inquiries: <EMAIL> (update with actual email)

---

**Last Updated:** January 2025
**Version:** 1.0
**Maintained by:** Development Team

// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Chinese (`zh`).
class AppLocalizationsZh extends AppLocalizations {
  AppLocalizationsZh([String locale = 'zh']) : super(locale);

  @override
  String get onboarding_welcome_headline => 'Welcome to Brainy Bunny';

  @override
  String get onboarding_welcome_subheading =>
      'Educational matching games for little learners';

  @override
  String get onboarding_welcome_cta => 'Let\'s begin';

  @override
  String get onboarding_name_headline => 'Let\'s personalize your experience';

  @override
  String get onboarding_name_hint => 'Your name';

  @override
  String get onboarding_name_mom => 'Mom';

  @override
  String get onboarding_name_dad => 'Dad';

  @override
  String get onboarding_name_parent => 'Parent';

  @override
  String onboarding_name_greeting(String name) {
    return 'Great, $name!';
  }

  @override
  String get onboarding_child_age_headline => 'How old is your little one?';

  @override
  String get onboarding_child_age_subtext =>
      'All games work for ages 2-5. This helps us provide age-appropriate tips.';

  @override
  String get onboarding_age_2 => '2 years old';

  @override
  String get onboarding_age_3 => '3 years old';

  @override
  String get onboarding_age_4 => '4 years old';

  @override
  String get onboarding_age_5_plus => '5+ years old';

  @override
  String get onboarding_philosophy_headline =>
      'Transform screen time into learning time';

  @override
  String get onboarding_philosophy_aap =>
      'Aligned with AAP screen time recommendations';

  @override
  String get onboarding_philosophy_learning =>
      'Turn screen time into meaningful learning experiences';

  @override
  String get onboarding_philosophy_skills =>
      'Develop cognitive skills through play-based learning';

  @override
  String get onboarding_transition_to_games =>
      'Let\'s play! Rotate your device →';

  @override
  String get onboarding_transition_from_games =>
      'Great learning! Let\'s finish up →';

  @override
  String get onboarding_solution_headline => 'Learning through play';

  @override
  String get onboarding_solution_description =>
      'While they match shapes and colors, they\'re building real cognitive skills.';

  @override
  String get onboarding_solution_benefit_engagement => 'Active engagement';

  @override
  String get onboarding_solution_benefit_pattern_recognition =>
      'Pattern recognition';

  @override
  String get onboarding_solution_benefit_cause_effect =>
      'Cause-and-effect thinking';

  @override
  String get onboarding_solution_benefit_screen_time =>
      'Aligned with 1-hour screen time guidelines';

  @override
  String get onboarding_solution_research_text =>
      'Research shows: Matching activities improve spatial reasoning...';

  @override
  String get onboarding_solution_research_source =>
      'Based on developmental psychology studies';

  @override
  String get onboarding_solution_cta => 'See how it works';

  @override
  String get onboarding_problem_headline =>
      'We understand your\nscreen time concerns';

  @override
  String get onboarding_problem_point_1_title => 'Passive video consumption';

  @override
  String get onboarding_problem_point_1_description =>
      'Hours of mindless watching with zero interaction or learning';

  @override
  String get onboarding_problem_point_1_statistic =>
      '6x higher risk of language delays';

  @override
  String get onboarding_problem_point_2_title => 'Brain development damage';

  @override
  String get onboarding_problem_point_2_description =>
      'Excessive screen time alters white matter structure in developing brains';

  @override
  String get onboarding_problem_point_2_statistic =>
      'Reduced cognitive abilities';

  @override
  String get onboarding_problem_point_3_title =>
      'Inappropriate content exposure';

  @override
  String get onboarding_problem_point_3_description =>
      'Ads, violence, and age-inappropriate material in \'kids\' content';

  @override
  String get onboarding_problem_point_3_statistic =>
      '85% of kids\' apps contain ads';

  @override
  String get onboarding_problem_point_4_title => 'Attention & focus problems';

  @override
  String get onboarding_problem_point_4_description =>
      'Fast-paced content destroys ability to concentrate and learn';

  @override
  String get onboarding_problem_point_4_statistic =>
      '40% increase in ADHD symptoms';

  @override
  String get onboarding_problem_research_title => 'Scientific Evidence';

  @override
  String get onboarding_problem_research_text =>
      'Peer-reviewed studies confirm: excessive passive screen time causes brain changes, language delays, and attention problems in young children.';

  @override
  String get onboarding_problem_subtext =>
      'You\'re not alone. 89% of parents share these concerns.';

  @override
  String get onboarding_problem_cta => 'There\'s a better way';

  @override
  String get demo_game_1_skill => 'Visual Matching & Shape Recognition';

  @override
  String get demo_game_1_science =>
      'Matching animals to silhouettes develops visual discrimination - the ability to notice differences between similar shapes. This skill is foundational for letter recognition and reading.';

  @override
  String get demo_game_1_citation =>
      'Bornstein (1985) - Visual processing and categorical thinking';

  @override
  String get demo_game_1_badge => 'Shape Detective';

  @override
  String get demo_game_2_skill => 'Visual Memory & Attention';

  @override
  String get demo_game_2_science =>
      'Finding matching pairs strengthens working memory - your child\'s ability to hold and manipulate information. This directly supports math problem-solving and following multi-step instructions.';

  @override
  String get demo_game_2_citation =>
      'Gopnik & Meltzoff (1987) - Categorization and cognitive flexibility';

  @override
  String get demo_game_2_badge => 'Memory Master';

  @override
  String get demo_game_3_skill => 'Logical Association & Categorization';

  @override
  String get demo_game_3_science =>
      'Connecting objects to their users teaches categorization and logical thinking. Your child learns that things belong together for reasons - a key step in understanding cause and effect.';

  @override
  String get demo_game_3_citation =>
      'Piaget (1952) - Pre-operational cognitive development';

  @override
  String get demo_game_3_badge => 'Logic Star';

  @override
  String get demo_game_4_skill => 'Pattern Recognition & Matching';

  @override
  String get demo_game_4_science =>
      'Matching patterns builds pattern recognition - the ability to see relationships between things. This skill strongly predicts math success and helps children understand \'same\' vs \'different.\'';

  @override
  String get demo_game_4_citation =>
      'Rittle-Johnson et al. (2019) - Pattern skills and mathematics';

  @override
  String get demo_game_4_badge => 'Pattern Pro';

  @override
  String get demo_game_5_skill => 'Symbolic Thinking & Real-World Connections';

  @override
  String get demo_game_5_science =>
      'Connecting tools to careers builds symbolic thinking - understanding that one thing can represent another. This abstract thinking is essential for language, math, and imagination.';

  @override
  String get demo_game_5_citation =>
      'Vygotsky (1978) - Symbolic representation in cognitive development';

  @override
  String get demo_game_5_badge => 'World Explorer';

  @override
  String get demo_game_next => 'Next Game';

  @override
  String onboarding_summary_headline(String name) {
    return 'Your Journey So Far, $name';
  }

  @override
  String onboarding_summary_age(int age) {
    return 'Perfect for $age-year-olds';
  }

  @override
  String get onboarding_summary_skills => '5 skills practiced';

  @override
  String get onboarding_summary_screen_time =>
      'Healthy screen time approach aligned with AAP guidelines';

  @override
  String get onboarding_summary_cta => 'See Your Personalized Plan';

  @override
  String get trust_headline => 'Trusted by Parents Worldwide';

  @override
  String get trust_approved => 'AAP Approved';

  @override
  String get trust_cta => 'Unlock Full Learning Experience';

  @override
  String get paywall_headline => 'Unlock All 15 Learning Games';

  @override
  String get paywall_subheadline => 'Continue your child\'s learning journey';

  @override
  String get paywall_feature_games =>
      '15 educational games targeting key skills';

  @override
  String get paywall_feature_progress => 'Progress tracking for your child';

  @override
  String get paywall_feature_ad_free => 'Ad-free, safe environment';

  @override
  String get paywall_feature_new_games => 'New games added monthly';

  @override
  String get paywall_feature_ages => 'Designed for ages 2-5';

  @override
  String get paywall_trial_headline => 'Try All Features FREE for 7 Days';

  @override
  String get paywall_trial_price => 'Then just €0.87/week';

  @override
  String get paywall_trial_today => 'Today: Full Access Unlocked';

  @override
  String get paywall_trial_day5 => 'Day 5: We\'ll send a reminder';

  @override
  String get paywall_trial_day7 =>
      'Day 7: Billing starts (cancel anytime before)';

  @override
  String get paywall_trial_no_payment => '✓ No Payment Due Now';

  @override
  String get paywall_trial_cta => 'Start Free Trial →';

  @override
  String get paywall_weekly => 'Weekly';

  @override
  String get paywall_monthly => 'Monthly';

  @override
  String get paywall_yearly => 'Yearly';

  @override
  String get paywall_save_60 => 'Save 60%';

  @override
  String get paywall_most_popular => 'Most popular choice';

  @override
  String get paywall_cancel_anytime =>
      'Cancel anytime from your device settings';

  @override
  String get paywall_restore => 'Restore Purchases';

  @override
  String get subscription_premium => 'Premium';

  @override
  String get subscription_trial => '7-Day Trial';

  @override
  String get subscription_lifetime => 'Lifetime Access';

  @override
  String get subscription_expired => 'Subscription Ended';

  @override
  String get subscription_manage => 'Manage Subscription';

  @override
  String get subscription_cancel => 'Cancel Subscription';

  @override
  String get subscription_change_plan => 'Change Plan';

  @override
  String get continue_button => 'Continue';

  @override
  String get skip_button => 'Skip';

  @override
  String get close_button => 'Close';

  @override
  String get loading => 'Loading...';

  @override
  String get error_purchase_failed =>
      'Purchase failed. If you already own this, try restarting the app.';

  @override
  String get error_purchase_failed_message =>
      'We couldn\'t complete your purchase. Please try again.';

  @override
  String get error_restore_failed => 'No purchases found';

  @override
  String get error_restore_failed_message =>
      'We couldn\'t find any previous purchases. If you believe this is an error, please contact support.';

  @override
  String get error_network => 'Network error';

  @override
  String get error_network_message =>
      'Please check your internet connection and try again.';

  @override
  String get summary_headline => 'Amazing Progress!';

  @override
  String summary_message_age_2(Object name) {
    return 'Great job, $name! Your little one is building important skills through play. At age 2, every match they make strengthens their visual recognition and problem-solving abilities.';
  }

  @override
  String summary_message_age_3(Object name) {
    return 'Wonderful, $name! Your child is developing crucial cognitive skills. At age 3, these activities enhance their memory, focus, and logical thinking.';
  }

  @override
  String summary_message_age_4(Object name) {
    return 'Excellent work, $name! Your 4-year-old is mastering advanced problem-solving. These games are preparing them for kindergarten success.';
  }

  @override
  String summary_message_age_5_plus(Object name) {
    return 'Fantastic, $name! Your child is excelling at complex thinking. These skills will give them a strong foundation for school.';
  }

  @override
  String get summary_badges_earned => 'Badges Earned';

  @override
  String get summary_badges => 'Badges';

  @override
  String get summary_games => 'Games';

  @override
  String get summary_skills => 'Skills';

  @override
  String get trust_aap_description =>
      'Our educational approach aligns with AAP guidelines for healthy screen time and early childhood development.';

  @override
  String get trust_research_title => 'Research-Backed Curriculum';

  @override
  String get trust_research_description =>
      'Every game is designed based on peer-reviewed studies in cognitive development, featuring methods proven to enhance learning in children ages 2-5.';

  @override
  String get trust_testimonial_1_name => 'Sarah M., Mother of 3-year-old';

  @override
  String get trust_testimonial_1_quote =>
      '\"My daughter went from struggling with shapes to confidently identifying them everywhere. The progress in just 2 weeks amazed me!\"';

  @override
  String get trust_testimonial_2_name => 'Michael T., Father of 4-year-old';

  @override
  String get trust_testimonial_2_quote =>
      '\"Finally, screen time I feel good about! My son is learning while having fun, and I can see real cognitive improvements.\"';

  @override
  String get trust_downloads_title => 'Join 100,000+ Families';

  @override
  String get trust_downloads_description =>
      'Trusted by parents in over 50 countries to give their children a head start in learning.';

  @override
  String get trust_cta_headline =>
      'Ready to unlock your child\'s full potential?';

  @override
  String get trust_cta_button => 'Start Free Trial';

  @override
  String get paywall_premium_badge => 'Premium Access';

  @override
  String get paywall_step1_headline => 'Unlock Your Child\'s Full Potential';

  @override
  String get paywall_value_1_title => '15 Premium Games';

  @override
  String get paywall_value_1_description =>
      'Access all educational games designed for ages 2-5, covering shapes, colors, numbers, logic, and more';

  @override
  String get paywall_value_2_title => 'Track Progress';

  @override
  String get paywall_value_2_description =>
      'Detailed analytics showing your child\'s development and skill improvement over time';

  @override
  String get paywall_value_3_title => 'Personalized Learning';

  @override
  String get paywall_value_3_description =>
      'Games adapt to your child\'s age and skill level for optimal learning';

  @override
  String get paywall_value_4_title => 'New Content Monthly';

  @override
  String get paywall_value_4_description =>
      'Regular updates with new games and activities to keep learning fresh and engaging';

  @override
  String get paywall_step1_cta => 'See Plans';

  @override
  String get paywall_secure_payment => 'Secure payment processing';

  @override
  String get paywall_trial_badge => '7-Day Free Trial';

  @override
  String get paywall_step2_headline => 'Choose Your Plan';

  @override
  String get paywall_step2_subheadline =>
      'Start your 7-day free trial. Cancel anytime.';

  @override
  String get paywall_plan_best_value => 'Best Value';

  @override
  String get paywall_plan_yearly_title => 'Yearly';

  @override
  String get paywall_plan_yearly_period => '/year';

  @override
  String get paywall_plan_yearly_per_month => 'Just €3.33/month';

  @override
  String get paywall_plan_yearly_savings => 'Save 63% compared to monthly';

  @override
  String get paywall_plan_monthly_title => 'Monthly';

  @override
  String get paywall_plan_monthly_period => '/month';

  @override
  String get paywall_plan_weekly_title => 'Weekly';

  @override
  String get paywall_plan_weekly_period => '/week';

  @override
  String get paywall_plan_weekly_note => 'For short-term access';

  @override
  String get paywall_trial_reminder =>
      'Your free trial starts today. You won\'t be charged until day 8. Cancel anytime before then with no cost.';

  @override
  String get paywall_step2_cta => 'Continue';

  @override
  String get paywall_terms =>
      'By continuing, you agree to our Terms of Service and Privacy Policy';

  @override
  String get paywall_urgency_text => 'Limited Time Offer';

  @override
  String get paywall_step3_headline => 'You\'re One Step Away!';

  @override
  String get paywall_step3_included_title => 'Everything You Get:';

  @override
  String get paywall_included_1 => 'All 15 premium educational games';

  @override
  String get paywall_included_2 => 'Personalized learning paths for your child';

  @override
  String get paywall_included_3 => 'Detailed progress tracking and insights';

  @override
  String get paywall_included_4 => 'Monthly new content and activities';

  @override
  String get paywall_included_5 => 'Ad-free experience for focused learning';

  @override
  String get paywall_included_6 => 'Offline mode - learn anywhere, anytime';

  @override
  String get paywall_guarantee_title => '100% Risk-Free Guarantee';

  @override
  String get paywall_guarantee_text =>
      'Try it free for 7 days. If you\'re not completely satisfied, cancel before the trial ends and pay nothing. No questions asked.';

  @override
  String get paywall_step3_cta => 'Start My Free Trial';

  @override
  String get pre_paywall_headline => 'Your Learning Journey is Ready!';

  @override
  String pre_paywall_subheadline_personalized(String name) {
    return 'Here\'s what we\'ve prepared for $name:';
  }

  @override
  String pre_paywall_subheadline_age(String age) {
    return 'Here\'s what we\'ve prepared for your $age-year-old:';
  }

  @override
  String get pre_paywall_subheadline_generic =>
      'Here\'s what we\'ve prepared for your child:';

  @override
  String get pre_paywall_card_1_title => 'Age-Appropriate Content';

  @override
  String pre_paywall_card_1_subtitle_age(String age) {
    return 'Perfect for $age years old';
  }

  @override
  String get pre_paywall_card_1_subtitle_generic =>
      'All 15 games work great for ages 2-5';

  @override
  String get pre_paywall_card_2_title => 'Learning Focus';

  @override
  String get pre_paywall_card_2_subtitle => 'Building skills through play';

  @override
  String get pre_paywall_card_3_title => '15 Educational Games';

  @override
  String get pre_paywall_card_3_subtitle =>
      'Shapes, colors, patterns, animals & more';

  @override
  String get pre_paywall_key_benefit =>
      'Transform screen time from guilt into growth—educational content you can feel good about.';

  @override
  String get pre_paywall_trust_1 => 'Teacher Approved';

  @override
  String get pre_paywall_trust_2 => 'Ad-Free';

  @override
  String get pre_paywall_trust_3 => 'Expert-Backed';

  @override
  String get pre_paywall_cta_primary => 'Start 7-Day FREE Trial';

  @override
  String get pre_paywall_cta_primary_subtext =>
      'Unlock all 15 games • No charge today';

  @override
  String get pre_paywall_cta_secondary => 'Continue with 5 free games';

  @override
  String get pre_paywall_important_note =>
      'Trial includes all 15 games. After trial: Keep 5 free games or subscribe for full access.';

  @override
  String get onboarding_rotate_to_landscape =>
      'Rotate your device to landscape';

  @override
  String get onboarding_rotate_to_portrait => 'Rotate your device to portrait';

  @override
  String get demo_game_1_title => 'Animal Shapes';

  @override
  String get demo_game_1_context =>
      'Help your child match animals to their shapes! This builds visual recognition skills essential for reading.';

  @override
  String get demo_game_2_title => 'Memory Match';

  @override
  String get demo_game_2_context =>
      'Find matching pairs to strengthen working memory - crucial for following instructions and problem-solving.';

  @override
  String get demo_game_3_title => 'Logic Puzzles';

  @override
  String get demo_game_3_context =>
      'Solve puzzles to develop logical thinking and pattern recognition skills.';

  @override
  String get demo_game_4_title => 'Pattern Fun';

  @override
  String get demo_game_4_context =>
      'Recognize patterns to build math readiness and sequencing skills.';

  @override
  String get demo_game_5_title => 'World Explorer';

  @override
  String get demo_game_5_context =>
      'Explore the world to expand vocabulary and cultural awareness.';

  @override
  String get demo_game_congratulations => 'Amazing! You earned a badge!';

  @override
  String paywall_benefit_all_games(int gameCount) {
    return 'Access to all $gameCount games';
  }

  @override
  String get paywall_benefit_age_appropriate => 'Age-appropriate content';

  @override
  String get paywall_benefit_progress_tracking => 'Progress tracking';

  @override
  String get paywall_benefit_offline_play => 'Offline play supported';

  @override
  String get paywall_benefit_no_ads => 'No ads, kid-safe';

  @override
  String get paywall_benefit_regular_updates => 'Regular updates';

  @override
  String get paywall_start_trial => 'Start Free Trial';

  @override
  String get paywall_step3_benefit_1 => 'Full access to all learning games';

  @override
  String get paywall_step3_benefit_2 => 'Ad-free, safe learning environment';

  @override
  String get paywall_step3_benefit_3 => 'Perfect for the whole family';

  @override
  String get paywall_subscribe_button => 'Subscribe Now';

  @override
  String get trial_explanation_headline => 'Try All 15 Games Free for 7 Days';

  @override
  String get trial_explanation_feature_1 =>
      'Full access to all 15 educational games';

  @override
  String get trial_explanation_feature_2 => 'No charge for 7 days';

  @override
  String get trial_explanation_feature_3 =>
      'We\'ll remind you 2 days before trial ends';

  @override
  String get trial_explanation_feature_4 =>
      'Cancel anytime during trial - no cost';

  @override
  String get trial_explanation_subtext =>
      'You won\'t be charged until day 8 of your trial';

  @override
  String get trial_explanation_cta => 'See Plans';

  @override
  String get unified_paywall_headline => 'Choose Your Plan';

  @override
  String get unified_paywall_subheadline =>
      'All plans unlock 15 educational games';

  @override
  String get unified_paywall_yearly_badge_save => 'Save 60%';

  @override
  String get unified_paywall_yearly_badge_trial => '7-Day Free Trial';

  @override
  String get unified_paywall_yearly_title => 'Yearly';

  @override
  String unified_paywall_yearly_price_breakdown(String monthlyEquivalent) {
    return 'Just $monthlyEquivalent/month';
  }

  @override
  String get unified_paywall_yearly_savings => 'Save 63% • Try free for 7 days';

  @override
  String get unified_paywall_yearly_feature_1 => 'No charge for 7 days';

  @override
  String get unified_paywall_yearly_feature_2 => 'Cancel anytime during trial';

  @override
  String get unified_paywall_yearly_feature_3 => 'Full access to all 15 games';

  @override
  String get unified_paywall_yearly_button => 'Start Free Trial';

  @override
  String get unified_paywall_monthly_title => 'Monthly';

  @override
  String unified_paywall_monthly_per_week(String weeklyEquivalent) {
    return '$weeklyEquivalent/week';
  }

  @override
  String get unified_paywall_monthly_savings => 'Flexible monthly plan';

  @override
  String get unified_paywall_monthly_feature_1 => 'Cancel anytime';

  @override
  String get unified_paywall_monthly_feature_2 => 'Full access to all 15 games';

  @override
  String get unified_paywall_monthly_button => 'Subscribe Monthly';

  @override
  String get unified_paywall_weekly_title => 'Weekly';

  @override
  String get unified_paywall_weekly_savings => 'Try for just one week';

  @override
  String get unified_paywall_weekly_feature_1 => 'Cancel anytime';

  @override
  String get unified_paywall_weekly_feature_2 => 'Full access to all 15 games';

  @override
  String get unified_paywall_weekly_button => 'Subscribe Weekly';

  @override
  String get unified_paywall_trust_1 => 'Secure payment processing';

  @override
  String get unified_paywall_trust_2 => 'Manage in App/Play Store';

  @override
  String get unified_paywall_trust_3 => 'All plans include full access';

  @override
  String get unified_paywall_restore => 'Restore Purchases';

  @override
  String get unified_paywall_terms =>
      'By continuing, you agree to our Terms of Service and Privacy Policy';

  @override
  String get educational_value_headline =>
      'Learning through play, not mindless entertainment';

  @override
  String get educational_value_point_1_title =>
      'Develops pattern recognition & logical thinking';

  @override
  String get educational_value_point_1_description =>
      'Essential skills for math readiness and problem-solving';

  @override
  String get educational_value_point_2_title =>
      'Strengthens visual discrimination skills';

  @override
  String get educational_value_point_2_description =>
      'Helps children identify differences and similarities';

  @override
  String get educational_value_point_3_title =>
      'Builds problem-solving abilities';

  @override
  String get educational_value_point_3_description =>
      'Active engagement develops critical thinking';

  @override
  String get educational_value_point_4_title =>
      'Designed for healthy screen time';

  @override
  String get educational_value_point_4_description =>
      'Aligned with pediatric recommendation of 1 hour daily';

  @override
  String get educational_value_research =>
      'Research shows: Matching activities improve spatial reasoning and cognitive development in children ages 2-5.';

  @override
  String get educational_value_research_source =>
      'Based on developmental psychology studies';

  @override
  String get value_carousel_1_headline => 'Screen time that builds skills';

  @override
  String get value_carousel_1_description =>
      '15 different matching games teaching shapes, colors, patterns, animals, professions, and cause-and-effect thinking.';

  @override
  String get value_carousel_1_subtext =>
      'Not just entertainment—actual learning in every match.';

  @override
  String get value_carousel_1_benefit_1_title => 'Memory Development';

  @override
  String get value_carousel_1_benefit_1_description =>
      'Matching games that strengthen recall and recognition';

  @override
  String get value_carousel_1_benefit_2_title => 'Problem Solving';

  @override
  String get value_carousel_1_benefit_2_description =>
      'Puzzles that encourage logical thinking and strategy';

  @override
  String get value_carousel_1_benefit_3_title => 'Progressive Learning';

  @override
  String get value_carousel_1_benefit_3_description =>
      'Difficulty adapts as your child masters new skills';

  @override
  String get value_carousel_2_headline => '15 ways to learn and grow';

  @override
  String get value_carousel_2_description =>
      'From simple shape matching to understanding relationships between objects and situations—each game targets specific developmental skills.';

  @override
  String get value_carousel_2_category_1 => 'Shapes & Geometry';

  @override
  String get value_carousel_2_category_2 => 'Colors & Patterns';

  @override
  String get value_carousel_2_category_3 => 'Animals & Nature';

  @override
  String get value_carousel_2_category_4 => 'Professions & Roles';

  @override
  String get value_carousel_2_category_5 => 'Cause & Effect';

  @override
  String get value_carousel_2_category_6 => 'And more...';

  @override
  String get value_carousel_2_feature_1_title => '100% Ad-Free';

  @override
  String get value_carousel_2_feature_1_description =>
      'No advertisements, no distractions, no inappropriate content';

  @override
  String get value_carousel_2_feature_2_title => 'Age-Appropriate';

  @override
  String get value_carousel_2_feature_2_description =>
      'Content designed specifically for ages 2-5';

  @override
  String get value_carousel_2_feature_3_title => 'Active Learning';

  @override
  String get value_carousel_2_feature_3_description =>
      'Engaging activities, not passive watching';

  @override
  String get value_carousel_3_headline => 'Teacher Approved, Parent Trusted';

  @override
  String get value_carousel_3_trust_element_1 => 'Ad-free learning environment';

  @override
  String get value_carousel_3_trust_element_2 => 'No data collection';

  @override
  String get value_carousel_3_trust_element_3 =>
      'Based on child development expert recommendations';

  @override
  String get value_carousel_3_trust_element_4 => 'Safe for young children';

  @override
  String get value_carousel_3_feature_1_title => 'Progress Tracking';

  @override
  String get value_carousel_3_feature_1_description =>
      'See which skills your child is developing';

  @override
  String get value_carousel_3_feature_2_title => 'Achievement Rewards';

  @override
  String get value_carousel_3_feature_2_description =>
      'Earn stars and badges that motivate continued learning';

  @override
  String get value_carousel_3_feature_3_title => 'Personalized Experience';

  @override
  String get value_carousel_3_feature_3_description =>
      'Games that adapt to your child\'s skill level';

  @override
  String get summary_headline_new =>
      'Perfect! Here\'s Your Personalized Learning Path';

  @override
  String get summary_learning_path_title => 'What Your Child Will Learn:';

  @override
  String get summary_skill_cognitive => 'Cognitive Development';

  @override
  String summary_skill_cognitive_desc(int age) {
    return 'Memory games and problem-solving activities perfect for age $age';
  }

  @override
  String get summary_skill_visual => 'Visual Perception';

  @override
  String summary_skill_visual_desc(int age) {
    return 'Shape recognition and spatial awareness games for $age-year-olds';
  }

  @override
  String get summary_skill_exploration => 'Exploration & Discovery';

  @override
  String summary_skill_exploration_desc(int age) {
    return 'Interactive games that encourage curiosity at age $age';
  }

  @override
  String get summary_next_step =>
      'Next: Try Premium free for 7 days to unlock all games!';

  @override
  String get trial_badge => '7-Day Free Trial';

  @override
  String get trial_headline => 'Try Premium Free for 7 Days';

  @override
  String get trial_description =>
      'Get full access to all premium games and features. Cancel anytime during your trial—no charges if you cancel before it ends.';

  @override
  String get trial_feature_1_title => 'All Premium Games';

  @override
  String get trial_feature_1_description =>
      'Access every learning game in our library';

  @override
  String get trial_feature_2_title => 'Ad-Free Experience';

  @override
  String get trial_feature_2_description =>
      'Safe learning environment with no advertisements';

  @override
  String get trial_feature_3_title => 'Progress Tracking';

  @override
  String get trial_feature_3_description =>
      'See your child\'s development and achievements';

  @override
  String get trial_feature_4_title => 'Regular Updates';

  @override
  String get trial_feature_4_description =>
      'New games and features added regularly';

  @override
  String get trial_how_it_works_title => 'How It Works:';

  @override
  String get trial_step_1 => 'Start your free 7-day trial today';

  @override
  String get trial_step_2 => 'Enjoy full access to all premium features';

  @override
  String get trial_step_3 => 'Cancel anytime—no charges before trial ends';

  @override
  String get trial_cta => 'Start My Free Trial';

  @override
  String get trial_disclaimer =>
      'Free for 7 days, then your selected plan rate. Cancel anytime.';

  @override
  String get notification_permission_headline => 'Never miss a learning moment';

  @override
  String get notification_permission_description =>
      'We\'ll send gentle reminders for:';

  @override
  String get notification_benefit_1_title => 'Trial Reminders';

  @override
  String get notification_benefit_1_description =>
      'Get notified before your trial ends so you never lose access';

  @override
  String get notification_benefit_2_title => 'Learning Milestones';

  @override
  String get notification_benefit_2_description =>
      'Celebrate when your child reaches new achievements';

  @override
  String get notification_benefit_3_title => 'Engagement Tips';

  @override
  String get notification_benefit_3_description =>
      'Get suggestions for keeping learning fun and engaging';

  @override
  String get notification_privacy_note =>
      'We respect your privacy. You can disable notifications anytime in settings.';

  @override
  String get notification_enable_button => 'Enable Notifications';

  @override
  String get notification_maybe_later => 'Maybe Later';

  @override
  String get subscription_management_title => 'Manage Subscription';

  @override
  String get subscription_status_active => 'Premium Active';

  @override
  String get subscription_status_active_description =>
      'You have full access to all premium features';

  @override
  String get subscription_status_inactive => 'Free Version';

  @override
  String get subscription_status_inactive_description =>
      'Upgrade to premium for full access to all games';

  @override
  String get subscription_actions_title => 'Actions';

  @override
  String get subscription_restore_title => 'Restore Purchases';

  @override
  String get subscription_restore_description =>
      'Already subscribed on another device? Restore your purchases here.';

  @override
  String get subscription_restore_button => 'Restore';

  @override
  String get subscription_manage_title => 'Manage Your Subscription';

  @override
  String get subscription_manage_description =>
      'View, change, or cancel your subscription through your app store account.';

  @override
  String get subscription_manage_button => 'Open Subscription Settings';

  @override
  String get subscription_help_title => 'Help & Information';

  @override
  String get subscription_cancel_title => 'How to Cancel';

  @override
  String get subscription_cancel_description =>
      'You can cancel your subscription anytime through your App Store or Google Play account settings. You\'ll keep access until the end of your billing period.';

  @override
  String get subscription_payment_failure_title => 'Payment Issues';

  @override
  String get subscription_payment_failure_description =>
      'If your payment fails, update your payment method in your app store account. We\'ll retry the payment automatically.';

  @override
  String get next_button => 'Next';

  @override
  String get back_button => 'Back';

  @override
  String get onboarding_priority_question => 'What\'s most important to you?';

  @override
  String get onboarding_priority_1 => 'Educational screen time';

  @override
  String get onboarding_priority_1_sub => 'Learning while playing';

  @override
  String get onboarding_priority_2 => 'School readiness';

  @override
  String get onboarding_priority_2_sub =>
      'Preparing for preschool/kindergarten';

  @override
  String get onboarding_priority_3 => 'Keeping them engaged';

  @override
  String get onboarding_priority_3_sub => 'Productive, happy screen time';

  @override
  String get onboarding_priority_4 => 'Learning through play';

  @override
  String get onboarding_priority_4_sub => 'Fun that builds skills';

  @override
  String get onboarding_transition_message => 'Thank you for sharing!';

  @override
  String onboarding_transition_submessage(int age) {
    return 'We\'re creating the perfect experience for your $age-year-old...';
  }

  @override
  String summary_result_headline(int age) {
    return 'Perfect for your $age-year old!';
  }

  @override
  String get summary_card_1_title => 'What They\'ll Learn:';

  @override
  String get summary_card_1_point_1 =>
      'Pattern recognition through 15 different games';

  @override
  String get summary_card_1_point_2 => 'Visual discrimination skills';

  @override
  String get summary_card_1_point_3 => 'Logical thinking and problem-solving';

  @override
  String get summary_card_1_point_4 =>
      'Real-world connections (animals, professions, nature)';

  @override
  String get summary_card_2_title => 'Why It Works:';

  @override
  String get summary_card_2_point_1 => 'Active learning beats passive watching';

  @override
  String get summary_card_2_point_2 => 'Immediate feedback keeps them engaged';

  @override
  String get summary_card_2_point_3 => 'Variety prevents boredom';

  @override
  String get summary_card_2_point_4 => 'Aligned with screen time guidelines';

  @override
  String get summary_cta => 'See what\'s included';

  @override
  String get free_trial_headline => 'Try 5 games free, unlock 10 more';

  @override
  String get free_trial_free_section => 'Start with 5 free matching games';

  @override
  String get free_trial_free_point_1 => 'Shapes, colors, and more';

  @override
  String get free_trial_free_point_2 => 'No time limit';

  @override
  String get free_trial_free_point_3 => 'No credit card needed';

  @override
  String get free_trial_premium_section =>
      'Unlock all 15 games with a subscription';

  @override
  String get free_trial_premium_point_1 => 'Full game library';

  @override
  String get free_trial_premium_point_2 => 'New learning categories';

  @override
  String get free_trial_premium_point_3 => 'Cancel anytime';

  @override
  String get free_trial_bottom_message =>
      'Try the free games first—unlock more anytime!';

  @override
  String get free_trial_cta_primary => 'Start with free games';

  @override
  String get free_trial_cta_secondary => 'See subscription options';

  @override
  String get paywall_section1_headline =>
      'Ready to unlock all 15 learning games?';

  @override
  String get paywall_section1_feature_1 => '15 educational matching games';

  @override
  String get paywall_section1_feature_2 =>
      'Shape, color, pattern & logic games';

  @override
  String get paywall_section1_feature_3 =>
      'Animals, professions, nature themes';

  @override
  String get paywall_section1_feature_4 => 'Cause-and-effect learning';

  @override
  String get paywall_section1_feature_5 => 'No ads, no distractions';

  @override
  String get paywall_section1_feature_6 => 'Teacher Approved';

  @override
  String get paywall_section2_badge => 'Teacher Approved';

  @override
  String get paywall_section2_text =>
      'Educators recognize Brainy Bunny for its developmental approach to early learning.';

  @override
  String get paywall_section3_weekly_title => 'Weekly';

  @override
  String get paywall_section3_weekly_subtext => 'Try it out';

  @override
  String get paywall_section3_weekly_feature => 'Cancel anytime';

  @override
  String get paywall_section3_weekly_button => 'Subscribe';

  @override
  String get paywall_section3_yearly_title => 'Yearly';

  @override
  String get paywall_section3_yearly_badge => 'BEST VALUE - Save 60%';

  @override
  String get paywall_section3_yearly_highlight => '7-day FREE trial';

  @override
  String paywall_section3_yearly_breakdown(String monthlyEquivalent) {
    return 'Just $monthlyEquivalent/month';
  }

  @override
  String paywall_section3_yearly_fine_print(String yearlyPrice) {
    return 'Then $yearlyPrice annually';
  }

  @override
  String get paywall_section3_yearly_feature =>
      'Cancel during trial - no charge';

  @override
  String get paywall_section3_yearly_button => 'Start FREE trial';

  @override
  String get paywall_section3_monthly_title => 'Monthly';

  @override
  String get paywall_section3_monthly_subtext => 'Flexible option';

  @override
  String get paywall_section3_monthly_feature => 'Cancel anytime';

  @override
  String get paywall_section3_monthly_button => 'Subscribe';

  @override
  String get paywall_trust_element_1 => 'Secure payment';

  @override
  String get paywall_trust_element_2 => 'Cancel anytime during trial';

  @override
  String get paywall_trust_element_3 =>
      'Manage subscription in App Store/Play Store';

  @override
  String get paywall_trust_element_4 =>
      'Charged only after trial ends (for yearly)';

  @override
  String get paywall_disclaimer =>
      'You won\'t be charged during your 7-day trial. Cancel anytime in your device settings.';

  @override
  String get paywall_continue_free_link => 'Continue with free games';

  @override
  String get parent_gate_title => 'Parent Verification Required';

  @override
  String get parent_gate_instruction =>
      'This purchase requires an adult. Please solve this problem:';

  @override
  String get parent_gate_input_placeholder => 'Enter answer';

  @override
  String get parent_gate_cancel => 'Cancel';

  @override
  String get parent_gate_verify => 'Verify';

  @override
  String get parent_gate_error => 'Incorrect answer. Please try again.';

  @override
  String get notification_type_1 => 'New game unlocks';

  @override
  String get notification_type_2 => 'Learning streak milestones';

  @override
  String get notification_type_3 => 'Trial ending reminder (if applicable)';

  @override
  String get notification_type_4 => 'Daily learning encouragement';

  @override
  String get notification_trial_callout =>
      'We\'ll remind you 2 days before your trial ends, so you\'re never surprised.';

  @override
  String get notification_benefit_1 => 'Stay consistent with learning';

  @override
  String get notification_benefit_2 => 'Never miss trial deadline';

  @override
  String get notification_benefit_3 => 'Celebrate progress together';

  @override
  String get notification_cta_enable => 'Enable notifications';

  @override
  String get notification_cta_skip => 'Not now';

  @override
  String get subscription_error_loading_title => 'Loading Subscriptions';

  @override
  String get subscription_error_loading_description =>
      'Please wait while we load subscription options...';

  @override
  String get subscription_error_offline_title => 'No Internet Connection';

  @override
  String get subscription_error_offline_description =>
      'Please check your internet connection and try again. You need to be online to subscribe.';

  @override
  String get subscription_error_not_available_title =>
      'Subscriptions Not Available';

  @override
  String get subscription_error_not_available_description =>
      'In-app purchases are not available on this device. Please try again later or contact support.';

  @override
  String get subscription_error_products_not_found_title =>
      'Products Not Available';

  @override
  String get subscription_error_products_not_found_description =>
      'We couldn\'t load subscription products from the store. Please try again later.';

  @override
  String get subscription_error_unknown_title => 'Something Went Wrong';

  @override
  String get subscription_error_unknown_description =>
      'An unexpected error occurred. Please try again.';

  @override
  String get subscription_error_retry => 'Try Again';

  @override
  String get subscription_error_continue_free => 'Continue with Free Games';

  @override
  String get subscription_loading => 'Loading...';

  @override
  String get goal_preschool_title => 'Prepare for preschool/kindergarten';

  @override
  String get goal_preschool_description => 'Building readiness skills';

  @override
  String get goal_cognitive_title => 'Develop cognitive abilities';

  @override
  String get goal_cognitive_description =>
      'Pattern recognition & problem-solving';

  @override
  String get goal_replace_screen_time_title => 'Replace passive screen time';

  @override
  String get goal_replace_screen_time_description =>
      'Active learning instead of videos';

  @override
  String get goal_keep_engaged_title => 'Keep them engaged & learning';

  @override
  String get goal_keep_engaged_description => 'Fun that actually builds skills';

  @override
  String get summary_age2_headline => 'Perfect for your 2-year-old explorer';

  @override
  String get summary_age2_card1_title => 'What they\'ll learn';

  @override
  String get summary_age2_card1_point1 =>
      'Basic shape recognition (circles, squares, triangles)';

  @override
  String get summary_age2_card1_point2 => 'Simple color matching';

  @override
  String get summary_age2_card1_point3 =>
      'Hand-eye coordination through drag-and-drop';

  @override
  String get summary_age2_card1_point4 => 'Cause and effect understanding';

  @override
  String get summary_age2_card2_title => 'Why it works for age 2';

  @override
  String get summary_age2_card2_point1 =>
      'Extra-large pieces perfect for tiny fingers';

  @override
  String get summary_age2_card2_point2 => 'Simple 1-2 pair matching to start';

  @override
  String get summary_age2_card2_point3 =>
      'Instant positive feedback builds confidence';

  @override
  String get summary_age2_card2_point4 =>
      'Sessions designed for 5-10 minute attention spans';

  @override
  String get summary_age3_headline => 'Designed for your curious 3-year-old';

  @override
  String get summary_age3_card1_title => 'What they\'ll learn';

  @override
  String get summary_age3_card1_point1 =>
      'Advanced shape recognition and sorting';

  @override
  String get summary_age3_card1_point2 => 'Pattern identification';

  @override
  String get summary_age3_card1_point3 => 'Color mixing and matching concepts';

  @override
  String get summary_age3_card1_point4 => 'Early problem-solving skills';

  @override
  String get summary_age3_card2_title => 'Why it works for age 3';

  @override
  String get summary_age3_card2_point1 =>
      'Progressive difficulty grows with their skills';

  @override
  String get summary_age3_card2_point2 =>
      'Builds on preschool learning concepts';

  @override
  String get summary_age3_card2_point3 =>
      'Celebrates small wins to boost motivation';

  @override
  String get summary_age3_card2_point4 => 'Perfect for emerging independence';

  @override
  String get summary_age4_headline => 'Tailored for your smart 4-year-old';

  @override
  String get summary_age4_card1_title => 'What they\'ll learn';

  @override
  String get summary_age4_card1_point1 => 'Complex pattern recognition';

  @override
  String get summary_age4_card1_point2 =>
      'Categorical thinking (animals, professions, objects)';

  @override
  String get summary_age4_card1_point3 => 'Spatial reasoning and relationships';

  @override
  String get summary_age4_card1_point4 =>
      'Pre-reading visual discrimination skills';

  @override
  String get summary_age4_card2_title => 'Why it works for age 4';

  @override
  String get summary_age4_card2_point1 =>
      'Challenges that match pre-K curriculum';

  @override
  String get summary_age4_card2_point2 =>
      'Multiple rounds build sustained focus';

  @override
  String get summary_age4_card2_point3 =>
      'Vocabulary expansion through themed games';

  @override
  String get summary_age4_card2_point4 => 'Prepares for kindergarten readiness';

  @override
  String get summary_age5_headline => 'Engaging games for your 5+ year-old';

  @override
  String get summary_age5_card1_title => 'What they\'ll learn';

  @override
  String get summary_age5_card1_point1 => 'Advanced categorization and sorting';

  @override
  String get summary_age5_card1_point2 => 'Abstract pattern completion';

  @override
  String get summary_age5_card1_point3 => 'Critical thinking and strategy';

  @override
  String get summary_age5_card1_point4 => 'Visual memory enhancement';

  @override
  String get summary_age5_card2_title => 'Why it works for age 5+';

  @override
  String get summary_age5_card2_point1 =>
      'Kindergarten-level cognitive challenges';

  @override
  String get summary_age5_card2_point2 =>
      'Builds confidence for school success';

  @override
  String get summary_age5_card2_point3 =>
      'Reinforces classroom learning at home';

  @override
  String get summary_age5_card2_point4 => 'Keeps advanced learners engaged';

  @override
  String get parental_gate_overlay_title => 'Parental Verification';

  @override
  String get parental_gate_overlay_instruction =>
      'Please solve this problem to continue:';

  @override
  String get error_purchase_verification_failed =>
      'Purchase verification failed. Please restart the app.';

  @override
  String get paywall_step2_badge_save => 'Save 60%';

  @override
  String get paywall_step2_badge_trial => '7-Day Trial';

  @override
  String get paywall_step2_yearly_title => 'Yearly';

  @override
  String paywall_step2_yearly_per_month(String price) {
    return 'Just $price/month';
  }

  @override
  String get paywall_step2_yearly_savings => 'Save 63% • Try free for 7 days';

  @override
  String get paywall_step2_yearly_feature1 => 'No charge for 7 days';

  @override
  String get paywall_step2_yearly_feature2 => 'Cancel anytime during trial';

  @override
  String get paywall_step2_yearly_feature3 =>
      'We\'ll remind you 2 days before trial ends';

  @override
  String get paywall_step2_yearly_feature4 => 'Full access to all 15 games';

  @override
  String get paywall_step2_yearly_button => 'Start FREE Trial';

  @override
  String get paywall_step2_monthly_title => 'Monthly';

  @override
  String paywall_step2_monthly_per_week(String price) {
    return '$price/week';
  }

  @override
  String get paywall_step2_monthly_savings => 'Flexible monthly plan';

  @override
  String get paywall_step2_monthly_feature1 => 'Cancel anytime';

  @override
  String get paywall_step2_monthly_feature2 => 'Full access to all 15 games';

  @override
  String get paywall_step2_monthly_button => 'Subscribe Monthly';

  @override
  String get paywall_step2_weekly_title => 'Weekly';

  @override
  String get paywall_step2_weekly_savings => 'Try for just one week';

  @override
  String get paywall_step2_weekly_feature1 => 'Cancel anytime';

  @override
  String get paywall_step2_weekly_feature2 => 'Full access to all 15 games';

  @override
  String get paywall_step2_weekly_button => 'Subscribe Weekly';

  @override
  String locked_game_headline_personalized(String childName) {
    return 'Unlock All Games for $childName!';
  }

  @override
  String get locked_game_headline_generic => 'Unlock All 15 Educational Games!';

  @override
  String locked_game_card1_title_age(int age) {
    return 'Perfect for Your $age-Year-Old';
  }

  @override
  String get locked_game_card1_title_generic => 'Age-Appropriate Learning';

  @override
  String get locked_game_card2_title => 'Building Cognitive Abilities';

  @override
  String locked_game_card3_title(int count) {
    return '$count More Games to Explore';
  }

  @override
  String get locked_game_card3_subtitle =>
      'Unlock the full collection of educational games';

  @override
  String get locked_game_age_skill_1_generic => 'Shape and color recognition';

  @override
  String get locked_game_age_skill_2_generic => 'Problem-solving abilities';

  @override
  String get locked_game_age_skill_3_generic => 'Hand-eye coordination';

  @override
  String get locked_game_age_skill_4_generic => 'Memory and focus';

  @override
  String get locked_game_age_skill_1_age2 => 'Basic shape recognition';

  @override
  String get locked_game_age_skill_2_age2 => 'Simple color matching';

  @override
  String get locked_game_age_skill_3_age2 => 'Hand-eye coordination';

  @override
  String get locked_game_age_skill_4_age2 => 'Cause and effect understanding';

  @override
  String get locked_game_age_skill_1_age3 => 'Advanced shape sorting';

  @override
  String get locked_game_age_skill_2_age3 => 'Pattern identification';

  @override
  String get locked_game_age_skill_3_age3 => 'Color mixing concepts';

  @override
  String get locked_game_age_skill_4_age3 => 'Early problem-solving';

  @override
  String get locked_game_age_skill_1_age4 => 'Complex pattern recognition';

  @override
  String get locked_game_age_skill_2_age4 => 'Categorical thinking';

  @override
  String get locked_game_age_skill_3_age4 => 'Spatial reasoning';

  @override
  String get locked_game_age_skill_4_age4 => 'Pre-reading visual skills';

  @override
  String get locked_game_age_skill_1_age5 => 'Advanced categorization';

  @override
  String get locked_game_age_skill_2_age5 => 'Abstract pattern completion';

  @override
  String get locked_game_age_skill_3_age5 => 'Critical thinking';

  @override
  String get locked_game_age_skill_4_age5 => 'Visual memory enhancement';

  @override
  String get locked_game_card2_content_default =>
      'Develop essential cognitive skills through play';

  @override
  String get locked_game_card2_content_school =>
      'Build skills for preschool and kindergarten success';

  @override
  String get locked_game_card2_content_cognitive =>
      'Enhance memory, focus, and problem-solving abilities';

  @override
  String get locked_game_card2_content_screentime =>
      'Quality educational content that parents can feel good about';

  @override
  String get locked_game_card2_content_engagement =>
      'Keep your child engaged with fun, educational activities';
}

/// The translations for Chinese, using the Han script (`zh_Hans`).
class AppLocalizationsZhHans extends AppLocalizationsZh {
  AppLocalizationsZhHans() : super('zh_Hans');
}

/// The translations for Chinese, using the Han script (`zh_Hant`).
class AppLocalizationsZhHant extends AppLocalizationsZh {
  AppLocalizationsZhHant() : super('zh_Hant');
}
